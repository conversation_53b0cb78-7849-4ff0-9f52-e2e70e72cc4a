export REGISTRY_PREFIX = eu.gcr.io/mouldlive/testriglive/arm64
export REGISTRY_PATH = dev/

TARGETS =  testrig-python3
TARGETS += testrig-go-reader
TARGETS += testrig-api
TARGETS += testrig-service
TARGETS += testrig-ui

.PHONY: all $(TARGETS) push

all: $(TARGETS)

register:
	docker run --rm --privileged multiarch/qemu-user-static:register

login-gcloud-registry:
	gcloud auth print-access-token | docker login -u oauth2accesstoken --password-stdin https://eu.gcr.io

$(TARGETS):
	$(MAKE) -C ./$@

push-production: 
	$(eval export REGISTRY_PATH = prod/)
	for i in $(TARGETS) ; do \
		$(MAKE) -C $$i push-production; \
	done

push-development: 
	$(eval export REGISTRY_PATH = dev/)
	for i in $(TARGETS) ; do \
		$(MAKE) -C $$i push-development; \
	done

clean:
	for i in $(TARGETS) ; do \
		$(MAKE) -C $$i clean; \
	done
