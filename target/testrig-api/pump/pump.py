import serial
import threading
from time import sleep
from auth.auth import get_current_user
from fastapi import APIRouter, Depends, HTTPException, BackgroundTasks
from typing import Dict
import os
from enum import Enum, auto
import time
import psycopg2
from psycopg2.extras import DictCursor

class PumpStatus(Enum):
    IDLE = 0
    RUNNING = 1
    STOPPED = 2

class DBConnect:
    def __init__(self, host, database, user, password):
        """Initialize the database connection."""
        try:
            self.conn = psycopg2.connect(
                host=host,
                database=database,
                user=user,
                password=password
            )
            self.cursor = self.conn.cursor(cursor_factory=DictCursor)
        except psycopg2.Error as e:
            print(f"Database connection error: {e}")
            self.conn = None
            
    def save_pressure_to_db(self, pressure_value):
        """
        Save the pressure reading to the samples table in the database.
        """
        try:
            if self.conn:
                query = "INSERT INTO pump_data (time, pump_pressure) VALUES (NOW(), %s)"
                self.cursor.execute(query, (pressure_value,))
                self.conn.commit()
                print(f"Pressure {pressure_value} bar saved to database.")
            else:
                print("Database connection is not available.")
        except psycopg2.Error as e:
            print(f"Error saving pressure to database: {e}")
            if self.conn:
                self.conn.rollback()


class GrundfosGenibus:
    def __init__(self, port, db: DBConnect, baudrate=9600, timeout=0):
        self.serial_lock = threading.Lock()
        self.db = db
        try:
            self.serial_port = serial.Serial(port, baudrate=baudrate, timeout=timeout, write_timeout=0)
            if self.serial_port.is_open:
                print(f"Connected to {port} at {baudrate} baud.")
                self.serial_port.reset_input_buffer()
                self.serial_port.reset_output_buffer()
        except serial.SerialException as e:
            print(f"Error opening serial port {port}: {e}")
            self.serial_port = None
            return
        self.serial_port.bytesize  = serial.EIGHTBITS
        self.serial_port.parity = serial.PARITY_NONE
        self.serial_port.stopbits = serial.STOPBITS_ONE
        self.serial_port.xonxoff = False
        self.serial_port.rtscts = False
        self.serial_port.dsrdtr = False
               
        # Create a stop event for the thread
        self.stop_event = threading.Event()
        
        self.pump_pressure = 0
        self.user_pressure = 0
        self.pump_status = PumpStatus.IDLE
        self.crc_tab = [
              0,   4129,   8258,  12387,  16516,  20645,  24774,  28903,
            33032,  37161,  41290,  45419,  49548,  53677,  57806,  61935,
            4657,    528,  12915,   8786,  21173,  17044,  29431,  25302,
            37689,  33560,  45947,  41818,  54205,  50076,  62463,  58334,
            9314,  13379,   1056,   5121,  25830,  29895,  17572,  21637,
            42346,  46411,  34088,  38153,  58862,  62927,  50604,  54669,
            13907,   9842,   5649,   1584,  30423,  26358,  22165,  18100,
            46939,  42874,  38681,  34616,  63455,  59390,  55197,  51132,
            18628,  22757,  26758,  30887,   2112,   6241,  10242,  14371,
            51660,  55789,  59790,  63919,  35144,  39273,  43274,  47403,
            23285,  19156,  31415,  27286,   6769,   2640,  14899,  10770,
            56317,  52188,  64447,  60318,  39801,  35672,  47931,  43802,
            27814,  31879,  19684,  23749,  11298,  15363,   3168,   7233,
            60846,  64911,  52716,  56781,  44330,  48395,  36200,  40265,
            32407,  28342,  24277,  20212,  15891,  11826,   7761,   3696,
            65439,  61374,  57309,  53244,  48923,  44858,  40793,  36728,
            37256,  33193,  45514,  41451,  53516,  49453,  61774,  57711,
            4224,    161,  12482,   8419,  20484,  16421,  28742,  24679,
            33721,  37784,  41979,  46042,  49981,  54044,  58239,  62302,
            689,   4752,   8947,  13010,  16949,  21012,  25207,  29270,
            46570,  42443,  38312,  34185,  62830,  58703,  54572,  50445,
            13538,   9411,   5280,   1153,  29798,  25671,  21540,  17413,
            42971,  47098,  34713,  38840,  59231,  63358,  50973,  55100,
            9939,  14066,   1681,   5808,  26199,  30326,  17941,  22068,
            55628,  51565,  63758,  59695,  39368,  35305,  47498,  43435,
            22596,  18533,  30726,  26663,   6336,   2273,  14466,  10403,
            52093,  56156,  60223,  64286,  35833,  39896,  43963,  48026,
            19061,  23124,  27191,  31254,   2801,   6864,  10931,  14994,
            64814,  60687,  56684,  52557,  48554,  44427,  40424,  36297,
            31782,  27655,  23652,  19525,  15522,  11395,   7392,   3265,
            61215,  65342,  53085,  57212,  44955,  49082,  36825,  40952,
            28183,  32310,  20053,  24180,  11923,  16050,   3793,   7920

        ]
        self.crc_accum = 0xFFFF
        
    def send_command(self, command):
        """
        Send command to pump and receive response.
        """
        # Send the command
        with self.serial_lock:    
            self.serial_port.write(bytes(command))
            # Read the response
            response = self.serial_port.read(256)  # Adjust buffer size as needed
        return response

    def set_initialization_parameters(self):
        """
        Send initialization parameters to the pump.
        TX:27,1a,ff,01,04,96,1b,01,aa,00,ac,00,e0,03,d5,00,d8,04,d9,88,d6,03,d7,e8,e6,01,2e,00,89,b4

        """
        # Example TX command (modify based on protocol details)
        tx_command = [
            0x00, 0x27, 0x18, 0xff, 0x01, 0x04, 0x96, 0x1b,
            0x01, 0xaa, 0x00, 0xac, 0x00, 0xe0, 0x03, 0xd5,
            0x00, 0xd8, 0x04, 0xd9, 0x88, 0xd6, 0x03, 0xd7,
            0xe8, 0xe6, 0x01, 0x2e, 0x00
        ]
        
        tx_command[2] = len(tx_command) - 3
        tx_command = self.add_crc(tx_command)
        
        response = self.send_command(tx_command)

        # Check response (RX)
        if response:
            pass
            #print("Response received:", response.hex())
            # Parse the RX response here
        else:
            print("No response from pump")
            
    def pump_discovery(self):
        """
        Send discovery to the pump.
        """
        # Example TX command (modify based on protocol details)
        tx_command = [
            0x00, 0x27, 0x0e, 0xfe, 0x01, 0x00, 0x02, 0x02,
            0x03, 0x04, 0x02, 0x2e, 0x2f, 0x02, 0x02, 0x94,
            0x95, 0xA2, 0xAA, 0x00
        ] 
        response = self.send_command(tx_command)

        # Check response (RX)
        if response:
            pass
            #print("Response received:", response.hex())
            # Parse the RX response here
        else:
            print("No response from pump")
            
    def start_pump(self):
        """
        Start the pump.
        """
        # Example TX command (modify based on protocol details)
        tx_command = [
            0x00, 0x27, 0x00, 0xff, 0x01, 0x03, 0x83, 0x07,
            0x18, 0x06
        ]
        
        tx_command[2] = len(tx_command) - 3
        tx_command = self.add_crc(tx_command)
        
        response = self.send_command(tx_command)

        # Check response (RX)
        if response:
            self.pump_status = PumpStatus.RUNNING
            pass
            #print("Response received:", response.hex())
            # Parse the RX response here
        else:
            print("No response from pump")
            
    def stop_pump(self):
        """
        Stop the pump.
        TX:27,06,ff,01,03,82,07,05,56,2e
        """
        # Example TX command (modify based on protocol details)
        tx_command = [
            0x00, 0x27, 0x00, 0xff, 0x01, 0x03, 0x82, 0x07,
            0x05
        ]
        
        tx_command[2] = len(tx_command) - 3
        tx_command = self.add_crc(tx_command)
        
        response = self.send_command(tx_command)

        # Check response (RX)
        if response:
            self.pump_status = PumpStatus.STOPPED
            pass
            #print("Response received:", response.hex())
            # Parse the RX response here
        else:
            print("No response from pump")
            
    def read_pressure(self):
        """
        Read the pressure.

        """
        # Example TX command (modify based on protocol details)
        tx_command = [
            0x00, 0x27, 0x06, 0xff, 0x01, 0x02, 0x02, 0x3c,
            0xc3
        ]
        
        tx_command[2] = len(tx_command) - 3
        tx_command = self.add_crc(tx_command)
        
        response = self.send_command(tx_command)

        # Check response (RX)
        if response:
            #print("Response received:", response.hex())
            return response.hex()
            # Parse the RX response here
        else:
            print("No response from pump")
            
   
    def read_command(self, input_data=None):
        """
        Reads a command from a serial port or processes the provided input_data.
        
        :param input_data: Optional bytes-like object for testing (e.g., b'\x24\x06\x01\x00\x02\x02\x00\x02\x49\xFE').
        :return: Parsed command as a bytearray.
        """
        cmd = bytearray()
        
        # If input_data is provided as a string, convert it to bytes
        if isinstance(input_data, str):
            try:
                input_data = bytes.fromhex(input_data)  # Convert hex string to bytes
            except ValueError:
                print("Invalid hexadecimal string input.")
                return cmd

        # Use input_data if provided, otherwise read from the serial port
        if input_data is not None:
            data_source = input_data
        else:
            print("No Connection or input data")
            return cmd

        cmd_found = False
        size_found = 256

        while True:
            # Read one byte from the data source
            if isinstance(data_source, (bytes, bytearray)):  # If input_data is provided
                if len(data_source) == 0:
                    break
                byte = data_source[:1]
                data_source = data_source[1:]

            if not byte:
                break  # Exit if no data is available

            c = byte[0]  # Get the integer value of the byte

            if not cmd_found:
                if c in (0x24, 0x26, 0x27):  # Start of frame
                    cmd_found = True
            else:
                if size_found > 0:
                    size_found -= 1
                if size_found == 255:  # First size byte
                    size_found = c + 2  # Include CRC
                elif size_found == 0:
                    break

            if cmd_found:
                cmd.append(c)

        return cmd

    def pressure_reader(self):
        """Continuously read and display pressure until stopped."""
        while not self.stop_event.is_set():#self.stop_thread:
            response = self.read_pressure()
            #print('Running pressure reader')
            read = self.read_command(response)
            #print('Read value', read)
            if len(read) > 1:
                if 0x06 == read[1]:
                    pressure_byte = read[6]  # Extracting the value at PRESSURE_BYTE index
                    # Assuming pressureHigh and pressureLow are defined
                    pressure_range = 16 - 0
                    read_pressure = (pressure_byte * (pressure_range / 254.0)) + 0

                    # If you need the result as a float (similar to double in C++)
                    read_pressure = float(read_pressure)
                    self.pump_pressure = read_pressure
                    self.db.save_pressure_to_db(self.pump_pressure)
                    
                    sleep(0.05)
                    response = self.set_pressure(self.user_pressure)
                    #print('Pressure_Read (bar)',read_pressure)
                    
                    #Implement logic to set pump ready status
            sleep(0.25)  # Adjust the interval as needed
           
    def start_task(self):
        """Starts the pressure reading task if not already running."""
        if not hasattr(self, 'pressure_thread') or not self.pressure_thread.is_alive():
            print("Starting pressure reading task...")
            #self.stop_thread = False  # Ensure flag is reset
            self.stop_event.clear()
            self.pressure_thread = threading.Thread(target=self.pressure_reader, daemon=True)
            self.pressure_thread.start()
        else:
            print("Pressure reading task is already running.")
                   
    def stop_task(self):
        """Stops the pressure reading task."""
        if hasattr(self, 'pressure_thread') and self.pressure_thread.is_alive():
            print("Stopping pressure reading task...")
            #self.stop_thread = True  # Signal the thread to stop
            self.stop_event.set()
            self.pressure_thread.join(timeout=2)  # Wait for it to stop
        else:
            print("Pressure reading task is not running.")
            
    def monitor_task(self):
        """Monitors and restarts the pressure reading task if it stops."""
        while True:
            if not self.pressure_thread.is_alive() and not self.stop_event.is_set():
                print("Pressure reading task stopped. Restarting...")
                self.start_task()
            time.sleep(5)  # Check every 5 seconds
                
    def set_pressure(self, pressureVal):
        """
        Set the pressure.
        """
        if self.user_pressure == 0:
            return
        
        pressure_Cmd = pressureVal
        error = pressureVal - self.pump_pressure
        
        if error > 0.5:
            pressure_Cmd = self.pump_pressure + 0.5
        
        data = pressure_Cmd
        value = int((254*data)/16)
        maximum = int((254 * 10) /16)

        if value > maximum:
            value = maximum
        elif value < 0:
            value = 0
        
        val = value #& 0xFF
        
        # Example TX command (modify based on protocol details)
        tx_command = [
            0x00, 0x27, 0x0A, 0xff, 0x01, 0x05, 0x82, 0x01,
            val
        ]
        
        tx_command[2] = len(tx_command) - 3
        tx_command = self.add_crc(tx_command)
        
        response = self.send_command(tx_command)

        # Check response (RX)
        if response:
            print("Response received:", response.hex())
            # Parse the RX response here
        else:
            print("No response from pump")
            
    def set_pressure_start_pump(self, pressureVal):
        """
        Set the pressure. 
        """
        pressure_Cmd = pressureVal
        #error = pressureVal - self.pump_pressure
        
        #if error > 0.5:
            #pressure_Cmd = self.pump_pressure + 0.5
        
        data = pressure_Cmd
        value = int((254*data)/16)
        maximum = int((254 * 10) /16)

        if value > maximum:
            value = maximum
        elif value < 0:
            value = 0
        
        val = value #& 0xFF
        
        # Example TX command (modify based on protocol details)
        tx_command = [
            0x00, 0x27, 0x00, 0xff, 0x01, 0x03, 0x83, 0x07,
            0x18, 0x06, 0x05, 0x82, 0x01, val
        ]
               
        tx_command[2] = len(tx_command) - 3
        tx_command = self.add_crc(tx_command)
               
        response = self.send_command(tx_command)

        # Check response (RX)
        if response:
            self.pump_status = PumpStatus.RUNNING
            print("Response received:", response.hex())
            # Parse the RX response here
        else:
            print("No response from pump")
            
    def update_crc(self, data):
        """Updates the CRC accumulator with a new byte of data."""
        self.crc_accum = ((self.crc_accum << 8) ^ self.crc_tab[(self.crc_accum >> 8) ^ data]) & 0xFFFF

    def add_crc(self, cmd):
        """
        Adds the calculated CRC to the cmd list.
        
        Args:
            cmd: List of bytes (integers between 0-255).
        
        Returns:
            The cmd list with appended CRC and trailing 0x00 byte.
        """
        self.crc_accum = 0xFFFF  # Reset accumulator
        for byte in cmd[2:]:  # Start from index 2
            self.update_crc(byte)

        # Calculate low and high CRC bytes
        low_crc = (0xFF & ~self.crc_accum )
        high_crc = (0xFF & (~self.crc_accum >> 8) )

        # Append CRC and trailing byte to command
        #print('High CRC',hex(high_crc))
        #print('Low CRC', hex(low_crc))
        cmd.append(high_crc)
        cmd.append(low_crc)
        cmd.append(0x00)
        return cmd

    def close(self):
        self.serial_port.close()


router = APIRouter()

serial_port = os.getenv("SERIAL_PORT_PUMP", "/dev/ttyUSB1")

# Global DB connection instance
db_instance =  DBConnect(host='postgres', database=os.environ.get('POSTGRES_DB'), user=os.environ.get('POSTGRES_USER'), password=os.environ.get('POSTGRES_PASSWORD'))

genibus = GrundfosGenibus(port=serial_port,db=db_instance)

genibus.serial_port.timeout = 0.5

genibus.pump_discovery() #Pump discovery

sleep(2)

genibus.set_initialization_parameters()  #Initialize the parameter

print('PUMP connected and initialized')

sleep(5)

genibus.stop_pump() # Set initialization state

genibus.start_task() # Start the pressure reading process

genibus.monitor_thread = threading.Thread(target=genibus.monitor_task, daemon=True)
genibus.monitor_thread.start()

@router.on_event("shutdown")
async def shutdown_event():
    try:
        genibus.stop_task()  # Stop the pressure reading thread
        sleep(1)
        genibus.close() # Close the serial port
        #stop_event_pump_auto.clear() # Reset the thread
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to stop background task: {e}")
    
@router.post("/initialize", tags=["PUMP APIs"])
def initialize_pump(current_user: Dict = Depends(get_current_user)):
    """
    Send initialization parameters to the pump.
    """
    try:
        genibus.set_initialization_parameters()
        return {"message": "Pump initialization parameters sent successfully."}
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))
    
@router.post("/start", tags=["PUMP APIs"])
def start_pump(current_user: Dict = Depends(get_current_user)):
    """
    Start the pump.
    """
    try:
        genibus.start_pump()
        return {"message": "Pump started successfully."}
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/stop", tags=["PUMP APIs"])
def stop_pump(current_user: Dict = Depends(get_current_user)):
    """
    Stop the pump.
    """
    try:
        #stop_event_pump_auto.set()
        genibus.user_pressure = 0
        genibus.stop_pump()
        #genibus.close()
        return {"message": "Pump stopped successfully."}
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))
    
@router.get("/read-pressure", tags=["PUMP APIs"])
def read_pressure(current_user: Dict = Depends(get_current_user)):
    """
    Read the current pump pressure.
    """
    try:
        #response = genibus.read_pressure()
        return {"pressure": genibus.pump_pressure}
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))
    
@router.get("/pump-status", tags=["PUMP APIs"])
def read_status(current_user: Dict = Depends(get_current_user)):
    """
    Read the current status of the pump.
    """
    try:
        return {"status": genibus.pump_status}
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/set-pressure/{pressure_val}", tags=["PUMP APIs"])
def set_pressure( pressure_val: float, current_user: Dict = Depends(get_current_user)):
    """
    Set the pressure and start the pump.
    """
    try:
        genibus.user_pressure = pressure_val
        genibus.set_pressure(pressure_val)
        return {"message": f"Pressure set to {pressure_val} bar and pump started successfully."}
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))
    

@router.post("/set-pressure-start-pump/{pressure_val}", tags=["PUMP APIs"])
def set_pressure_start_pump( pressure_val: float, current_user: Dict = Depends(get_current_user)):
    """
    Set the pressure and start the pump.
    """
    try:
        genibus.user_pressure = pressure_val
        genibus.set_pressure_start_pump(pressure_val)
        return {"message": f"Pressure set to {pressure_val} bar and pump started successfully."}
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))




