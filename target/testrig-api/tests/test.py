from fastapi import FastAP<PERSON>, API<PERSON><PERSON><PERSON>, HTTPException, Depends
from auth.auth import get_current_user
import valkey
from typing import Dict
import threading

router = APIRouter()

# Valkey connection settings
VALKEY_HOST = "valkey"
VALKEY_PORT = 6379

test_status = {"state": "idle"}  # Store the last received test status
purge_status = {"state" : "idle"} # Store the last received purge status
clean_status = {"state" : "idle"} # Store the last received clean status
filter_status = {"state" : "idle"} # Store the last received filter status
recycle_status = {"state" : "idle"} # Store the last received recycle status

def get_valkey_connection():
    """Create an Valkey connection"""
    return valkey.Valkey(host=VALKEY_HOST, port=VALKEY_PORT)

def subscribe_to_valkey():
    """Background task to listen for Pub/Sub messages."""
    valkey_client = get_valkey_connection()
    pubsub = valkey_client.pubsub()
    pubsub.subscribe("test_status","purge_status","clean_status", "filter_status", "recycle_status")

    for message in pubsub.listen():
        if message["type"] == "message":
            channel = message["channel"].decode("utf-8")
            data = message["data"].decode("utf-8")

            if channel == "test_status":
                test_status["state"] = data
                print(f"Updated Test Status: {test_status['state']}")
            elif channel == "purge_status":
                purge_status["state"] = data
                print(f"Updated Purge Status: {purge_status['state']}")
            elif channel == "clean_status":
                clean_status["state"] = data
                print(f"Updated Clean Status: {clean_status['state']}")
            elif channel == "filter_status":
                filter_status["state"] = data
                print(f"Updated Filter Status: {filter_status['state']}")
            elif channel == "recycle_status":
                recycle_status["state"] = data
                print(f"Updated Recycle Status: {recycle_status['state']}")

# Start the Valkey Pub/Sub listener in a separate thread
threading.Thread(target=subscribe_to_valkey, daemon=True).start()

@router.post("/publish/{test_topic}",tags=["Test APIs"])
def publish_message(test_topic: str,message: str,current_user: Dict = Depends(get_current_user)):
    """Publish a message to a Valkey channel"""
    try:
        valkey_client = get_valkey_connection()
        valkey_client.publish(test_topic, message)
        valkey_client.close()
        return {"status": "Message published", "message": message}
    except Exception as e:
        return {"error": str(e)}
    
@router.get("/get_test_status", tags=["Test APIs"])
async def get_test_status(current_user: Dict = Depends(get_current_user)):
    """Endpoint to return the latest received flow test status."""
    return {"state": test_status["state"]}

@router.get("/get_purge_status", tags=["Test APIs"])
async def get_purge_status(current_user: Dict = Depends(get_current_user)):
    """Endpoint to return the latest received purge status."""
    return {"state": purge_status["state"]}

@router.get("/get_clean_status", tags=["Test APIs"])
async def get_clean_status(current_user: Dict = Depends(get_current_user)):
    """Endpoint to return the latest received clean status."""
    return {"state": clean_status["state"]}

@router.get("/get_filter_status", tags=["Test APIs"])
async def get_filter_status(current_user: Dict = Depends(get_current_user)):
    """Endpoint to return the latest received filter status."""
    return {"state": filter_status["state"]}

@router.get("/get_recycle_status", tags=["Test APIs"])
async def get_recycle_status(current_user: Dict = Depends(get_current_user)):
    """Endpoint to return the latest received recycle status."""
    return {"state": recycle_status["state"]}
