ARG REPOS
FROM ${REPOS}

RUN mkdir -p /root/.ssh && chmod 700 /root/.ssh

# Copy all files into the container
COPY main.py /

COPY models /models

COPY auth /auth

COPY pump /pump

COPY relay /relay

COPY reader /reader

COPY config /config

COPY mould /mould

COPY graphs /graphs

COPY tests /tests

COPY service /service

# Command to run the FastAPI application
CMD ["uvicorn", "main:app", "--host=0.0.0.0", "--reload", "--port", "8000"]
