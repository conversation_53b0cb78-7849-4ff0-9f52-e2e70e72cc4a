# auth.py
from fastapi import Depends, H<PERSON><PERSON><PERSON>x<PERSON>, APIRouter
from fastapi.security import OAuth<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTP<PERSON>ear<PERSON>, HTTPAuthorizationCredentials
from jose import jwt
from datetime import datetime, timedelta
from typing import Dict, Optional
import json
from models.auth_model import LoginRequest
import os

# Constants
SECRET_KEY = "ZJrUo3Wig7yCbO33P_BzMmd4eyCZ-laQfYY0L9Bz3vVGtGdTtYYupO-nSxur402pe2OcJg2uV3nSalnPPrkmAg%"
ALGORITHM = "HS256"
ACCESS_TOKEN_EXPIRE_MINUTES = 120

# Security dependencies
oauth2_scheme = OAuth2PasswordBearer(tokenUrl="token")
security = HTTPBearer()

router = APIRouter()

# Load users from JSON
users = {}

def load_users_from_json():
    global users
    with open("/auth/users.json", "r") as file:
        data = json.load(file)
        users = {user["username"]: user for user in data["users"]}

# Run at startup
load_users_from_json()

# Authentication Functions
def authenticate_user(username: str, password: str):
    user = users.get(username)
    if not user or user["password"] != password:  # Plain text comparison (consider hashing)
        return None
    return user

def create_access_token(data: dict, expires_delta: Optional[timedelta] = None):
    to_encode = data.copy()
    expire = datetime.utcnow() + (expires_delta or timedelta(minutes=ACCESS_TOKEN_EXPIRE_MINUTES))
    to_encode.update({"exp": expire})
    return jwt.encode(to_encode, SECRET_KEY, algorithm=ALGORITHM)

def decode_jwt(token: str) -> Dict:
    try:
        payload = jwt.decode(token, SECRET_KEY, algorithms=[ALGORITHM])
        return payload  # Contains user details
    except jwt.JWTError:
        raise HTTPException(status_code=401, detail="Invalid or expired token")

def get_current_user(credentials: HTTPAuthorizationCredentials = Depends(security)) -> Dict:
    token = credentials.credentials  # Extract Bearer token
    return decode_jwt(token)

# Login route
@router.post("/token", tags=["Authentication API"])
def login(login_request: LoginRequest):
    user = authenticate_user(login_request.username, login_request.password)
    if not user:
        raise HTTPException(
            status_code=401,
            detail="Invalid username or password",
            headers={"WWW-Authenticate": "Bearer"},
        )
    access_token = create_access_token(data={"sub": user["username"], "role": user["role"]})
    return {"access_token": access_token, "token_type": "bearer",  "role": user["role"] }

# List usernmae
@router.get("/users", tags=["Authentication API"])
def list_usernames():
    try:
        with open("/auth/users.json", "r") as file:
            data = json.load(file)
            usernames = [user["username"] for user in data["users"]]
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Could not load users: {str(e)}")

    return {"usernames": usernames}