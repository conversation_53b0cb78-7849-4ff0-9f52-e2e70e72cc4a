from pydantic import BaseModel
from typing import Optional, Dict, Any, Union, List


class Channel(BaseModel):
    channel_id: int
    channel_name: str
    channel_lower_limit: float
    channel_upper_limit: float
    channel_diameter: str
    channel_enabled: bool

class InsertData(BaseModel):
    time: str  # Expecting format "YYYY-MM-DD HH:MM:SS"
    mouldid: str
    pressure_set_point_min: float
    pressure_set_point_max: float
    max_water_pressure: float  = 5
    #temperature_set_point: float
    comments: Optional[str] = None
    channels: List[Channel]
    tests: Dict[str, bool]
    
class UpdateData(BaseModel):
    time: Optional[str] = None  # Optional, format "YYYY-MM-DD HH:MM:SS"
    mouldid: Optional[str] = None
    pressure_set_point_min: Optional[float] = None
    pressure_set_point_max: Optional[float] = None
    max_water_pressure: float = 5
    #temperature_set_point: Optional[float]
    comments: Optional[str] = None
    channels: Optional[List[Channel]] = None  # JSON field
    tests: Optional[Dict[str, bool]] = None
    
class ConfigUpdate(BaseModel):
    key: str
    value: Union[int, float, str]  # Allow strings
    
    
    