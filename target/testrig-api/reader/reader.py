import psycopg2
from psycopg2.extras import DictCursor
from fastapi import FastAPI, APIRouter, Depends, HTTPException, File, UploadFile, Form
from fastapi.responses import JSONResponse, StreamingResponse
from typing import Dict, Optional
from auth.auth import get_current_user
import os
from datetime import datetime
import io

class DBConnect:
    def __init__(self, host, database, user, password):
        """Initialize the database connection."""
        try:
            self.conn = psycopg2.connect(
                host=host,
                database=database,
                user=user,
                password=password
            )
        except psycopg2.Error as e:
            print(f"Database connection error: {e}")
            self.conn = None

    def fetch_samples(self):
        """Fetch the latest rows from the samples table."""
        if not self.conn:
            print("No database connection.")
            return []
        
        try:
            cur = self.conn.cursor(cursor_factory=psycopg2.extras.DictCursor)
            query = "SELECT * FROM samples ORDER BY time DESC LIMIT 1;"
            cur.execute(query)
            row = cur.fetchone()
            cur.close()
            return dict(row) if row else None
        except psycopg2.Error as e:
            print(f"Query execution error: {e}")
            return None
        
    def fetch_average_samples(self):
        """Fetch the average of all data_*_* values over the last 5 seconds."""
        if not self.conn:
            print("No database connection.")
            return {}

        try:
            cur = self.conn.cursor(cursor_factory=psycopg2.extras.DictCursor)

            query = "SELECT * FROM samples ORDER BY time DESC LIMIT 1;"           
            cur.execute(query)
            row = cur.fetchone()
            cur.close()
            return dict(row) if row else None

        except psycopg2.Error as e:
            print(f"Query execution error: {e}")
            return None
            
    '''def fetch_average_samples(self):
        """Fetch the average of all data_*_* values over the last 5 seconds."""
        if not self.conn:
            print("No database connection.")
            return {}

        try:
            cur = self.conn.cursor(cursor_factory=psycopg2.extras.DictCursor)

            query = """
                SELECT
                    AVG(data_1_0) AS data_1_0,
                    AVG(data_1_1) AS data_1_1,
                    AVG(data_2_0) AS data_2_0,
                    AVG(data_2_1) AS data_2_1,
                    AVG(data_3_0) AS data_3_0,
                    AVG(data_3_1) AS data_3_1,
                    AVG(data_4_0) AS data_4_0,
                    AVG(data_4_1) AS data_4_1,
                    AVG(data_5_0) AS data_5_0,
                    AVG(data_5_1) AS data_5_1,
                    AVG(data_6_0) AS data_6_0,
                    AVG(data_6_1) AS data_6_1,
                    AVG(data_7_0) AS data_7_0,
                    AVG(data_7_1) AS data_7_1,
                    AVG(data_8_0) AS data_8_0,
                    AVG(data_8_1) AS data_8_1,
                    AVG(data_9_0) AS data_9_0,
                    AVG(data_9_1) AS data_9_1,
                    AVG(data_10_0) AS data_10_0,
                    AVG(data_10_1) AS data_10_1,
                    AVG(data_11_0) AS data_11_0,
                    AVG(data_11_1) AS data_11_1,
                    AVG(data_12_0) AS data_12_0,
                    AVG(data_12_1) AS data_12_1,
                    AVG(data_13_0) AS data_13_0,
                    AVG(data_13_1) AS data_13_1,
                    AVG(data_14_0) AS data_14_0,
                    AVG(data_14_1) AS data_14_1,
                    AVG(data_15_0) AS data_15_0,
                    AVG(data_15_1) AS data_15_1
                FROM samples
                WHERE time > NOW() - INTERVAL '5 seconds';
            """

            cur.execute(query)
            row = cur.fetchone()
            cur.close()
            return dict(row) if row else None

        except Exception as e:
            print(f"Error fetching average samples: {e}")
            return {}'''

        
    def fetch_flow(self):
        """Fetch the latest rows from the samples table."""
        if not self.conn:
            print("No database connection.")
            return []
        
        try:
            cur = self.conn.cursor(cursor_factory=psycopg2.extras.DictCursor)
            query = """
            SELECT data_1_0 AS "1", data_2_0 AS "2", data_3_0 AS "3", data_4_0 AS "4", 
               data_5_0 AS "5", data_6_0 AS "6", data_7_0 AS "7", data_8_0 AS "8", 
               data_9_0 AS "9", data_10_0 AS "10", data_11_0 AS "11", data_12_0 AS "12" 
            FROM samples 
            ORDER BY time DESC 
            LIMIT 1;
            """
            cur.execute(query)
            row = cur.fetchone()
            cur.close()
            return dict(row) if row else None
        except psycopg2.Error as e:
            print(f"Query execution error: {e}")
            return None
    
    def fetch_flow_times(self, fromtime , totime ):
        """Fetch the latest rows from the samples table."""
        if not self.conn:
            print("No database connection.")
            return []
        
        try:
            cur = self.conn.cursor(cursor_factory=psycopg2.extras.DictCursor)
            query = """
            SELECT time, data_1_0 AS "1", data_2_0 AS "2", data_3_0 AS "3", data_4_0 AS "4", 
               data_5_0 AS "5", data_6_0 AS "6", data_7_0 AS "7", data_8_0 AS "8", 
               data_9_0 AS "9", data_10_0 AS "10", data_11_0 AS "11", data_12_0 AS "12" 
            FROM samples time WHERE time BETWEEN %s AND %s
            ORDER BY time DESC 
            ;
            """
            cur.execute(query, (fromtime, totime))
            rows = cur.fetchall()
            cur.close()
            return [dict(row) for row in rows] if rows else []
        except psycopg2.Error as e:
            print(f"Query execution error: {e}")
            return None    
    
    def fetch_flow_moving_weighted_average(self):
        """Fetch the weighted moving average (90% new, 10% old) for the latest row."""
        if not self.conn:
            print("No database connection.")
            return []
        
        try:
            cur = self.conn.cursor(cursor_factory=psycopg2.extras.DictCursor)
            query = """
            SELECT 
                (0.9 * data_1_0 + 0.1 * COALESCE(LAG(data_1_0) OVER (ORDER BY time DESC), data_1_0)) AS "1",
                (0.9 * data_2_0 + 0.1 * COALESCE(LAG(data_2_0) OVER (ORDER BY time DESC), data_2_0)) AS "2",
                (0.9 * data_3_0 + 0.1 * COALESCE(LAG(data_3_0) OVER (ORDER BY time DESC), data_3_0)) AS "3",
                (0.9 * data_4_0 + 0.1 * COALESCE(LAG(data_4_0) OVER (ORDER BY time DESC), data_4_0)) AS "4",
                (0.9 * data_5_0 + 0.1 * COALESCE(LAG(data_5_0) OVER (ORDER BY time DESC), data_5_0)) AS "5",
                (0.9 * data_6_0 + 0.1 * COALESCE(LAG(data_6_0) OVER (ORDER BY time DESC), data_6_0)) AS "6",
                (0.9 * data_7_0 + 0.1 * COALESCE(LAG(data_7_0) OVER (ORDER BY time DESC), data_7_0)) AS "7",
                (0.9 * data_8_0 + 0.1 * COALESCE(LAG(data_8_0) OVER (ORDER BY time DESC), data_8_0)) AS "8",
                (0.9 * data_9_0 + 0.1 * COALESCE(LAG(data_9_0) OVER (ORDER BY time DESC), data_9_0)) AS "9",
                (0.9 * data_10_0 + 0.1 * COALESCE(LAG(data_10_0) OVER (ORDER BY time DESC), data_10_0)) AS "10",
                (0.9 * data_11_0 + 0.1 * COALESCE(LAG(data_11_0) OVER (ORDER BY time DESC), data_11_0)) AS "11",
                (0.9 * data_12_0 + 0.1 * COALESCE(LAG(data_12_0) OVER (ORDER BY time DESC), data_12_0)) AS "12"
            FROM samples
            ORDER BY time DESC
            LIMIT 1;
            """
            cur.execute(query)
            row = cur.fetchone()
            cur.close()
            return dict(row) if row else None
        except psycopg2.Error as e:
            print(f"Query execution error: {e}")
            return None

        
    def outlet_pressure(self):
        """Fetch the latest rows from the samples table."""
        if not self.conn:
            print("No database connection.")
            return []
        
        try:
            cur = self.conn.cursor(cursor_factory=psycopg2.extras.DictCursor)
            query = "SELECT data_14_0 FROM samples ORDER BY time DESC LIMIT 1;"
            cur.execute(query)
            row = cur.fetchone()
            cur.close()
            return dict(row) if row else None
        except psycopg2.Error as e:
            print(f"Query execution error: {e}")
            return None
        
    def inlet_temperature(self):
        """Fetch the latest rows from the samples table."""
        if not self.conn:
            print("No database connection.")
            return []
        
        try:
            cur = self.conn.cursor(cursor_factory=psycopg2.extras.DictCursor)
            query = "SELECT data_15_0 AS inlet FROM samples ORDER BY time DESC LIMIT 1;"
            cur.execute(query)
            row = cur.fetchone()
            cur.close()
            return dict(row) if row else None
        except psycopg2.Error as e:
            print(f"Query execution error: {e}")
            return None
        
    def inlet_temperature_times(self, fromtime, totime):
        """Fetch the latest rows from the samples table."""
        if not self.conn:
            print("No database connection.")
            return []
        
        try:
            cur = self.conn.cursor(cursor_factory=psycopg2.extras.DictCursor)
            query = "SELECT time, data_15_0 AS inlet FROM samples WHERE time BETWEEN %s AND %s ORDER BY time DESC;"
            cur.execute(query, (fromtime, totime))
            rows = cur.fetchall()
            cur.close()
            return [dict(row) for row in rows] if rows else []
        except psycopg2.Error as e:
            print(f"Query execution error: {e}")
            return None

        
    def max_outlet_temperature(self):
        """Fetch the latest rows from the samples table."""
        if not self.conn:
            print("No database connection.")
            return []
        
        try:
            cur = self.conn.cursor(cursor_factory=psycopg2.extras.DictCursor)
            query = """
                SELECT GREATEST(
                        data_1_1, data_2_1, data_3_1, data_4_1, data_5_1, data_6_1,
                        data_7_1, data_8_1, data_9_1, data_10_1, data_11_1, data_12_1
                    ) AS outlet
                FROM samples
                ORDER BY time DESC
                LIMIT 1;
            """
            cur.execute(query)
            result = cur.fetchone()
            cur.close()
            return dict(result) if result else None
        except psycopg2.Error as e:
            print(f"Query execution error: {e}")
            return None
        
    def max_outlet_temperature_times(self,fromtime, totime):
        """Fetch the latest rows from the samples table."""
        if not self.conn:
            print("No database connection.")
            return []
        
        try:
            cur = self.conn.cursor(cursor_factory=psycopg2.extras.DictCursor)
            query = """
                SELECT time, GREATEST(
                        data_1_1, data_2_1, data_3_1, data_4_1, data_5_1, data_6_1,
                        data_7_1, data_8_1, data_9_1, data_10_1, data_11_1, data_12_1
                    ) AS outlet
                FROM samples
                WHERE time BETWEEN %s AND %s ORDER BY time DESC;
            """
            cur.execute(query, (fromtime, totime))
            rows = cur.fetchall()
            cur.close()
            return [dict(row) for row in rows] if rows else []
        except psycopg2.Error as e:
            print(f"Query execution error: {e}")
            return None
        
    def filter_pressure(self):
        """Fetch the latest rows from the samples table."""
        if not self.conn:
            print("No database connection.")
            return []
        
        try:
            cur = self.conn.cursor(cursor_factory=psycopg2.extras.DictCursor)
            query = "SELECT data_13_0 AS filter_pressure FROM samples ORDER BY time DESC LIMIT 1;"
            cur.execute(query)
            row = cur.fetchone()
            cur.close()
            return {"filter_pressure": row[0]} if row else 0
        except psycopg2.Error as e:
            print(f"Query execution error: {e}")
            return None

        
    def pump_pressure(self):
        """Fetch the latest rows from the pump_data table."""
        if not self.conn:
            print("No database connection.")
            return []
        
        try:
            cur = self.conn.cursor(cursor_factory=psycopg2.extras.DictCursor)
            query = "SELECT pump_pressure FROM pump_data ORDER BY time DESC LIMIT 1;"
            cur.execute(query)
            row = cur.fetchone()
            cur.close()
            return dict(row) if row else None
        except psycopg2.Error as e:
            print(f"Query execution error: {e}")
            return None
        
    def pump_pressure_times(self, fromtime, totime):
        """Fetch the latest rows from the pump_data table."""
        if not self.conn:
            print("No database connection.")
            return []
        
        try:
            cur = self.conn.cursor(cursor_factory=psycopg2.extras.DictCursor)
            query = "SELECT time , pump_pressure FROM pump_data WHERE time BETWEEN %s AND %s ORDER BY time DESC;"
            cur.execute(query, (fromtime, totime))
            rows = cur.fetchall()
            cur.close()
            return [dict(row) for row in rows] if rows else []
        except psycopg2.Error as e:
            print(f"Query execution error: {e}")
            return None
        
    def save_file(self, file, file_content):
        if not self.conn:
            print("No database connection.")
            return []
        
        try:
            cur = self.conn.cursor(cursor_factory=psycopg2.extras.DictCursor)
            # 1. Delete all existing rows
            cur.execute("DELETE FROM uploaded_files")
            
            # 2. Insert a new row
            cur.execute(
            "INSERT INTO uploaded_files (filename, content_type, data) VALUES (%s, %s, %s)",
            (file.filename, file.content_type, psycopg2.Binary(file_content))
        )
            self.conn.commit()
            cur.close()
            return JSONResponse({"message": "File saved successfully", "filename": file.filename})
        except psycopg2.Error as e:
            print(f"Query execution error: {e}")
            return None
        
    def download_file(self, filename):
        if not self.conn:
            print("No database connection.")
            return []
           
        try:
            cur = self.conn.cursor(cursor_factory=psycopg2.extras.DictCursor)
            cur.execute("SELECT data, content_type FROM uploaded_files WHERE filename = %s", (filename,))
            row = cur.fetchone()
            cur.close()
  
            if row is None:
                raise HTTPException(status_code=404, detail="File not found")

            file_stream = io.BytesIO(row["data"])

            return StreamingResponse(file_stream, media_type=row["content_type"], headers={
                "Content-Disposition": f"attachment; filename={filename}"
            })
            
        except psycopg2.Error as e:
            print(f"Query execution error: {e}")
            return None
       
    def close(self):
        """Close the database connection."""
        if self.conn:
            self.conn.close()
            print("Database connection closed.")

   
# Global DB connection instance
db_instance = None

router = APIRouter()

@router.on_event("startup")
def startup():
    """Initialize the database connection when the API starts."""
    global db_instance
    db_instance =  DBConnect(host='postgres', database=os.environ.get('POSTGRES_DB'), user=os.environ.get('POSTGRES_USER'), password=os.environ.get('POSTGRES_PASSWORD'))

@router.on_event("shutdown")
def shutdown():
    """Close the database connection when the API shuts down."""
    if db_instance:
        db_instance.close()

# API route to fetch the latest sample
@router.get("/data", response_model=Optional[Dict], tags=["Reader APIs"])
def get_latest_sample(current_user: Dict = Depends(get_current_user)):
    """API endpoint to fetch the latest sample row using a persistent connection."""
    if db_instance:
        return db_instance.fetch_samples()
    return {"error": "Database connection not available"}

@router.get("/data-average", response_model=Optional[Dict], tags=["Reader APIs"])
def get_latest_avg_sample(current_user: Dict = Depends(get_current_user)):
    """API endpoint to fetch the latest sample avg row using a persistent connection."""
    if db_instance:
        return db_instance.fetch_average_samples()
    return {"error": "Database connection not available"}

# API route to fetch the latest flow
@router.get("/flow", response_model=Optional[Dict], tags=["Reader APIs"])
def get_latest_flow(current_user: Dict = Depends(get_current_user)):
    if db_instance:
        return db_instance.fetch_flow()
    return {"error": "Database connection not available"}

# API route to fetch the latest flow
@router.get("/flow-moving-average", response_model=Optional[Dict], tags=["Reader APIs"])
def get_latest_flow_moving_average(current_user: Dict = Depends(get_current_user)):
    if db_instance:
        return db_instance.fetch_flow_moving_weighted_average()
    return {"error": "Database connection not available"}

# API route to fetch the latest output pressure
@router.get("/outlet-pressure", response_model=Optional[Dict], tags=["Reader APIs"])
def get_latest_outletpressure(current_user: Dict = Depends(get_current_user)):
    if db_instance:
        return db_instance.outlet_pressure()
    return {"error": "Database connection not available"}

# API to get the pump pressure
@router.get("/pump-pressure", response_model=Optional[Dict], tags=["Reader APIs"])
def get_latest_pumppressure(current_user: Dict = Depends(get_current_user)):
    """API endpoint to fetch the latest sample row using a persistent connection."""
    if db_instance:
        return db_instance.pump_pressure()
    return {"error": "Database connection not available"}

# API to get the inlet temperature
@router.get("/read-inlet-temperature", response_model=Optional[Dict], tags=["Reader APIs"])
def get_latest_inlet_temp(current_user: Dict = Depends(get_current_user)):
    """API endpoint to fetch the latest sample row using a persistent connection."""
    if db_instance:
        return db_instance.inlet_temperature()
    return {"error": "Database connection not available"}

@router.get("/read-outlet-temperature", response_model=Optional[Dict], tags=["Reader APIs"])
def get_latest_outlet_temp(current_user: Dict = Depends(get_current_user)):
    """API endpoint to fetch the latest sample row using a persistent connection."""
    if db_instance:
        return db_instance.max_outlet_temperature()
    return {"error": "Database connection not available"}

# API to get the filter pressure
@router.get("/read-filter-pressure", response_model=Optional[Dict], tags=["Reader APIs"])
def get_latest_filter_pressure(current_user: Dict = Depends(get_current_user)):
    """API endpoint to fetch the latest sample row using a persistent connection."""
    if db_instance:
        return db_instance.filter_pressure()
    return {"error": "Database connection not available"}

#API to get flow for time ranges
@router.get("/flow/{from_timestamp}/{to_timestamp}", tags=["Reader APIs"])
def get_flow_times(
    from_timestamp: str,
    to_timestamp: str,
    current_user: Dict = Depends(get_current_user),
):
    """Get a list of mould events within a time range (passed as path parameters)."""
    try:
        # Convert string timestamps to datetime objects
        try:
            from_dt = datetime.fromisoformat(from_timestamp)
            to_dt = datetime.fromisoformat(to_timestamp)
        except ValueError:
            raise HTTPException(status_code=400, detail="Invalid timestamp format. Use ISO format: YYYY-MM-DDTHH:MM:SS")

        if from_dt > to_dt:
            raise HTTPException(status_code=400, detail="from_timestamp must be earlier than to_timestamp")

        events = db_instance.fetch_flow_times(from_dt, to_dt)
        return {"flow_timestamped_data": events}
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))
    
#API to get pump pressure for time ranges
@router.get("/pump-pressure/{from_timestamp}/{to_timestamp}", tags=["Reader APIs"])
def get_pump_pressure_times(
    from_timestamp: str,
    to_timestamp: str,
    current_user: Dict = Depends(get_current_user),
):
    """Get a list of mould events within a time range (passed as path parameters)."""
    try:
        # Convert string timestamps to datetime objects
        try:
            from_dt = datetime.fromisoformat(from_timestamp)
            to_dt = datetime.fromisoformat(to_timestamp)
        except ValueError:
            raise HTTPException(status_code=400, detail="Invalid timestamp format. Use ISO format: YYYY-MM-DDTHH:MM:SS")

        if from_dt > to_dt:
            raise HTTPException(status_code=400, detail="from_timestamp must be earlier than to_timestamp")

        events = db_instance.pump_pressure_times(from_dt, to_dt)
        return {"pump_pressure_timestamped_data": events}
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))
    
#API to get inlet temperature for time ranges
@router.get("/read-inlet-temperature/{from_timestamp}/{to_timestamp}", tags=["Reader APIs"])
def get_inlet_temperature_times(
    from_timestamp: str,
    to_timestamp: str,
    current_user: Dict = Depends(get_current_user),
):
    """Get a list of mould events within a time range (passed as path parameters)."""
    try:
        # Convert string timestamps to datetime objects
        try:
            from_dt = datetime.fromisoformat(from_timestamp)
            to_dt = datetime.fromisoformat(to_timestamp)
        except ValueError:
            raise HTTPException(status_code=400, detail="Invalid timestamp format. Use ISO format: YYYY-MM-DDTHH:MM:SS")

        if from_dt > to_dt:
            raise HTTPException(status_code=400, detail="from_timestamp must be earlier than to_timestamp")

        events = db_instance.inlet_temperature_times(from_dt, to_dt)
        return {"inlet_temp_timestamped_data": events}
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))
    
#API to get outlet temperature for time ranges
@router.get("/read-outlet-temperature/{from_timestamp}/{to_timestamp}", tags=["Reader APIs"])
def get_outlet_temperature_times(
    from_timestamp: str,
    to_timestamp: str,
    current_user: Dict = Depends(get_current_user),
):
    """Get a list of mould events within a time range (passed as path parameters)."""
    try:
        # Convert string timestamps to datetime objects
        try:
            from_dt = datetime.fromisoformat(from_timestamp)
            to_dt = datetime.fromisoformat(to_timestamp)
        except ValueError:
            raise HTTPException(status_code=400, detail="Invalid timestamp format. Use ISO format: YYYY-MM-DDTHH:MM:SS")

        if from_dt > to_dt:
            raise HTTPException(status_code=400, detail="from_timestamp must be earlier than to_timestamp")

        events = db_instance.outlet_temperature_times(from_dt, to_dt)
        return {"inlet_temp_timestamped_data": events}
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))
    
@router.post("/upload/", tags=["Reader APIs"])
def upload_file(file: UploadFile = File(...), current_user: Dict = Depends(get_current_user)):
    try:
        file_content = file.file.read()
        if db_instance:
            return db_instance.save_file(file, file_content)
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))
    
@router.get("/download/{filename}", tags=["Reader APIs"])
def download_file(filename: str,current_user: Dict = Depends(get_current_user)):
    try:
        if db_instance:
            return db_instance.download_file(filename)
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))
