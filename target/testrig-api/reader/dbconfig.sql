CREATE TABLE IF NOT EXISTS samples (
"time" timestamp with time zone NOT NULL,
machine macaddr NOT NULL,
manifold macaddr NOT NULL,
data_1_0 double precision,
data_1_1 double precision,
data_2_0 double precision,
data_2_1 double precision,
data_3_0 double precision,
data_3_1 double precision,
data_4_0 double precision,
data_4_1 double precision,
data_5_0 double precision,
data_5_1 double precision,
data_6_0 double precision,
data_6_1 double precision,
data_7_0 double precision,
data_7_1 double precision,
data_8_0 double precision,
data_8_1 double precision,
data_9_0 double precision,
data_9_1 double precision,
data_10_0 double precision,
data_10_1 double precision,
data_11_0 double precision,
data_11_1 double precision,
data_12_0 double precision,
data_12_1 double precision,
data_13_0 double precision,
data_13_1 double precision,
data_14_0 double precision,
data_14_1 double precision,
data_15_0 double precision,
data_15_1 double precision
);

CREATE TABLE IF NOT EXISTS mould_data (
    time TIMESTAMP,
    mouldid TEXT PRIMARY KEY,
    pressure_set_point_min DOUBLE PRECISION,
    pressure_set_point_max DOUBLE PRECISION,
    max_water_pressure DOUBLE PRECISION,
    comments TEXT,
    channels JSONB,  -- Stores all the connection/channel information as a JSON object
    tests JSONB

);

CREATE TABLE IF NOT EXISTS mould_events (
    time TIMESTAMP,
    mouldid TEXT,
    msg TEXT
);

CREATE TABLE IF NOT EXISTS valve_events (
    time TIMESTAMP,
    relayID TEXT PRIMARY KEY,
    state INT
);

CREATE TABLE IF NOT EXISTS pump_data (
    time TIMESTAMP,
    pump_pressure double precision
    
);

CREATE TABLE IF NOT EXISTS uploaded_files (
    id SERIAL PRIMARY KEY,
    filename TEXT,
    content_type TEXT,
    data BYTEA
);


CREATE INDEX CONCURRENTLY ON samples (time);

CREATE INDEX idx_samples_time ON samples(time DESC);

CREATE INDEX UNIQUEINDEX ON samples(time,machine,manifold); 

CREATE INDEX MANIFOLDTIMEIDX ON samples(manifold, time DESC);

CREATE INDEX idx_pump_pressure_timestamp ON pump_data (time);

