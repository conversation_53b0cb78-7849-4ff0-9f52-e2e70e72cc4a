from fastapi import APIRouter, Depends, HTTPException
import json
import os
from auth.auth import get_current_user
from typing import Dict, List, Union, Any

router = APIRouter()

# Default configuration values
CONFIG_FILE = "/config/config.json"
DEFAULT_CONFIG = {
    "flow_stabilization_timeout": 20,
    "flow_stabilization_limit": 0.012,
    "max_pressure_loss": 0.6,
    "pressure_stabilization_timeout": 60,
    "pressure_measure_time": 60,
    "tcu_temperature":30,
    #"cooling_cutoff_temperature":28,
    "location": "FR",
    "user_language": "FR",
    "language": ["FR"]  # Changed from string to a list of supported languages
}

def load_config():
    """Load configuration from JSON file or create default if not found."""
    if os.path.exists(CONFIG_FILE):
        with open(CONFIG_FILE, "r") as file:
            config = json.load(file)
            # Ensure backward compatibility if old config uses a string for language
            if isinstance(config.get("language"), str):
                config["language"] = [config["language"]]
            return config
    else:
        save_config(DEFAULT_CONFIG)
        return DEFAULT_CONFIG

def save_config(config):
    """Save configuration to JSON file."""
    with open(CONFIG_FILE, "w") as file:
        json.dump(config, file, indent=4)

@router.get("/config", tags=["Configuration APIs"])
def get_config(current_user: Dict = Depends(get_current_user)):
    """Retrieve the current configuration."""
    return load_config()

@router.put("/config", tags=["Configuration APIs"])
def update_config(updates: Dict[str, Any], current_user: Dict = Depends(get_current_user)):
    """Update multiple configuration values at once."""
    config = load_config()

    # Restricted keys
    restricted_keys = {"language"}
    string_keys = {"location", "user_language"}

    for key, value in updates.items():
        if key in restricted_keys:
            raise HTTPException(status_code=403, detail=f"Modification of '{key}' is not allowed")
        if key in string_keys and not isinstance(value, str):
            raise HTTPException(status_code=400, detail=f"{key} must be a string")
        if key not in config:
            raise HTTPException(status_code=400, detail=f"Invalid configuration key: {key}")

        # Update config
        config[key] = value

    # Save updated config
    save_config(config)
    return {"message": "Configuration updated successfully"}

