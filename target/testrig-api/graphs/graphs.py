from fastapi import APIRouter, Depends, HTTPException
from typing import Dict, List
from fastapi import FastAPI, Query, UploadFile, File, HTTPException
from fastapi.responses import StreamingResponse, JSONResponse
import matplotlib.pyplot as plt
import matplotlib.dates as mdates
import pandas as pd
from io import BytesIO
from datetime import datetime
from auth.auth import get_current_user
import os
import psycopg2
from psycopg2.extras import DictCursor
import numpy as np
from scipy.interpolate import make_interp_spline
from scipy.interpolate import UnivariateSpline
from scipy.signal import savgol_filter
from pathlib import Path

UPLOAD_DIR = Path("/mould/reports")
UPLOAD_DIR.mkdir(parents=True, exist_ok=True)

class DBConnect:
    def __init__(self, host, database, user, password):
        """Initialize the database connection."""
        try:
            self.conn = psycopg2.connect(
                host=host,
                database=database,
                user=user,
                password=password
            )
            self.cursor = self.conn.cursor(cursor_factory=DictCursor)
        except psycopg2.Error as e:
            print(f"Database connection error: {e}")
            self.conn = None           

router = APIRouter()

########################## Helper Function#########################################
def moving_average(x, window_size=5):
        return np.convolve(x, np.ones(window_size)/window_size, mode='same')
       

################################ Generate Graph for flow test############################################                
'''@router.get("/generate_flow_chart/flow/{start_time}/{end_time}", tags=["Graph APIs"])
def generate_flow_chart(
    start_time: datetime,
    end_time: datetime,
    enabled_channels: str,
    lang: str = Query("en", enum=["en", "fr"]),
    current_user: dict = Depends(get_current_user)
):
    try:
        channel_list = list(map(int, enabled_channels.split(",")))
        valid_channels = [ch for ch in channel_list if 1 <= ch <= 12]
        if not valid_channels:
            raise HTTPException(status_code=400, detail="No valid channels provided (1–12 allowed).")

        db = DBConnect(
            host='postgres',
            database=os.environ.get('POSTGRES_DB'),
            user=os.environ.get('POSTGRES_USER'),
            password=os.environ.get('POSTGRES_PASSWORD')
        )

        if not db.conn:
            raise HTTPException(status_code=500, detail="Database connection failed.")

        channel_columns = [f"data_{ch}_0" for ch in valid_channels]
        columns_str = ', '.join(['time'] + channel_columns)

        db.cursor.execute(f"""
            SELECT {columns_str}
            FROM samples
            WHERE time BETWEEN %s AND %s
        """, (start_time, end_time))
        sample_data_rows = db.cursor.fetchall()
        sample_columns = [desc[0] for desc in db.cursor.description]

        if not sample_data_rows:
            raise HTTPException(status_code=404, detail="No sample data found.")

        sample_df = pd.DataFrame(sample_data_rows, columns=sample_columns)
        sample_df['time'] = pd.to_datetime(sample_df['time']).dt.floor("S")
        if sample_df['time'].dt.tz is not None:
            sample_df['time'] = sample_df['time'].dt.tz_localize(None)
        sample_df = sample_df.sort_values('time')

        db.cursor.execute("""
            SELECT time, pump_pressure
            FROM pump_data
            WHERE time BETWEEN %s AND %s
        """, (start_time, end_time))
        pump_data_rows = db.cursor.fetchall()
        pump_columns = [desc[0] for desc in db.cursor.description]

        if not pump_data_rows:
            raise HTTPException(status_code=404, detail="No pump data found.")

        pump_df = pd.DataFrame(pump_data_rows, columns=pump_columns)
        pump_df['time'] = pd.to_datetime(pump_df['time']).dt.floor("S")
        if pump_df['time'].dt.tz is not None:
            pump_df['time'] = pump_df['time'].dt.tz_localize(None)
        pump_df = pump_df.sort_values('time')

        aligned_df = pd.merge_asof(
            sample_df,
            pump_df,
            on='time',
            direction='nearest',
            tolerance=pd.Timedelta("0.2s")
        )
        aligned_df.dropna(subset=["pump_pressure"], inplace=True)
        if aligned_df.empty:
            raise HTTPException(status_code=404, detail="No aligned pump pressure data found.")

        # --- FILTER: Keep only stable pressures (rolling stddev < 0.015 bar over 5s window) ---
        aligned_df["pressure_std"] = aligned_df["pump_pressure"].rolling(window=5, min_periods=1, center=True).std()
        aligned_df = aligned_df[aligned_df["pressure_std"] < 0.015]
        aligned_df = aligned_df.reset_index(drop=True)

        step_size = 0.2
        aligned_df["pressure_step"] = (aligned_df["pump_pressure"] / step_size).round() * step_size

        min_p = max(0.5, aligned_df["pressure_step"].min())
        max_p = min(9.0, aligned_df["pressure_step"].max())
        target_steps = np.round(np.arange(min_p, max_p + step_size, step_size), 2)

        labels = {
            "en": {
                "xlabel": "Pump Pressure (bar)",
                "ylabel": "Average Flow",
                "title": "Flow vs Pump Pressure"
            },
            "fr": {
                "xlabel": "Pression de la pompe (bar)",
                "ylabel": "Débit moyen",
                "title": "Débit vs Pression de la pompe"
            }
        }

        fig, ax = plt.subplots()
        plotted_any = False

        for ch in valid_channels:
            col = f"data_{ch}_0"
            if col in aligned_df.columns and aligned_df[col].notnull().sum() > 0:
                grouped = aligned_df.groupby("pressure_step")[col].agg(['mean', 'median', 'count']).reset_index()
                grouped["avg_flow"] = (grouped["mean"] + grouped["median"]) / 2

                # Clip outliers
                z = (grouped["avg_flow"] - grouped["avg_flow"].mean()) / grouped["avg_flow"].std()
                grouped = grouped[(z > -2.5) & (z < 2.5)]

                # Keep only target steps and enough samples per step
                grouped = grouped[grouped["pressure_step"].isin(target_steps)]
                grouped = grouped[grouped["count"] >= 5]
                grouped = grouped.sort_values("pressure_step").reset_index(drop=True)

                x = grouped["pressure_step"].values
                y = grouped["avg_flow"].values

                # --- EXTRA: Smooth curve using Savitzky-Golay filter ---
                if len(x) >= 7:
                    try:
                        y_smooth = savgol_filter(y, window_length=7, polyorder=3)
                        ax.plot(x, y_smooth, label=f"Channel {ch}")
                    except Exception as ex:
                        ax.plot(x, y, label=f"Channel {ch}")
                else:
                    ax.plot(x, y, label=f"Channel {ch}")

                plotted_any = True

        if not plotted_any:
            raise HTTPException(status_code=404, detail="No valid flow data to plot.")

        ax.set_xlabel(labels[lang]["xlabel"])
        ax.set_ylabel(labels[lang]["ylabel"])
        ax.set_title(labels[lang]["title"])
        ax.legend(loc='best', fontsize='small')
        ax.grid(True)

        buf = BytesIO()
        plt.savefig(buf, format='png')
        buf.seek(0)
        plt.close(fig)
        return StreamingResponse(buf, media_type="image/png")

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Internal error: {str(e)}")
    finally:
        db.cursor.close()
        db.conn.close()
        plt.close('all')'''

@router.get("/generate_flow_chart/flow/{start_time}/{end_time}", tags=["Graph APIs"])
def generate_flow_chart(
    start_time: datetime,
    end_time: datetime,
    enabled_channels: str,
    lang: str = Query("en", enum=["en", "fr"]),
    current_user: dict = Depends(get_current_user)
):
    try:
        channel_list = list(map(int, enabled_channels.split(",")))
        valid_channels = [ch for ch in channel_list if 1 <= ch <= 12]
        if not valid_channels:
            raise HTTPException(status_code=400, detail="No valid channels provided (1–12 allowed).")

        db = DBConnect(
            host='postgres',
            database=os.environ.get('POSTGRES_DB'),
            user=os.environ.get('POSTGRES_USER'),
            password=os.environ.get('POSTGRES_PASSWORD')
        )

        if not db.conn:
            raise HTTPException(status_code=500, detail="Database connection failed.")

        channel_columns = [f"data_{ch}_0" for ch in valid_channels]
        columns_str = ', '.join(['time'] + channel_columns)

        db.cursor.execute(f"""
            SELECT {columns_str}
            FROM samples
            WHERE time BETWEEN %s AND %s
        """, (start_time, end_time))
        sample_data_rows = db.cursor.fetchall()
        sample_columns = [desc[0] for desc in db.cursor.description]

        if not sample_data_rows:
            raise HTTPException(status_code=404, detail="No sample data found.")

        sample_df = pd.DataFrame(sample_data_rows, columns=sample_columns)
        sample_df['time'] = pd.to_datetime(sample_df['time']).dt.floor("S")
        if sample_df['time'].dt.tz is not None:
            sample_df['time'] = sample_df['time'].dt.tz_localize(None)
        sample_df = sample_df.sort_values('time')

        db.cursor.execute("""
            SELECT time, pump_pressure
            FROM pump_data
            WHERE time BETWEEN %s AND %s
        """, (start_time, end_time))
        pump_data_rows = db.cursor.fetchall()
        pump_columns = [desc[0] for desc in db.cursor.description]

        if not pump_data_rows:
            raise HTTPException(status_code=404, detail="No pump data found.")

        pump_df = pd.DataFrame(pump_data_rows, columns=pump_columns)
        pump_df['time'] = pd.to_datetime(pump_df['time']).dt.floor("S")
        if pump_df['time'].dt.tz is not None:
            pump_df['time'] = pump_df['time'].dt.tz_localize(None)
        pump_df = pump_df.sort_values('time')

        # Align on time
        aligned_df = pd.merge_asof(
            sample_df,
            pump_df,
            on='time',
            direction='nearest',
            tolerance=pd.Timedelta("0s")
        )
        aligned_df.dropna(subset=["pump_pressure"], inplace=True)
        if aligned_df.empty:
            raise HTTPException(status_code=404, detail="No aligned pump pressure data found.")

        step_size = 0.2
        aligned_df["pressure_step"] = (aligned_df["pump_pressure"] / step_size).round() * step_size

        min_p = max(0.5, aligned_df["pressure_step"].min())
        max_p = min(9.0, aligned_df["pressure_step"].max())
        target_steps = np.round(np.arange(min_p, max_p + step_size, step_size), 2)

        labels = {
            "en": {
                "xlabel": "Pump Pressure (bar)",
                "ylabel": "Average Flow",
                "title": "Flow vs Pump Pressure"
            },
            "fr": {
                "xlabel": "Pression de la pompe (bar)",
                "ylabel": "Débit moyen",
                "title": "Débit vs Pression de la pompe"
            }
        }

        fig, ax = plt.subplots()
        plotted_any = False

        for ch in valid_channels:
            col = f"data_{ch}_0"
            if col in aligned_df.columns and aligned_df[col].notnull().sum() > 0:
                # Aggregate flow by pressure step
                grouped = aligned_df.groupby("pressure_step")[col].agg(['mean', 'median', 'count']).reset_index()
                grouped["avg_flow"] = (grouped["mean"] + grouped["median"]) / 2

                # IQR filtering for outliers
                q1 = grouped["avg_flow"].quantile(0.25)
                q3 = grouped["avg_flow"].quantile(0.75)
                iqr = q3 - q1
                grouped = grouped[(grouped["avg_flow"] >= q1 - 1.5 * iqr) & (grouped["avg_flow"] <= q3 + 1.5 * iqr)]

                # Only retain target with count >=1
                grouped = grouped[grouped["pressure_step"].isin(target_steps)]
                grouped = grouped[grouped["count"] >= 1]
                grouped = grouped.sort_values("pressure_step").reset_index(drop=True)

                x = grouped["pressure_step"].values
                y = grouped["avg_flow"].values

                if len(x) >= 5:
                    try:
                        # 1. Initial smoothing with Savitzky-Golay
                        window_len = min(11, len(x) // 2 * 2 + 1)
                        y_savgol = savgol_filter(y, window_length=window_len, polyorder=2)

                        # 2. Further smoothing using UnivariateSpline to reduce wiggles
                        spline = UnivariateSpline(x, y_savgol, k=3, s=0.5 * len(x))  # s controls smoothness
                        y_spline = spline(x)

                        ax.plot(x, y_spline, label=f"Channel {ch}")
                        plotted_any = True
                    except Exception as e:
                        ax.plot(x, y, label=f"Channel {ch}")
                        plotted_any = True
                elif len(x) >= 2:
                    ax.plot(x, y, label=f"Channel {ch}")
                    plotted_any = True

        if not plotted_any:
            raise HTTPException(status_code=404, detail="No valid flow data to plot.")

        ax.set_xlabel(labels[lang]["xlabel"])
        ax.set_ylabel(labels[lang]["ylabel"])
        ax.set_title(labels[lang]["title"])
        ax.legend(loc='best', fontsize='small')
        ax.grid(True)

        buf = BytesIO()
        plt.savefig(buf, format='png')
        buf.seek(0)
        plt.close(fig)
        return StreamingResponse(buf, media_type="image/png")

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Internal error: {str(e)}")
    finally:
        db.cursor.close()
        db.conn.close()
        plt.close('all')



################################Generate Graph for heat test############################################
        
@router.get("/generate_temp_chart/temp/{start_time}/{end_time}", tags=["Graph APIs"])
def generate_temp_chart(
    start_time: datetime,
    end_time: datetime,
    enabled_channels: str,
    lang: str = Query("en", enum=["en", "fr"]),
    current_user: dict = Depends(get_current_user)
):
    # Convert the comma-separated string into a list of integers
    try:
        channel_list = list(map(int, enabled_channels.split(",")))
    except ValueError:
        raise HTTPException(status_code=400, detail="Invalid format for enabled channels. Use comma-separated numbers.")

    # Validate channels (1-12 allowed)
    valid_channels = [ch for ch in channel_list if 1 <= ch <= 12]
    if not valid_channels:
        raise HTTPException(status_code=400, detail="No valid channels provided (1-12 allowed).")

    # Establish DB connection
    db = DBConnect(host='postgres', 
                   database=os.environ.get('POSTGRES_DB'),
                   user=os.environ.get('POSTGRES_USER'),
                   password=os.environ.get('POSTGRES_PASSWORD'))
    
    if not db.conn:
        raise HTTPException(status_code=500, detail="Database connection failed.")

    try:
        # Fetch temperature data
        temp_columns = [f"data_{ch}_1" for ch in valid_channels]  # Using correct column mapping
        columns_str = ', '.join(['"time"'] + [f'"{col}"' for col in temp_columns])  # Ensure correct SQL syntax

        db.cursor.execute(f"""
            SELECT {columns_str}
            FROM samples
            WHERE "time" BETWEEN %s AND %s
            ORDER BY "time" ASC
        """, (start_time, end_time))

        sample_data_rows = db.cursor.fetchall()

        if not sample_data_rows:
            raise HTTPException(status_code=404, detail="No temperature data found for the given time range.")

        # Extract timestamps and temperature values
        sample_times = [row['time'] for row in sample_data_rows]
        temp_data = {ch: [] for ch in valid_channels}

        for row in sample_data_rows:
            for ch in valid_channels:
                temp_data[ch].append(row[f"data_{ch}_1"])  # Extracting corresponding temp data

        # Convert timestamps for plotting
        sample_times = np.array(sample_times)

        # Plot data
        # Label translations
        labels = {
            "en": {
                "xlabel": "Time",
                "ylabel": "Temperature (°C)",
                "title": "Temperature vs Time"
            },
            "fr": {
                "xlabel": "Heure",
                "ylabel": "Température (°C)",
                "title": "Température vs. Heure"
            }
        }
        
        fig, ax = plt.subplots(figsize=(10, 5))
        for ch in valid_channels:
            smoothed_temp = moving_average(temp_data[ch], window_size=5)
            ax.plot(sample_times, smoothed_temp, label=f"Channel {ch}")

        # Formatting plot       
        fig, ax = plt.subplots(figsize=(10, 5))

        for ch in valid_channels:
            smoothed_temp = moving_average(temp_data[ch], window_size=5)
            ax.plot(sample_times, smoothed_temp, label=f"Channel {ch}")

        # Formatting plot
        ax.set_xlabel(labels[lang]["xlabel"])
        ax.set_ylabel(labels[lang]["ylabel"])
        ax.set_title(labels[lang]["title"])
        ax.legend()
        ax.grid(True)

        # Format x-axis as datetime
        # Set major ticks every 5 minutes
        ax.xaxis.set_major_locator(mdates.MinuteLocator(interval=5))
        ax.xaxis.set_major_formatter(mdates.DateFormatter('%H:%M'))
        fig.autofmt_xdate()  # Rotate date labels for readability

        # Save to BytesIO and stream as PNG
        buf = BytesIO()
        plt.savefig(buf, format="png")
        buf.seek(0)
        plt.close(fig)

        return StreamingResponse(buf, media_type="image/png")

    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

    finally:
        db.cursor.close()
        db.conn.close()
        plt.close(fig)
        

################################Upload PDF file############################################        
@router.post("/upload-pdf/", tags=["Graph APIs"])
def upload_pdf(file: UploadFile = File(...)):
    if file.content_type != "application/pdf":
        raise HTTPException(status_code=400, detail="Only PDF files are allowed.")

    try:
        file_path = UPLOAD_DIR / file.filename
        contents = file.file.read()  # synchronous read
        file_path.write_bytes(contents)  # write file to disk
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to save PDF: {str(e)}")
    finally:
        file.file.close()

    return JSONResponse(content={"message": "PDF uploaded successfully", "filename": file.filename})