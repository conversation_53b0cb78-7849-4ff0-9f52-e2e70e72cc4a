import psycopg2
import psycopg2.extras
from datetime import datetime
from fastapi import APIRouter, Depends, HTTPException, Body
from pydantic import BaseModel
from typing import Optional, Dict, Any
from datetime import datetime, timedelta
from auth.auth import get_current_user
from models.data_model import InsertData, UpdateData
import os
import pandas as pd

MOULDS_FILE_PATH = "/mould/moulds.xlsx"
CHANNELS_FILE_PATH = "/mould/circuits.xlsx"
DIAMETERS_FILE_PATH = "/mould/diameters.xlsx"

# To load and split the sheet
def _load_moulds():
    custom_headers = [
        "Year", "Order Number", "Client", "Description",
        "No. of Moulds", "No. of Cavity", "Volume", "Name"
    ]
    df = pd.read_excel(MOULDS_FILE_PATH, header=None, engine='openpyxl')
    df_cleaned = df.dropna(how='all').dropna(axis=1, how='all')
    df_cleaned.columns = custom_headers[:df_cleaned.shape[1]]
    df_data = df_cleaned[1:].reset_index(drop=True)
    meta = df_cleaned.iloc[:1]
    return meta, df_data

class DBConnect:
    def __init__(self, host, database, user, password):
        """Initialize the database connection."""
        self.conn = psycopg2.connect(
            host=host,
            database=database,
            user=user,
            password=password
        )

    def insert_data(self, time_value, mouldid, pressure_set_point_min, pressure_set_point_max ,max_water_pressure, comments, channels, tests):
        try:
            cur = self.conn.cursor()
            
            # Ensure time_value is in correct format
            if isinstance(time_value, str):
                time_value = datetime.strptime(time_value, '%Y-%m-%d %H:%M:%S')

            # Convert Pydantic models to dictionaries before inserting into JSONB
            channels_serialized = [channel.dict() if hasattr(channel, 'dict') else channel for channel in channels]
            
            # Ensure tests is a list of 4 booleans
            if not isinstance(tests, dict):
                cur.close()
                raise ValueError("tests must be a dictionary of test results")
            
            # Check if the record already exists
            check_query = "SELECT 1 FROM mould_data WHERE mouldid = %s AND time = %s"
            cur.execute(check_query, (mouldid, time_value))
            if cur.fetchone():
                raise HTTPException(status_code=404, detail="Record already exists")

            insert_query = """
                INSERT INTO mould_data (time, mouldid, pressure_set_point_min, pressure_set_point_max ,max_water_pressure,comments, channels, tests)
                VALUES (%s, %s, %s, %s, %s, %s, %s, %s)
                RETURNING mouldid;
            """
            cur.execute(insert_query, (
                time_value,
                mouldid,
                pressure_set_point_min,
                pressure_set_point_max,
                max_water_pressure,
                comments,
                psycopg2.extras.Json(channels_serialized),  # Use psycopg2.extras.Json for JSONB field
                psycopg2.extras.Json(tests)
            ))
            inserted_mouldid = cur.fetchone()[0]
            self.conn.commit()
            cur.close()
            return inserted_mouldid

        except Exception as e:
            self.conn.rollback()
            print("Error in insert_data:", e)
            return None
        
    def update_data(self, mould_id, updated_fields):
        try:
            cur = self.conn.cursor()
            
            # Check if the mould_id exists
            cur.execute("SELECT 1 FROM mould_data WHERE mouldid = %s;", (mould_id,))
            if cur.fetchone() is None:
                cur.close()
                raise HTTPException(status_code=404, detail=f"Mould ID {mould_id} not found")

            
            set_clauses = []
            values = []

            # Dynamically build the SET clause
            for key, value in updated_fields.items():
                set_clauses.append(f"{key} = %s")

                # Convert JSONB fields properly
                if key in ['channels', 'tests']:  # Ensure both JSONB fields are handled
                    values.append(psycopg2.extras.Json(value))
                else:
                    values.append(value)

            # Append the mould_id for the WHERE clause
            values.append(mould_id)

            # Ensure at least one field is updated
            if not set_clauses:
                raise ValueError("No valid fields provided for update")

            update_query = f"""
                UPDATE mould_data
                SET {', '.join(set_clauses)}
                WHERE mouldid = %s;
            """
            
            cur.execute(update_query, tuple(values))
            self.conn.commit()

            # Check if any rows were actually updated
            if cur.rowcount == 0:
                print("Warning: No rows updated (mould_id may not exist)")
                return False
            
            cur.close()
            return True  # Indicating success

        except Exception as e:
            self.conn.rollback()
            print("Error in update_data:", e)
            return False
        

    def fetch_data(self, mouldid):
        """
        Fetch data for the specified mouldid.
        """
        try:
            cur = self.conn.cursor(cursor_factory=psycopg2.extras.DictCursor)
            select_query = "SELECT * FROM mould_data WHERE mouldid = %s;"
            cur.execute(select_query, (mouldid,))
            row = cur.fetchone()
            cur.close()
            return dict(row) if row else None
        except Exception as e:
            print("Error in fetch_data:", e)
            return None
        
    def delete_data(self, mouldid):
        """
        Delete data for the specified mouldid.
        """
        try:
            cur = self.conn.cursor()
            delete_query = "DELETE FROM mould_data WHERE mouldid = %s;"
            cur.execute(delete_query, (mouldid,))
            self.conn.commit()
            cur.close()
            return True
        except Exception as e:
            print("Error in delete_data:", e)
            return False

        
    def fetch_latest_mould_event(self, mouldid):
        """
        Fetch data for the specified mouldid.
        """
        try:
            cur = self.conn.cursor(cursor_factory=psycopg2.extras.DictCursor)
            select_query = "SELECT msg FROM mould_events WHERE mouldid = %s order by time DESC LIMIT 1;"
            cur.execute(select_query, (mouldid,))
            row = cur.fetchone()
            cur.close()
            return dict(row) if row else None
        except Exception as e:
            print("Error in fetch_data:", e)
            return None
        
    def fetch_events_from_last_start(self, mouldid):
        try:
            cur = self.conn.cursor(cursor_factory=psycopg2.extras.DictCursor)

            # Get the timestamp of the last 'start' message
            cur.execute("""
                SELECT time 
                FROM mould_events 
                WHERE mouldid = %s AND msg = 'start' 
                ORDER BY time DESC 
                LIMIT 1;
            """, (mouldid,))
            start_row = cur.fetchone()

            if not start_row:
                cur.close()
                return []  # No 'start' message found

            start_time = start_row['time']

            # Get all events since thatimestamp 
            cur.execute("""
                SELECT msg 
                FROM mould_events 
                WHERE mouldid = %s AND time >= %s 
                ORDER BY time ASC;
            """, (mouldid, start_time))
            rows = cur.fetchall()
            cur.close()

            return [row['msg'] for row in rows]
        except Exception as e:
            print("Error in fetch_events_from_last_start:", e)
            return []
        
    def fetch_mould_events_times(self, mouldid, from_timestamp, to_timestamp):
        """
        Fetch all mould events for the specified mouldid within a given time range.
        """
        try:
            cur = self.conn.cursor(cursor_factory=psycopg2.extras.DictCursor)
            select_query = """
                SELECT msg, time FROM mould_events
                WHERE mouldid = %s AND time >= %s AND time <= %s
                ORDER BY time DESC;
            """
            to_timestamp_plus_one = to_timestamp + timedelta(seconds=1)
            cur.execute(select_query, (mouldid, from_timestamp, to_timestamp_plus_one))
            rows = cur.fetchall()
            cur.close()
            return [dict(row) for row in rows] if rows else []
        except Exception as e:
            print("Error in fetch_mould_events:", e)
            return []
        
           
    def fetch_mould_start_stop_pairs(self, mouldid):
        """
        Retrieve all start and stop timestamp pairs for the given mouldid,
        ensuring each start is followed by a stop while ignoring other messages.
        A start-stop pair is ignored if the message immediately before the stop 
        contains the word 'stop'.
        """
        try:
            cur = self.conn.cursor(cursor_factory=psycopg2.extras.DictCursor)

            query = """
            SELECT msg, time FROM mould_events 
            WHERE mouldid = %s 
            ORDER BY time ASC;
            """

            cur.execute(query, (mouldid,))
            rows = cur.fetchall()
            cur.close()

            start_stop_pairs = []
            start_time = None
            prev_msg = ""

            for row in rows:
                msg = row["msg"]
                time = row["time"]

                if msg == "start":
                    start_time = time
                elif msg == "stop":
                    if start_time and "stop" not in prev_msg.lower():
                        start_stop_pairs.append({"start_time": start_time, "stop_time": time})
                        start_time = None  # Reset after pairing

                prev_msg = msg  # Update previous message

            return start_stop_pairs

        except Exception as e:
            print(f"Error fetching mould start-stop pairs: {e}")
            return []

    
    def list_mouldids(self):
        """
        Fetch a list of distinct mould IDs from the database.
        """
        try:
            cur = self.conn.cursor()
            query = "SELECT DISTINCT mouldid FROM mould_data;"
            cur.execute(query)
            mouldids = [row[0] for row in cur.fetchall()]
            cur.close()
            return mouldids
        except Exception as e:
            print("Error in list_mouldids:", e)
            return []

    def close(self):
        """Close the database connection."""
        self.conn.close()


router = APIRouter()

@router.on_event("startup")
def startup_event():
    global db
    db = DBConnect(host='postgres', database=os.environ.get('POSTGRES_DB'), user=os.environ.get('POSTGRES_USER'), password=os.environ.get('POSTGRES_PASSWORD'))

@router.on_event("shutdown")
def shutdown_event():
    db.close()

@router.post("/insert", tags=["Mould APIs"])
def insert_record(data: InsertData, current_user: Dict = Depends(get_current_user)):
    """Insert a new record."""
    try:
        record_id = db.insert_data(
            data.time,
            data.mouldid,
            data.pressure_set_point_min,
            data.pressure_set_point_max,
            data.max_water_pressure,
            data.comments,
            data.channels,
            data.tests
        )
        if record_id is None:
            raise HTTPException(status_code=500, detail="Insertion failed.")
        return {"mouldid": record_id}
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.put("/update/{mouldid}", tags=["Mould APIs"])
def update_record(mouldid: str, data: UpdateData, current_user: Dict = Depends(get_current_user)):
    """Update an existing record by mouldid."""
    try:
        update_fields = data.dict(exclude_unset=True)
        if "time" in update_fields and isinstance(update_fields["time"], str):
            update_fields["time"] = datetime.strptime(update_fields["time"], '%Y-%m-%d %H:%M:%S')
        db.update_data(mouldid, update_fields)
        return {"message": "Record updated successfully."}
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/fetch/{mouldid}", tags=["Mould APIs"])
def fetch_record(mouldid: str, current_user: Dict = Depends(get_current_user)):
    """Fetch a record by its mouldid."""
    try:
        record = db.fetch_data(mouldid)
        if record is None:
            raise HTTPException(status_code=404, detail="Record not found.")
        if "time" in record and isinstance(record["time"], datetime):
            record["time"] = record["time"].strftime('%Y-%m-%d %H:%M:%S')
        return record
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))
    
@router.delete("/delete/{mouldid}", tags=["Mould APIs"])
def delete_record(mouldid: str, current_user: Dict = Depends(get_current_user)):
    """Delete a record by its mouldid."""
    try:
        success = db.delete_data(mouldid)
        if not success:
            raise HTTPException(status_code=500, detail="Failed to delete record.")

        return {"detail": f"Record with mouldid '{mouldid}' has been deleted."}
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))
    
@router.get("/list-mouldids", tags=["Mould APIs"])
def get_mouldids(current_user: Dict = Depends(get_current_user)):
    """Get a list of distinct mould IDs."""
    try:
        mouldids = db.list_mouldids()
        return {"mouldids": mouldids}
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))
    
@router.get("/read-moulds-file", tags=["Mould APIs"])
def read_file(current_user: Dict = Depends(get_current_user)):
    try:
        # Read the Excel file without letting pandas treat the first row as headers
        df = pd.read_excel(MOULDS_FILE_PATH, header=None, engine='openpyxl')

        # Column H is column index 7 (zero-based indexing)
        # Row 3 in Excel is index 2 (zero-based)
        column_h_values = df.iloc[2:, 7].dropna().astype(str).str.strip().tolist()

        return {"mould-names": column_h_values}
    except FileNotFoundError:
        raise HTTPException(status_code=404, detail="File not found")
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))
    
@router.get("/read-moulds-file-whole", tags=["Mould APIs"])
def read_file_whole(current_user: Dict = Depends(get_current_user)):
    try:
        # Define your custom headers here
        custom_headers = [
            "Year", "Order Number", "Client", "Description",
            "No. of Moulds", "No. of Cavity", "Volume", "Name"
        ]

        # Read the Excel without headers
        df = pd.read_excel(MOULDS_FILE_PATH, header=None, engine='openpyxl')

        # Drop completely empty rows and columns
        df_cleaned = df.dropna(how='all').dropna(axis=1, how='all')

        # Assign headers to cleaned data (must match the number of columns in df_cleaned)
        df_cleaned.columns = custom_headers[:df_cleaned.shape[1]]

        # Optionally drop the first few rows if they contain non-data content
        df_data_only = df_cleaned[1:]  # skip first two rows if they are metadata

        # Strip whitespace and convert to list of dictionaries
        df_data_only = df_data_only.applymap(lambda x: str(x).strip())
        data_dicts = df_data_only.to_dict(orient="records")

        return {"data": data_dicts}
    except FileNotFoundError:
        raise HTTPException(status_code=404, detail="File not found")
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

# Add a new row  
@router.post("/add-mould-row", tags=["Mould APIs"])
def add_mould_row(
    new_row: Dict = Body(...),
    current_user: Dict = Depends(get_current_user)
):
    try:
        # 1) Define headers and load the sheet
        custom_headers = [
            "Year", "Order Number", "Client", "Description",
            "No. of Moulds", "No. of Cavity", "Volume", "Name"
        ]
        df = pd.read_excel(MOULDS_FILE_PATH, header=None, engine='openpyxl')
        df_cleaned = df.dropna(how='all').dropna(axis=1, how='all')
        df_cleaned.columns = custom_headers[:df_cleaned.shape[1]]
        
        # 2) Pull out just the data rows (skip first two metadata rows)
        df_data_only = df_cleaned[2:].reset_index(drop=True)
        
        # 3) Determine Order Number with uniqueness check
        existing_orders = df_data_only["Order Number"].astype(str).tolist()
        requested = str(new_row.get("Order Number", "")).strip()
        is_maintenance = False

        if requested.lower() == "maintenance":
            maintenance_nums = [
                int(x.split("-")[1]) for x in existing_orders
                if x.lower().startswith("maintenance-") and x.split("-")[1].isdigit()
            ]
            next_num = max(maintenance_nums, default=0) + 1
            order_number = f"Maintenance-{next_num}"
            is_maintenance = True
        elif requested:
            if requested in existing_orders:
                raise HTTPException(status_code=400, detail=f"Order Number '{requested}' already exists.")
            order_number = requested
        else:
            numeric_orders = [int(x) for x in existing_orders if x.isdigit()]
            order_number = str(max(numeric_orders, default=0) + 1)
            
        # Ensure 'Name' is unique
        new_name = str(new_row.get("Name", "")).strip()
        if not new_name:
            raise HTTPException(status_code=400, detail="Name is required.")

        existing_names = df_data_only["Name"].astype(str).str.strip().tolist()
        if new_name in existing_names:
            raise HTTPException(status_code=400, detail=f"Name '{new_name}' already exists.")

        # 4) Build and append the new record
        record = {col: "" for col in df_data_only.columns}
        for k, v in new_row.items():
            if k in record:
                record[k] = str(v).strip()
        record["Order Number"] = order_number

        # 5) Concatenate metadata + existing data + new row, then overwrite Excel
        df_out = pd.concat([
            df_cleaned.iloc[:2], 
            df_data_only, 
            pd.DataFrame([record])
        ], ignore_index=True)
        df_out.to_excel(MOULDS_FILE_PATH, header=False, index=False, engine='openpyxl')

        return {
            "message": "New mould row added",
            "order_number": order_number,
            "is_maintenance_number": is_maintenance
        }

    except FileNotFoundError:
        raise HTTPException(status_code=404, detail="File not found")
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

# DELETE row
@router.delete("/delete-mould-row/{order_number}", tags=["Mould APIs"])
def delete_mould_row(
    order_number: str,
    current_user: Dict = Depends(get_current_user)
):
    try:
        meta, df_data = _load_moulds()
        # Remove matching row(s)
        mask = df_data["Order Number"].astype(str) != order_number
        if mask.sum() == len(df_data):
            raise HTTPException(status_code=404, detail=f"No row with Order Number '{order_number}'")

        df_filtered = df_data[mask].reset_index(drop=True)

        # Reassemble and save
        out = pd.concat([meta, df_filtered], ignore_index=True)
        out.to_excel(MOULDS_FILE_PATH, header=False, index=False, engine='openpyxl')

        return {"message": f"Row '{order_number}' deleted successfully"}
    except FileNotFoundError:
        raise HTTPException(status_code=404, detail="File not found")
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))
    
# EDIT endpoint
@router.put("/edit-mould-row/{order_number}", tags=["Mould APIs"])
def edit_mould_row(
    order_number: str,
    updates: Dict = Body(...),
    current_user: Dict = Depends(get_current_user)
):
    try:
        meta, df_data = _load_moulds()

        # Locate the original row by order_number
        mask = df_data["Order Number"].astype(str) == order_number
        if not mask.any():
            raise HTTPException(status_code=404, detail=f"No row with Order Number '{order_number}'")
        idx = df_data[mask].index[0]

        existing_orders = df_data["Order Number"].astype(str).tolist()
        is_maintenance = False

        if "Order Number" in updates:
            requested = str(updates["Order Number"]).strip()

            if requested.lower() == "maintenance":
                # Auto-generate next available Maintenance-N
                maintenance_nums = [
                    int(x.split("-")[1]) for x in existing_orders
                    if x.lower().startswith("maintenance-") and x.split("-")[1].isdigit()
                ]
                next_num = max(maintenance_nums, default=0) + 1
                updates["Order Number"] = f"Maintenance-{next_num}"
                is_maintenance = True
            elif requested != order_number and requested in existing_orders:
                raise HTTPException(
                    status_code=400,
                    detail=f"Order Number '{requested}' already exists."
                )
                
        # Check for Name uniqueness if it's being updated
        if "Name" in updates:
            new_name = str(updates["Name"]).strip()
            existing_names = df_data["Name"].astype(str).str.strip()

            # Check if any other row has this name
            if any((existing_names == new_name) & (df_data.index != idx)):
                raise HTTPException(
                    status_code=400,
                    detail=f"Name '{new_name}' already exists in another row."
                )

        # Apply updates to the identified row
        for col, val in updates.items():
            if col not in df_data.columns:
                raise HTTPException(status_code=400, detail=f"Unknown column '{col}'")
            df_data.at[idx, col] = str(val).strip()

        # Reassemble and save
        out = pd.concat([meta, df_data], ignore_index=True)
        out.to_excel(MOULDS_FILE_PATH, header=False, index=False, engine='openpyxl')

        return {
            "message": f"Row '{order_number}' updated successfully",
            "new_order_number": updates.get("Order Number", order_number),
            "is_maintenance_number": is_maintenance
        }

    except FileNotFoundError:
        raise HTTPException(status_code=404, detail="File not found")
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))
    
@router.get("/read-channels-file", tags=["Mould APIs"])
def read_channels_from_excel():
    try:
        # Read the Excel file without treating any row as header
        df = pd.read_excel(CHANNELS_FILE_PATH, header=None, engine='openpyxl')

        # Column A is index 0, Row 2 is index 1
        column_values = df.iloc[1:, 0].dropna().astype(str).str.strip().tolist()

        return {"channel-names": column_values}
    except FileNotFoundError:
        raise HTTPException(status_code=404, detail="File not found")
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))
                                
@router.get("/read-diameters-file", tags=["Mould APIs"])
def read_file(current_user: Dict = Depends(get_current_user)):
    try:
        # Read the Excel file without treating any row as header
        df = pd.read_excel(DIAMETERS_FILE_PATH, header=None, engine='openpyxl')

        # Column A is index 0, Row 2 is index 1
        column_values = df.iloc[1:, 0].dropna().astype(str).str.strip().tolist()

        return {"hydraulic-diameters": column_values}
    except FileNotFoundError:
        raise HTTPException(status_code=404, detail="File not found")
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))
    
@router.get("/latest-mould-event/{mouldid}", tags=["Mould APIs"])
def get_latest_mould_event(mouldid: str,current_user: Dict = Depends(get_current_user)):
    """Get a list of distinct mould IDs."""
    try:
        msg = db.fetch_latest_mould_event(mouldid)
        return {"mould_event": msg}
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))
    
@router.get("/latest-mould-test-event/{mouldid}", tags=["Mould APIs"])
def get_latest_mould_test_event(mouldid: str,current_user: Dict = Depends(get_current_user)):
    """Get a list of distinct mould IDs."""
    try:
        msg = db.fetch_events_from_last_start(mouldid)
        return {"mould_events": msg}
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/mould-event-timestamped/{mouldid}/{from_timestamp}/{to_timestamp}", tags=["Mould APIs"])
def get_mould_events(
    mouldid: str,
    from_timestamp: str,
    to_timestamp: str,
    current_user: Dict = Depends(get_current_user),
):
    """Get a list of mould events within a time range (passed as path parameters)."""
    try:
        # Convert string timestamps to datetime objects
        try:
            from_dt = datetime.fromisoformat(from_timestamp)
            to_dt = datetime.fromisoformat(to_timestamp)
        except ValueError:
            raise HTTPException(status_code=400, detail="Invalid timestamp format. Use ISO format: YYYY-MM-DDTHH:MM:SS")

        if from_dt > to_dt:
            raise HTTPException(status_code=400, detail="from_timestamp must be earlier than to_timestamp")

        events = db.fetch_mould_events_times(mouldid, from_dt, to_dt)
        return {"mould_events_timestamp": events}
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))
    
@router.get("/mould-test-history/{mouldid}", tags=["Mould APIs"])
def get_start_stop_events(mouldid: str, current_user: Dict = Depends(get_current_user)):
    """Get all start and stop timestamp pairs for the given mouldid, ignoring other messages."""
    try:
        pairs = db.fetch_mould_start_stop_pairs(mouldid)
        return {"mould_history": pairs}
    
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))