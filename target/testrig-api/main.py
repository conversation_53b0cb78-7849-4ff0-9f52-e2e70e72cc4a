from fastapi import FastAPI, Depends, HTTPException, status
from fastapi.middleware.cors import CORSMiddleware
from fastapi.security import OAuth2PasswordRe<PERSON>Form
from auth.auth import authenticate_user, create_access_token  # Import from auth.py
from config.config import router as config_router  # Import router from config.py
from pump.pump import router as pump_router  # Import router from config.py
from relay.relay import router as relay_router  # Import router from config.py
from mould.mould import router as mould_router # Import router from mould.py
from reader.reader import router as reader_router # Import router from reader.py
from auth.auth import router as auth_router # Import router from auth.py
from tests.test import router as test_router # Import router from test.py
from graphs.graphs import router as graphs_router # Import router from graphs.py
from service.service import router as system_router # Import router from system.py
import os

ENV = os.getenv('ENV', 'production') # Default to 'development'

app = FastAPI(
    docs_url=None if ENV == "production" else "/docs", # Disable Swagger UI in production
    redoc_url=None if ENV == "production" else "/redoc", # Disable Redoc in production
    openapi_url=None if ENV == "production" else "/openapi.json", # Disable OpenAPI schema in production
    openapi_tags=[
        {
            "name": "Configuration APIs",
            "description": "APIs to handle testrig configuration.",
        },
        {
            "name": "PUMP APIs",
            "description": "APIs to handle PUMP communication",
        },
        {
            "name": "RELAY APIs",
            "description": "APIs to handle RELAY interactions",
        },
        {
            "name": "Mould APIs",
            "description": "APIs to handle Mould settings",
        },
        {
            "name": "Reader APIs",
            "description": "APIs to handle manifold real time data communication",
        },
        {
            "name": "Graph APIs",
            "description": "APIs to handle graph generation",
        },
        {
            "name": "Service APIs",
            "description": "APIs to handle addtional services",
        },
        {
            "name": "Test APIs",
            "description": "APIs to handle tests",
        },
        {
            "name": "Authentication API",
            "description": "APIs to handle user authentication",
        }
        
    ])

origins = [
    "*"

]

app.add_middleware(
    CORSMiddleware,
    allow_origins=origins,  # Allows CORS for the specified origins
    allow_credentials=True,
    allow_methods=["*"],  # Allows all HTTP methods (GET, POST, etc.)
    allow_headers=["*"],  # Allows all headers
)

# Include config router
app.include_router(config_router)

# Include pump router
app.include_router(pump_router)

# Include relay router
app.include_router(relay_router)

# Include mould router
app.include_router(mould_router)

# Include reader router
app.include_router(reader_router)

# Include auth router
app.include_router(auth_router)

# Include auth router
app.include_router(test_router)

# Include graph router
app.include_router(graphs_router)

# Include system router
app.include_router(system_router)
