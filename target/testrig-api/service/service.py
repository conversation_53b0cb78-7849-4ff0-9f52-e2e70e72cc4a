from fastapi import FastAP<PERSON>, APIRouter, Depends, HTTPException
import subprocess
import os
from typing import Dict
from auth.auth import get_current_user

router = APIRouter()

SSH_USER = os.getenv("SSH_USER", "root")  # Use root or another user with sudo privileges
SSH_HOST = os.getenv("SSH_HOST", "host.docker.internal")
SERVICE_NAME = "ssh-tunnel"

def manage_ssh_tunnel(action: str):
    """Start or stop the ssh-tunnel service on the host via SSH"""
    if action not in ["start", "stop"]:
        return {"status": "error", "message": "Invalid action"}

    try:
        # Command to run systemctl on the host via SSH
        command = [
            "ssh",
            "-i", "/root/.ssh/id_rsa",
            "-o", "StrictHostKeyChecking=no",
            "-o", "UserKnownHostsFile=/dev/null",
            f"{SSH_USER}@{SSH_HOST}",
            f"systemctl {action} {SERVICE_NAME}"
        ]
        subprocess.run(command, check=True)
        return {"status": "success", "message": f"ssh-tunnel service {action}ed"}
    except subprocess.CalledProcessError as e:
        return {"status": "error", "message": str(e)}
    
def get_ssh_tunnel_status():
    """Check if the ssh-tunnel service is running on the host"""
    try:
        command = [
            "ssh",
            "-i", "/root/.ssh/id_rsa",
            "-o", "StrictHostKeyChecking=no",
            "-o", "UserKnownHostsFile=/dev/null",
            f"{SSH_USER}@{SSH_HOST}",
            f"systemctl is-active {SERVICE_NAME}"
        ]
        result = subprocess.run(command, capture_output=True, text=True, check=False)
        status = result.stdout.strip()

        return {"status": "success", "service_status": status}
    except Exception as e:
        return {"status": "error", "message": str(e)}

@router.post("/ssh-tunnel/start", tags=["Service APIs"])
def start_ssh_tunnel(current_user: Dict = Depends(get_current_user)):
    return manage_ssh_tunnel("start")

@router.post("/ssh-tunnel/stop", tags=["Service APIs"])
def stop_ssh_tunnel(current_user: Dict = Depends(get_current_user)):
    return manage_ssh_tunnel("stop")

@router.get("/ssh-tunnel/status", tags=["Service APIs"])
def ssh_tunnel_status(current_user: Dict = Depends(get_current_user)):
    return get_ssh_tunnel_status()