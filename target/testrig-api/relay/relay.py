import serial
from fastapi import APIRouter, Depends, HTTPException
from typing import Dict
from auth.auth import get_current_user
import os
import psycopg2
from psycopg2.extras import DictCursor

class DBConnect:
    def __init__(self, host, database, user, password):
        """Initialize the database connection."""
        try:
            self.conn = psycopg2.connect(
                host=host,
                database=database,
                user=user,
                password=password
            )
            self.cursor = self.conn.cursor(cursor_factory=DictCursor)
        except psycopg2.Error as e:
            print(f"Database connection error: {e}")
            self.conn = None

# Serial port configuration
serial_port = os.getenv("SERIAL_PORT_RELAY", "/dev/ttyUSB0")
baud_rate = 115200
timeout = 1

serial_port_addon =  os.getenv("SERIAL_PORT_ADDON_RELAY", "/dev/ttyACM0")
baud_rate_addon = 9600
timeout_addon = 1

router = APIRouter()

# Helper function to save the relay to the database
def log_valve_event(relay_id: str, state: int):
    db = DBConnect(host='postgres', 
                   database=os.environ.get('POSTGRES_DB'),
                   user=os.environ.get('POSTGRES_USER'),
                   password=os.environ.get('POSTGRES_PASSWORD'))


    if not db.conn:
        print("Database connection failed. Event not logged.")
        return

    try:
        insert_query = """
        INSERT INTO valve_events (time, relayID, state)
        VALUES (NOW(), %s, %s)
        ON CONFLICT (relayID) DO UPDATE SET
            time = EXCLUDED.time,
            state = EXCLUDED.state;
        """
        db.cursor.execute(insert_query, (relay_id, state))
        db.conn.commit()
        print(f"Logged event: Relay {relay_id}, State {state}")
    except Exception as e:
        print(f"Error logging valve event: {e}")
        db.conn.rollback()
    finally:
        db.cursor.close()
        db.conn.close()

# Initialize serial connections
try:
    ser = serial.Serial(serial_port, baud_rate, timeout=timeout)
    print(f"Connected to {serial_port} at {baud_rate} baud.")
    
    ser_addon = serial.Serial(serial_port_addon, baud_rate_addon, timeout=timeout_addon)
    print(f"Connected to {serial_port_addon} at {baud_rate_addon} baud.")
except serial.SerialException as e:
    ser, ser_addon = None, None
    print(f"Error initializing serial connections: {e}")


def send_command(ser_instance, command):
    """
    Send a command to the specified serial connection and return the response.
    """
    try:
        ser_instance.write(command)
        response = ser_instance.readline().decode().strip()
        return response
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Serial communication error: {e}")
    
def addon_status(ser_instance):
    try:
        ser_instance.write(b'\x5B')  # Send '[' (0x5B) command to request relay status
        status = ser_instance.read(1)  # Read 1 byte of response

        if status:
            print(f"Relay Status Byte: {bin(status[0])}")  # Show status in binary
            return bin(status[0])[2:]
        else:
            print("No response from relay module.")
            return None
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Serial communication error: {e}")
    
    
@router.post("/relay/main/{relay_number}/{state}", tags=["RELAY APIs"])
async def control_main_relay(relay_number: int, state: str, current_user: Dict = Depends(get_current_user)):
    """
    Control a relay on the main board.
    """
    if not (1 <= relay_number <= 7):
        raise HTTPException(status_code=400, detail="Relay number must be between 1 and 7.")
    if state not in ["ON", "OFF"]:
        raise HTTPException(status_code=400, detail="State must be 'ON' or 'OFF'.")
    if not ser:
        raise HTTPException(status_code=500, detail="Main serial connection not initialized.")
    
    command = f"{'set' if state == 'ON' else 'clear'} {relay_number}\r".encode()
    response = send_command(ser, command)
    
    # Save to database after successful command
    relay_id = f"main_{relay_number}"
    state_int = 1 if state == "ON" else 0
    log_valve_event(relay_id, state_int)
    
    return {"relay_number": relay_number, "state": state, "response": response, "controlled_by": current_user.get("sub", "Unknown")}


@router.post("/relay/addon/{relay_number}/{state}",tags=["RELAY APIs"])
async def control_addon_relay(relay_number: str, state: str, current_user: Dict = Depends(get_current_user)):
    """
    Control a relay on the add-on board.
    """
    valid_relays = [f"r{i}" for i in range(1, 9)] + ["all"]
    if relay_number not in valid_relays:
        raise HTTPException(status_code=400, detail="Invalid relay number. Use r1-r8 or 'all'.")
    if state not in ["ON", "OFF"]:
        raise HTTPException(status_code=400, detail="State must be 'ON' or 'OFF'.")
    if not ser_addon:
        raise HTTPException(status_code=500, detail="Add-on serial connection not initialized.")
    
    predefined_strings = ["all", "r1", "r2", "r3", "r4", "r5", "r6", "r7", "r8", "help"]
    action_strings = ["ON", "OFF"]
    
    byte_value = predefined_strings.index(relay_number) + 100
    byte_value += action_strings.index(state) * 10
    command = byte_value.to_bytes(1, 'big')
    
    response = send_command(ser_addon, command)
    
    # Save to database after successful command
    relay_id = f"addon_{relay_number}"
    state_int = 1 if state == "ON" else 0
    log_valve_event(relay_id, state_int)
    
    return {"relay_number": relay_number, "state": state, "response": response, "controlled_by": current_user.get("sub", "Unknown")}

@router.get("/relay/addon/status",tags=["RELAY APIs"])
async def control_addon_relay_status(current_user: Dict = Depends(get_current_user)):
    """
    Get status of the add-on board.
    """    
    response = addon_status(ser_addon)
    return { "response": response, "controlled_by": current_user.get("sub", "Unknown")}


@router.post("/led/{action}/{color}", tags=["RELAY APIs"])
async def control_led(action: str, color: str, current_user: Dict = Depends(get_current_user)):
    """
    Control an LED on the main board.
    """
    valid_actions = {"set_led", "blink_led", "clear_led"}
    valid_colors = {"r", "y", "g"}

    if action not in valid_actions:
        raise HTTPException(status_code=400, detail="Invalid action. Use 'set_led', 'blink_led', or 'clear_led'.")
    if color not in valid_colors:
        raise HTTPException(status_code=400, detail="Invalid LED color. Use 'r', 'y', or 'g'.")
    if not ser:
        raise HTTPException(status_code=500, detail="Main serial connection not initialized.")
    
    command = f"{action} {color}\r".encode()
    response = send_command(ser, command)
    return {"action": action, "color": color, "response": response, "controlled_by": current_user.get("sub", "Unknown")}


@router.get("/inputs", tags=["RELAY APIs"])
async def get_input_values(current_user: Dict = Depends(get_current_user)):
    """
    Fetch input values from the main board.
    """
    if not ser:
        raise HTTPException(status_code=500, detail="Main serial connection not initialized.")
    
    command = "m\r".encode()
    response = send_command(ser, command)
    if response:
        return {"inputs": response, "controlled_by": current_user.get("sub", "Unknown")}
    else:
        raise HTTPException(status_code=500, detail="No response or invalid response received.")
   
@router.on_event("shutdown") 
async def shutdown_event():
    """
    Close serial connections on shutdown.
    """
    if ser and ser.is_open:
        ser.close()
        print("Serial connection closed: ttyUSB0.")
    if ser_addon and ser_addon.is_open:
        ser_addon.close()
        print("Serial connection closed: ttyACM0.")


