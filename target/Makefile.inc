# -*- Makefile -*-
#
# Common docker app Makefile
#
# Make targets available:
#   push-development          - push images to development repository
#   push-production           - push images to production repository
#   development               - all of the above for development
#   production                - all of the above for production

.PHONY: all push-arm32v7

REGISTRY_PREFIX ?= eu.gcr.io/mouldlive/arm32v7

VER ?= latest
REPOS_VER ?= latest
REPOS_X86_RUNTIME ?= $(REPOS_X86)
IMAGE_NAME ?= $(MODULE)

all: ${IMAGE_NAME} deps

${IMAGE_NAME}:
    docker build . \
        --build-arg MODULE=$(MODULE) \
        --build-arg REPOS_RUNTIME=$(REPOS_X86_RUNTIME) \
        --build-arg REPOS_VER=${REPOS_VER} \
        --build-arg REPOS=${REPOS_X86} \
        --build-arg ARCH=arm32v7 \
        -t $(REGISTRY_PREFIX)/prod/${IMAGE_NAME}:${VER} \
        -t $(REGISTRY_PREFIX)/dev/${IMAGE_NAME}:${VER} \
        -f ${DOCKER_FILE}

docker-clean:
    - [ ! -z "$$(docker ps -a -q)" ] && docker rm -f $$(docker ps -a -q)
    - [ ! -z "$$(docker images | grep none | awk '{print $$3}')" ] && \
        docker rmi -f $$(docker images | grep none | awk '{print $$3}')

gcloud-login-registry:
    gcloud auth print-access-token | docker login -u oauth2accesstoken --password-stdin https://eu.gcr.io

push-development: ${IMAGE_NAME}
    docker push ${REGISTRY_PREFIX}/dev/${MODULE}:${VER}

push-production: ${IMAGE_NAME}
    docker push ${REGISTRY_PREFIX}/prod/${MODULE}:${VER}

help:
    @echo ""
    @echo "  all                       - build docker images"
    @echo "  push-development          - push images to development repository"
    @echo "  push-production           - push images to production repository"
    @echo "  development               - all of the above for development"
    @echo "  production                - all of the above for production"
    @echo ""
