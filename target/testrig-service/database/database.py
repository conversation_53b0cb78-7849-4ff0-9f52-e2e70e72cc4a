import psycopg2
from psycopg2.extras import DictCursor

class DBConnect:
    def __init__(self, host, database, user, password):
        """Initialize the database connection."""
        try:
            self.conn = psycopg2.connect(
                host=host,
                database=database,
                user=user,
                password=password
            )
            self.cursor = self.conn.cursor(cursor_factory=DictCursor)
        except psycopg2.Error as e:
            print(f"Database connection error: {e}")
            self.conn = None

    def save_mould_event_msg(self, mouldid, msg):
        """Fetch the latest rows from the samples table."""
        if not self.conn:
            print("No database connection.")
            return []
        
        try:
            insert_query = """
            INSERT INTO mould_events (time, mouldid, msg)
            VALUES (NOW(), %s, %s)
            """
            self.cursor.execute(insert_query, (mouldid, msg))
            self.conn.commit()
            print(f"Logged event: Relay {mouldid}, State {msg}")
        except Exception as e:
            print(f"Error logging valve event: {e}")
            self.conn.rollback()
        
    def fetch_tests_for_mould(self, mouldid):
        """ Order the return test"""
        priority_order = ["flow_test", "air_leak_test", "water_leak_test", "heat_test"]
        
        try:
            cur = self.conn.cursor(cursor_factory=psycopg2.extras.RealDictCursor)
            cur.execute("SELECT tests FROM mould_data WHERE mouldid = %s;", (mouldid,))
            row = cur.fetchone()
            cur.close()
            
            if row and 'tests' in row:
                enabled_tests = [k for k, v in row['tests'].items() if v is True]
                
                # Sort enabled tests based on the defined priority order
                sorted_tests = [test for test in priority_order if test in enabled_tests]
                
                if sorted_tests:
                    # Return a dictionary with test names in sorted order
                    return {test: True for test in sorted_tests}
                else:
                    return None

        except Exception as e:
            print(f"Error fetching tests for mould {mouldid}: {e}")
            return None


    def fetch_pressure_set_points(self, mouldid):
        """
        Fetch 'pressure_set_point_min' and 'pressure_set_point_max' for a specific mouldid.
        """
        try:
            cur = self.conn.cursor(cursor_factory=psycopg2.extras.RealDictCursor)
            cur.execute(
                "SELECT pressure_set_point_min, pressure_set_point_max FROM mould_data WHERE mouldid = %s;",
                (mouldid,)
            )
            row = cur.fetchone()
            cur.close()
            return row if row else None
        except Exception as e:
            print(f"Error fetching pressure set points for mould {mouldid}: {e}")
            return None
        
    def outlet_pressure(self):
        """Fetch the latest rows from the samples table."""
        try:
            cur = self.conn.cursor(cursor_factory=psycopg2.extras.RealDictCursor)
            query = "SELECT data_14_0 FROM samples ORDER BY time DESC LIMIT 1;"
            cur.execute(query)
            row = cur.fetchone()
            return dict(row) if row else None
        except psycopg2.Error as e:
            print(f"Query execution error: {e}")
            return None
        
    def fetch_max_water_pressure(self, mouldid):
        """
        Fetch max water pressure for the specified mouldid.
        """
        try:
            cur = self.conn.cursor(cursor_factory=psycopg2.extras.DictCursor)
            select_query = "SELECT max_water_pressure FROM mould_data WHERE mouldid = %s;"
            cur.execute(select_query, (mouldid,))
            row = cur.fetchone()
            cur.close()
            return row if row else None
        except Exception as e:
            print("Error in fetch_data:", e)
            return None