from transitions import Machine
import valkey
import requests
import time
import math
import os 
import jwt

class WaterEmpty:
    # Define states
    states = ["idle", "starting", "running", "stopped" ,"completed", "error"]
    
    def __init__(self):
        self.machine = Machine(model=self, states=WaterEmpty.states, initial="idle")
        self.mouldid = None       
        self.testrig_api_url = "http://testrig-api:8000/"
        self.valkey_client = valkey.Valkey(host="valkey", port=6379)
        self.token_url = "http://testrig-api:8000/token"  # Adjust endpoint if different
        self.auth_token = None
        self.token_expiry = 0
        self.running = False
        
        # Define transitions
        self.machine.add_transition(trigger="start", source="idle", dest="starting", after="on_start")
        self.machine.add_transition(trigger="run", source="starting", dest="running", after="on_run")
        self.machine.add_transition(trigger="stop", source="*", dest="stopped", after="on_stop")
        self.machine.add_transition(trigger="complete", source="running", dest="completed", after="on_complete")
        self.machine.add_transition(trigger="fail", source="*", dest="error", after="on_fail")
        self.machine.add_transition(trigger="reset", source=["error", "stopped", "completed"], dest="idle", after="on_reset")
        
    def get_auth_token(self):
        """
        Generate and cache the authentication token using hardcoded secret key.
        """
        SECRET_KEY = "ZJrUo3Wig7yCbO33P_BzMmd4eyCZ-laQfYY0L9Bz3vVGtGdTtYYupO-nSxur402pe2OcJg2uV3nSalnPPrkmAg%"
        ALGORITHM = "HS256"
        EXPIRY_MINUTES = 120

        current_time = time.time()

        if self.auth_token and current_time < self.token_expiry:
            return self.auth_token

        print("Generating new auth token...")

        try:
            payload = {
                "sub": "client_auth",           # subject (can be customized)
                "iat": int(current_time),        # issued at
                "exp": int(current_time) + EXPIRY_MINUTES * 60  # expiry time
            }

            token = jwt.encode(payload, SECRET_KEY, algorithm=ALGORITHM)

            self.auth_token = token
            self.token_expiry = current_time + EXPIRY_MINUTES * 60 - 60  # refresh 1 min before expiry

            print(f"Token generated! Expires in {EXPIRY_MINUTES * 60} seconds.")
            return self.auth_token

        except Exception as e:
            print(f"Exception when generating token: {e}")
            return None
        
    
    def start_test(self):
        """
        Start the water empty process.
        """
        print("Starting water empty")
        self.start()  # Call the original 'start' trigger
        
    def call_testrig_api(self, endpoint: str, method: str = "POST", retry: bool = True):
        url = f"{self.testrig_api_url}{endpoint}"
        token = self.get_auth_token()

        if not token:
            print("No valid token available. Aborting API call.")
            return None

        headers = {
            "Authorization": f"Bearer {token}"
        }

        try:
            if method.upper() == "GET":
                response = requests.get(url, headers=headers)
            else:
                response = requests.post(url, headers=headers)

            if response.status_code == 200:
                print(f"Response from {url}: {response.json()}")
                return response.json()

            elif response.status_code == 401 and retry:
                print("Unauthorized! Refreshing token and retrying...")
                self.auth_token = None
                return self.call_testrig_api(endpoint, method, retry=False)

            else:
                print(f"Failed API call: {response.status_code} - {response.text}")
                return None

        except requests.RequestException as e:
            print(f"Error calling testrig API: {e}")
            return None

    def publish_status(self):
        """Publish the current state to Valkey (Redis)."""
        channel = "recycle_status"
        message = f"water-empty:{self.state}"
        self.valkey_client.publish(channel, message)
        print(f"Published to {channel}: {message}")
           
    def on_start(self):
        """Logic when start is triggered."""
        self.running = True
        self.publish_status()
        
        # Stop pump if running
        self.call_testrig_api("stop")
        self.call_testrig_api("relay/main/4/OFF")
               
        if self.running:
            self.run()
    
  
    def on_run(self):
        print("Water empty is running...")
        self.publish_status()
        
        # Empty water
        self.call_testrig_api("relay/addon/r4/OFF")
        for _ in range(5):
            if not self.running:
                return
            time.sleep(1)
            
        self.call_testrig_api("relay/main/5/ON")
        
        while True:
            try:
                # Replace this with your actual API call
                response = self.call_testrig_api("inputs", method="GET")
                
                # Ensure response is valid
                if response and "inputs" in response:
                    inputs_list = response["inputs"].strip().split()
                    lower_level_sensor = inputs_list[-2]

                    if lower_level_sensor == '0':
                        print("Lower level sensor is OFF. Triggering Purge OFF.")
                        self.call_testrig_api("relay/main/5/OFF")
                        self.call_testrig_api("relay/addon/r4/ON")
                        for _ in range(5):
                            if not self.running:
                                return
                            time.sleep(1)
                        break
                    else:
                        print("Lower level sensor is ON. No action needed.")

                else:
                    print("Invalid response or 'inputs' missing.")

            except Exception as e:
                print(f"Error while checking sensor: {e}")
                
            if not self.running:
                return

            time.sleep(1)  # Adjust the interval as needed

        if self.running:
            self.complete()

        
    def on_stop(self):
        self.publish_status()
        self.running = False
        self.call_testrig_api("relay/main/5/OFF")
        self.call_testrig_api("relay/addon/r4/ON")
        print("Water Empty stopped.")
        self.reset()

    def on_complete(self):
        self.publish_status()
        self.call_testrig_api("relay/main/5/OFF")
        self.call_testrig_api("relay/addon/r4/ON")
        self.running = False
        print("Water Empty completed successfully.")

    def on_fail(self):
        self.publish_status()
        self.running = False
        self.call_testrig_api("relay/main/5/OFF")
        self.call_testrig_api("relay/addon/r4/ON")
        print("Water Empty encountered an error.")
        self.reset()

    def on_reset(self):
        self.state = "idle"
        self.running = False
        self.publish_status()
        print("Water Empty reset. Ready to start again.")
    
    def is_complete(self):
        return self.state == "completed"

    def handle_message(self, message):
        """Trigger state transition based on message."""
        if message == "start":
            self.start()
        elif message == "run":
            self.run()
        elif message == "stop":
            self.stop()
        elif message == "complete":
            self.complete()
        elif message == "fail":
            self.fail()
        elif message == "reset":
            self.reset()
        print(f"Current State: {self.state}")
        
class WaterFill:
    # Define states
    states = ["idle", "starting", "running", "stopped" ,"completed", "error"]
    
    def __init__(self):
        self.machine = Machine(model=self, states=WaterFill.states, initial="idle")
        self.mouldid = None
        self.testrig_api_url = "http://testrig-api:8000/"
        self.valkey_client = valkey.Valkey(host="valkey", port=6379)
        self.token_url = "http://testrig-api:8000/token"  # Adjust endpoint if different
        self.auth_token = None
        self.token_expiry = 0
        self.running = False
        
        # Define transitions
        self.machine.add_transition(trigger="start", source="idle", dest="starting", after="on_start")
        self.machine.add_transition(trigger="run", source="starting", dest="running", after="on_run")
        self.machine.add_transition(trigger="stop", source="*", dest="stopped", after="on_stop")
        self.machine.add_transition(trigger="complete", source="running", dest="completed", after="on_complete")
        self.machine.add_transition(trigger="fail", source="*", dest="error", after="on_fail")
        self.machine.add_transition(trigger="reset", source=["error", "stopped", "completed"], dest="idle", after="on_reset")
        
    def get_auth_token(self):
        """
        Generate and cache the authentication token using hardcoded secret key.
        """
        SECRET_KEY = "ZJrUo3Wig7yCbO33P_BzMmd4eyCZ-laQfYY0L9Bz3vVGtGdTtYYupO-nSxur402pe2OcJg2uV3nSalnPPrkmAg%"
        ALGORITHM = "HS256"
        EXPIRY_MINUTES = 120

        current_time = time.time()

        if self.auth_token and current_time < self.token_expiry:
            return self.auth_token

        print("Generating new auth token...")

        try:
            payload = {
                "sub": "client_auth",           # subject (can be customized)
                "iat": int(current_time),        # issued at
                "exp": int(current_time) + EXPIRY_MINUTES * 60  # expiry time
            }

            token = jwt.encode(payload, SECRET_KEY, algorithm=ALGORITHM)

            self.auth_token = token
            self.token_expiry = current_time + EXPIRY_MINUTES * 60 - 60  # refresh 1 min before expiry

            print(f"Token generated! Expires in {EXPIRY_MINUTES * 60} seconds.")
            return self.auth_token

        except Exception as e:
            print(f"Exception when generating token: {e}")
            return None
        
    
    def start_test(self):
        """
        Start the water fill process.
        """
        print("Starting water fill")
        self.start()  # Call the original 'start' trigger
        
    def call_testrig_api(self, endpoint: str, method: str = "POST", retry: bool = True):
        url = f"{self.testrig_api_url}{endpoint}"
        token = self.get_auth_token()

        if not token:
            print("No valid token available. Aborting API call.")
            return None

        headers = {
            "Authorization": f"Bearer {token}"
        }

        try:
            if method.upper() == "GET":
                response = requests.get(url, headers=headers)
            else:
                response = requests.post(url, headers=headers)

            if response.status_code == 200:
                print(f"Response from {url}: {response.json()}")
                return response.json()

            elif response.status_code == 401 and retry:
                print("Unauthorized! Refreshing token and retrying...")
                self.auth_token = None
                return self.call_testrig_api(endpoint, method, retry=False)

            else:
                print(f"Failed API call: {response.status_code} - {response.text}")
                return None

        except requests.RequestException as e:
            print(f"Error calling testrig API: {e}")
            return None

    def publish_status(self):
        """Publish the current state to Valkey (Redis)."""
        channel = "recycle_status"
        message = f"water-fill:{self.state}"
        self.valkey_client.publish(channel, message)
        print(f"Published to {channel}: {message}")
           
    def on_start(self):
        """Logic when start is triggered."""
        self.running = True
        self.publish_status()
        
        # Stop pump if running
        self.call_testrig_api("stop")
        self.call_testrig_api("relay/main/5/OFF")
               
        if self.running:
            self.run()
    
  
    def on_run(self):
        print("Water fill is running...")
        self.publish_status()
        
        # Empty water
        self.call_testrig_api("relay/addon/r4/ON")
        for _ in range(5):
            if not self.running:
                return
            time.sleep(1)
        self.call_testrig_api("relay/main/4/ON")
        
        while True:
            try:
                # Replace this with your actual API call
                response = self.call_testrig_api("inputs", method="GET")
                
                # Ensure response is valid
                if response and "inputs" in response:
                    inputs_list = response["inputs"].strip().split()
                    upper_level_sensor = inputs_list[-3]

                    if upper_level_sensor == '0':
                        print("Upper level sensor is ON. Triggering Fill OFF.")
                        self.call_testrig_api("relay/main/4/OFF")
                        break
                    else:
                        print("Upper level sensor is OFF. No action needed.")

                else:
                    print("Invalid response or 'inputs' missing.")

            except Exception as e:
                print(f"Error while checking sensor: {e}")
                
            if not self.running:
                return

            time.sleep(1)  # Adjust the interval as needed
        
        if self.running:
            self.complete()
        

        
    def on_stop(self):
        self.publish_status()
        self.running = False
        self.call_testrig_api("relay/main/4/OFF")
        print("Water Fill stopped.")
        self.reset()

    def on_complete(self):
        self.publish_status()
        self.call_testrig_api("relay/main/4/OFF")
        self.running = False
        print("Water Fill completed successfully.")

    def on_fail(self):
        self.publish_status()
        self.running = False
        self.call_testrig_api("relay/main/4/OFF")
        print("Water Fill encountered an error.")
        self.reset()

    def on_reset(self):
        self.state = "idle"
        self.running = False
        self.publish_status()
        print("Water Fill reset. Ready to start again.")
    
    def is_complete(self):
        return self.state == "completed"

    def handle_message(self, message):
        """Trigger state transition based on message."""
        if message == "start":
            self.start()
        elif message == "run":
            self.run()
        elif message == "stop":
            self.stop()
        elif message == "complete":
            self.complete()
        elif message == "fail":
            self.fail()
        elif message == "reset":
            self.reset()
        print(f"Current State: {self.state}")