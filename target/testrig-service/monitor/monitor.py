import requests
import time
import jwt

class Monitor:
    def __init__(self):
        self.testrig_api_url = "http://testrig-api:8000/"
        self.token_url = "http://testrig-api:8000/token"
        self.auth_token = None
        self.token_expiry = 0
        
    def get_auth_token(self):
        """
        Generate and cache the authentication token using hardcoded secret key.
        """
        SECRET_KEY = "ZJrUo3Wig7yCbO33P_BzMmd4eyCZ-laQfYY0L9Bz3vVGtGdTtYYupO-nSxur402pe2OcJg2uV3nSalnPPrkmAg%"
        ALGORITHM = "HS256"
        EXPIRY_MINUTES = 120

        current_time = time.time()

        if self.auth_token and current_time < self.token_expiry:
            return self.auth_token

        print("Generating new auth token...")

        try:
            payload = {
                "sub": "client_auth",           # subject (can be customized)
                "iat": int(current_time),        # issued at
                "exp": int(current_time) + EXPIRY_MINUTES * 60  # expiry time
            }

            token = jwt.encode(payload, SECRET_KEY, algorithm=ALGORITHM)

            self.auth_token = token
            self.token_expiry = current_time + EXPIRY_MINUTES * 60 - 60  # refresh 1 min before expiry

            print(f"Token generated! Expires in {EXPIRY_MINUTES * 60} seconds.")
            return self.auth_token

        except Exception as e:
            print(f"Exception when generating token: {e}")
            return None

    def call_testrig_api(self, endpoint: str, method: str = "POST", retry: bool = True):
        url = f"{self.testrig_api_url}{endpoint}"
        token = self.get_auth_token()

        if not token:
            print("No valid token available. Aborting API call.")
            return None

        headers = {"Authorization": f"Bearer {token}"}

        try:
            response = requests.request(method.upper(), url, headers=headers)

            if response.status_code == 200:
                return response.json()
            elif response.status_code == 401 and retry:
                print("Unauthorized! Refreshing token and retrying...")
                self.auth_token = None
                return self.call_testrig_api(endpoint, method, retry=False)
            else:
                print(f"Failed API call: {response.status_code} - {response.text}")
                return None
        except requests.RequestException as e:
            print(f"Error calling testrig API: {e}")
            return None

    def monitor_emergency(self):
        while True:
            response = self.call_testrig_api("inputs", "GET")
            if response and "inputs" in response:
                try:
                    # Split the string and convert to integers
                    input_values = list(map(int, response["inputs"].split()))

                    # Check the emergency signal (assuming it's the first input, index 0)
                    emergency = input_values[0]
                    if emergency == 1:
                        print("Emergency triggered. Executing shutdown sequence.")

                        # Stop the main pump
                        self.call_testrig_api("stop")

                        # Close TCU Valve and TCU
                        self.call_testrig_api("relay/addon/r3/OFF")
                        time.sleep(5)

                        self.call_testrig_api("relay/main/1/OFF")

                        # Close the purge pump
                        self.call_testrig_api("relay/main/5/OFF")

                        # Open the main valve
                        self.call_testrig_api("relay/addon/r1/ON")
                        self.call_testrig_api("relay/addon/r2/OFF")
                        time.sleep(25)

                        # Open Air and Water Inlet / Outlet Valves
                        self.call_testrig_api("relay/addon/r5/ON")
                        self.call_testrig_api("relay/addon/r6/ON")
                        self.call_testrig_api("relay/addon/r7/ON")
                        self.call_testrig_api("relay/addon/r8/ON")

                except (ValueError, IndexError) as e:
                    print(f"Failed to parse inputs: {response['inputs']} — {e}")
            else:
                print("No inputs found in API response.")

            time.sleep(1)


    def monitor_flow(self):
        while True:
            data = self.call_testrig_api("flow", "GET")
            if data:
                if not (data.get("1", 0) <= 400 and data.get("2", 0) <= 400 and 
                        all(data.get(str(i), 0) <= 40 for i in range(3, 11)) and 
                        data.get("11", 0) <= 20 and data.get("12", 0) <= 20):
                    self.call_testrig_api("stop")
            time.sleep(1)

