from transitions import Machine
import valkey
import requests
import time
import math
from database.database import DBConnect
import os 
import jwt
import numpy as np

# Instance of the database
db = DBConnect(host='postgres', database=os.environ.get('POSTGRES_DB'), user=os.environ.get('POSTGRES_USER'), password=os.environ.get('POSTGRES_PASSWORD'))

# State machine for flow test
class FlowTestStateMachine:
    # Define states
    states = ["idle", "starting", "running", "stopped" ,"completed", "error"]
    
    def __init__(self, db=None):
        self.machine = Machine(model=self, states=FlowTestStateMachine.states, initial="idle")
        self.mouldid = None       
        self.db = db
        self.testrig_api_url = "http://testrig-api:8000/"
        self.valkey_client = valkey.Valkey(host="valkey", port=6379)
        self.token_url = "http://testrig-api:8000/token"  # Adjust endpoint if different
        self.auth_token = None
        self.token_expiry = 0
        self.running = False
        self.stop_requested = False
        
        # Define transitions
        self.machine.add_transition(trigger="start", source="idle", dest="starting", after="on_start")
        self.machine.add_transition(trigger="run", source="starting", dest="running", after="on_run")
        self.machine.add_transition(trigger="stop", source="*", dest="stopped", after="on_stop")
        self.machine.add_transition(trigger="complete", source="running", dest="completed", after="on_complete")
        self.machine.add_transition(trigger="fail", source="*", dest="error", after="on_fail")
        self.machine.add_transition(trigger="reset", source=["error", "stopped", "completed", "idle"], dest="idle", after="on_reset")
        
    def get_auth_token(self):
        """
        Generate and cache the authentication token using hardcoded secret key.
        """
        SECRET_KEY = "ZJrUo3Wig7yCbO33P_BzMmd4eyCZ-laQfYY0L9Bz3vVGtGdTtYYupO-nSxur402pe2OcJg2uV3nSalnPPrkmAg%"
        ALGORITHM = "HS256"
        EXPIRY_MINUTES = 120

        current_time = time.time()

        if self.auth_token and current_time < self.token_expiry:
            return self.auth_token

        print("Generating new auth token...")

        try:
            payload = {
                "sub": "client_auth",           # subject (can be customized)
                "iat": int(current_time),        # issued at
                "exp": int(current_time) + EXPIRY_MINUTES * 60  # expiry time
            }

            token = jwt.encode(payload, SECRET_KEY, algorithm=ALGORITHM)

            self.auth_token = token
            self.token_expiry = current_time + EXPIRY_MINUTES * 60 - 60  # refresh 1 min before expiry

            print(f"Token generated! Expires in {EXPIRY_MINUTES * 60} seconds.")
            return self.auth_token

        except Exception as e:
            print(f"Exception when generating token: {e}")
            return None
        
    def custom_round(self,val):
        fractional_part = val - int(val)
        
        if fractional_part > 0.95:
            return math.ceil(val)  # Round UP to next integer
        else:
            return round(val, 1)   # Round to 1 decimal place (or keep as is)
            
        
    def start_test(self, pressure_set_point_min=1, pressure_set_point_max=3, flow_stabilization_timeout=20,flow_stabilization_limit=0.012 ):
        """
        Start the flow test with the given pressure set points.
        """
        self.pressure_set_point_min = pressure_set_point_min
        self.pressure_set_point_max = pressure_set_point_max
        self.flow_stabilization_limit = flow_stabilization_limit
        self.flow_stabilization_timeout = flow_stabilization_timeout
        print(f"Starting flow test with min: {self.pressure_set_point_min}, max: {self.pressure_set_point_max}")
        print(f"Flow Stabilize timeout: {self.flow_stabilization_timeout}, Flow Stabilize Limit: {self.flow_stabilization_limit}")
        self.start()  # Call the original 'start' trigger
        
    def call_testrig_api(self, endpoint: str, method: str = "POST", retry: bool = True):
        url = f"{self.testrig_api_url}{endpoint}"
        token = self.get_auth_token()

        if not token:
            print("No valid token available. Aborting API call.")
            return None

        headers = {
            "Authorization": f"Bearer {token}"
        }

        try:
            if method.upper() == "GET":
                response = requests.get(url, headers=headers)
            else:
                response = requests.post(url, headers=headers)

            if response.status_code == 200:
                print(f"Response from {url}: {response.json()}")
                return response.json()

            elif response.status_code == 401 and retry:
                print("Unauthorized! Refreshing token and retrying...")
                self.auth_token = None
                return self.call_testrig_api(endpoint, method, retry=False)

            else:
                print(f"Failed API call: {response.status_code} - {response.text}")
                return None

        except requests.RequestException as e:
            print(f"Error calling testrig API: {e}")
            return None

    def publish_status(self):
        """Publish the current state to Valkey (Redis)."""
        channel = "test_status"
        message = f"{self.mouldid}:flow_test:{self.state}"
        self.valkey_client.publish(channel, message)
        print(f"Published to {channel}: {message}")
           
    def on_start(self):
        """Logic when start is triggered."""
        print("Starting flow test...")
        self.running = True
        self.publish_status()
        # Call relay API to turn ON relay 1
        try:
            status = self.call_testrig_api("relay/addon/status","GET")
            if isinstance(status, dict) and "response" in status:
                if status["response"][-2:] == "11":
                    self.call_testrig_api("relay/addon/r1/ON")
                    self.call_testrig_api("relay/addon/r2/OFF")
                    for _ in range(25):
                        if not self.running:
                            return
                        time.sleep(1)
                
            else:
                print("Invalid status response from addon relay.")
        except Exception as e:
            print(f"Error checking relay status: {e}")     
        
         # Take air out from the system
        self.db.save_mould_event_msg(self.mouldid,'flow_test_air_purge_start')
        self.call_testrig_api(f"/set-pressure-start-pump/1")
        self.call_testrig_api("relay/addon/r5/ON")
        self.call_testrig_api("relay/addon/r7/ON")
        
        for _ in range(30):
            if not self.running:
                return
            time.sleep(1)
            
        self.call_testrig_api("relay/addon/r5/OFF")
        self.call_testrig_api("relay/addon/r7/OFF")
        #self.call_testrig_api("relay/addon/r6/OFF")
        #self.call_testrig_api("relay/addon/r8/OFF")
        self.db.save_mould_event_msg(self.mouldid,'flow_test_air_purge_complete')
        
        self.db.save_mould_event_msg(self.mouldid,'flow_test_start')
        
        if self.running:
            self.run()
            

    def on_run(self):
        print("Flow test is running...")
        self.publish_status()
        self.db.save_mould_event_msg(self.mouldid, 'flow_test_run')

        START_PRESSURE = self.pressure_set_point_min
        END_PRESSURE = self.pressure_set_point_max
        PRESSURE_STEP = 0.2
        TIMEOUT = 300
        CHECK_INTERVAL = 1

        # Generate pressure values and always include END_PRESSURE
        pressures_to_run = list(np.arange(START_PRESSURE, END_PRESSURE, PRESSURE_STEP))
        if not pressures_to_run or round(pressures_to_run[-1], 2) < END_PRESSURE:
            pressures_to_run.append(END_PRESSURE)

        for rounded_pressure in [round(p, 2) for p in pressures_to_run]:
            if not self.running:
                return

            print(f"\nSetting pressure to {rounded_pressure} bar...")
            self.db.save_mould_event_msg(self.mouldid, f'flow_test_set_pressure_{rounded_pressure}_bar')
            self.call_testrig_api(f"/set-pressure-start-pump/{rounded_pressure}")
            for _ in range(5):
                if not self.running:
                    return
                time.sleep(1)

            start_time = time.time()
            pressure_reached = False

            while self.running and time.time() - start_time < TIMEOUT:
                pressure_response = self.call_testrig_api("/read-pressure", "GET")
                if pressure_response and "pressure" in pressure_response:
                    pressure_value = pressure_response["pressure"]
                    print(f"Current pressure: {pressure_value} bar (Target: {rounded_pressure} bar)")

                    if (pressure_value + 0.07) >= rounded_pressure:
                        print(f"Target pressure {rounded_pressure} bar reached!")
                        self.db.save_mould_event_msg(self.mouldid, f'flow_test_target_pressure_{rounded_pressure}_bar_reached')
                        self.db.save_mould_event_msg(self.mouldid, f'flow_test_stabilize_flow_at_{rounded_pressure}_bar_start')
                        for _ in range(self.flow_stabilization_timeout):
                            if not self.running:
                                return
                            time.sleep(1)
                        self.db.save_mould_event_msg(self.mouldid, f'flow_test_stabilize_flow_at_{rounded_pressure}_bar_complete')
                        self.db.save_mould_event_msg(self.mouldid, f'flow_test_measure_flow_at_{rounded_pressure}_bar_start')
                        self.db.save_mould_event_msg(self.mouldid, f'flow_test_measure_flow_at_{rounded_pressure}_bar_complete')
                        pressure_reached = True
                        break
                    elif pressure_value == 0:
                        print("Pressure reading is zero. Something is wrong!")
                        self.fail()
                        return
                else:
                    print("Failed to get pressure reading.")
                time.sleep(CHECK_INTERVAL)

            if not pressure_reached:
                print(f"Failed to reach target pressure {rounded_pressure} bar within {TIMEOUT} seconds. Triggering fail state.")
                self.fail()
                return

        print(f"\nTest completed successfully up to {END_PRESSURE} bar!")
        if self.running:
            self.complete()

        
    def on_stop(self):
        self.publish_status()
        self.stop_requested = True
        self.running = False
        self.db.save_mould_event_msg(self.mouldid,'flow_test_stop')
        self.call_testrig_api("stop")
        print("Flow test stopped.")
        self.reset()

    def on_complete(self):
        self.publish_status()
        self.running = False
        self.call_testrig_api("stop")
        self.db.save_mould_event_msg(self.mouldid,'flow_test_complete')
        print("Flow test completed successfully.")

    def on_fail(self):
        self.publish_status()
        if self.stop_requested:
            print("Skip error handling due to user stop.")
            return
        self.running = False
        self.call_testrig_api("stop")
        self.db.save_mould_event_msg(self.mouldid,'flow_test_error')
        print("Flow test encountered an error.")
        self.reset()

    def on_reset(self):
        self.state = "idle"
        self.stop_requested = False
        self.running = False
        self.call_testrig_api("stop")
        self.publish_status()
        print("Flow test reset. Ready to start again.")
    
    def is_complete(self):
        return self.state == "completed"

    def handle_message(self, message):
        """Trigger state transition based on message."""
        if message == "start":
            self.start()
        elif message == "run":
            self.run()
        elif message == "stop":
            self.stop()
        elif message == "complete":
            self.complete()
        elif message == "fail":
            self.fail()
        elif message == "reset":
            self.reset()
        print(f"Current State: {self.state}")
        
# State machine for Water Leak Test
class WaterLeakTestStateMachine:
    # Define states
    states = ["idle", "starting", "running", "stopped","completed", "error"]
    
    def __init__(self, db=None):
        self.machine = Machine(model=self, states=WaterLeakTestStateMachine.states, initial="idle")
        self.mouldid = None
        self.db = db
        self.testrig_api_url = "http://testrig-api:8000/"
        self.valkey_client = valkey.Valkey(host="valkey", port=6379)
        self.token_url = "http://testrig-api:8000/token"  # Adjust endpoint if different
        self.auth_token = None
        self.token_expiry = 0
        self.running = False
        self.stop_requested = False
        
        # Define transitions
        self.machine.add_transition(trigger="start", source="idle", dest="starting", after="on_start")
        self.machine.add_transition(trigger="run", source="starting", dest="running", after="on_run")
        self.machine.add_transition(trigger="stop", source="*", dest="stopped", after="on_stop")
        self.machine.add_transition(trigger="complete", source="running", dest="completed", after="on_complete")
        self.machine.add_transition(trigger="fail", source="*", dest="error", after="on_fail")
        self.machine.add_transition(trigger="reset", source=["error", "stopped", "completed", "idle"], dest="idle", after="on_reset")
    
    def get_auth_token(self):
        """
        Generate and cache the authentication token using hardcoded secret key.
        """
        SECRET_KEY = "ZJrUo3Wig7yCbO33P_BzMmd4eyCZ-laQfYY0L9Bz3vVGtGdTtYYupO-nSxur402pe2OcJg2uV3nSalnPPrkmAg%"
        ALGORITHM = "HS256"
        EXPIRY_MINUTES = 120

        current_time = time.time()

        if self.auth_token and current_time < self.token_expiry:
            return self.auth_token

        print("Generating new auth token...")

        try:
            payload = {
                "sub": "client_auth",           # subject (can be customized)
                "iat": int(current_time),        # issued at
                "exp": int(current_time) + EXPIRY_MINUTES * 60  # expiry time
            }

            token = jwt.encode(payload, SECRET_KEY, algorithm=ALGORITHM)

            self.auth_token = token
            self.token_expiry = current_time + EXPIRY_MINUTES * 60 - 60  # refresh 1 min before expiry

            print(f"Token generated! Expires in {EXPIRY_MINUTES * 60} seconds.")
            return self.auth_token

        except Exception as e:
            print(f"Exception when generating token: {e}")
            return None
               
    def call_testrig_api(self, endpoint: str, method: str = "POST", retry: bool = True):
        url = f"{self.testrig_api_url}{endpoint}"
        token = self.get_auth_token()

        if not token:
            print("No valid token available. Aborting API call.")
            return None

        headers = {
            "Authorization": f"Bearer {token}"
        }

        try:
            if method.upper() == "GET":
                response = requests.get(url, headers=headers)
            else:
                response = requests.post(url, headers=headers)

            if response.status_code == 200:
                print(f"Response from {url}: {response.json()}")
                return response.json()

            elif response.status_code == 401 and retry:
                print("Unauthorized! Refreshing token and retrying...")
                self.auth_token = None
                return self.call_testrig_api(endpoint, method, retry=False)

            else:
                print(f"Failed API call: {response.status_code} - {response.text}")
                return None

        except requests.RequestException as e:
            print(f"Error calling testrig API: {e}")
            return None
        
    def custom_round(self,val):
        fractional_part = val - int(val)
        
        if fractional_part > 0.95:
            return math.ceil(val)  # Round UP to next integer
        else:
            return round(val, 1)   # Round to 1 decimal place (or keep as is)
        
    def start_test(self, max_pressure_loss=0.6, max_water_pressure=4, pressure_stabilization_timeout=60,pressure_measure_time=60 ):
        """
        Start the flow test with the given pressure set points.
        """
        self.max_pressure_loss = max_pressure_loss
        self.max_water_pressure = max_water_pressure
        self.pressure_stabilization_timeout = pressure_stabilization_timeout
        self.pressure_measure_time = pressure_measure_time
        print(f"max_pressure_loss: {self.max_pressure_loss}, max_water_pressure: {self.max_water_pressure}")
        print(f"Pressure stabilize time: {self.pressure_stabilization_timeout}, Pressure measure time: {self.pressure_measure_time}")
        self.start()  # Call the original 'start' trigger
        
    def publish_status(self):
        """Publish the current state to Valkey (Redis)."""
        channel = "test_status"
        message = f"{self.mouldid}:water_leak_test:{self.state}"
        self.valkey_client.publish(channel, message)
        print(f"Published to {channel}: {message}")
        
    def on_start(self):
        """Logic when start is triggered."""
        print("Starting Water-pressure test...")
        self.publish_status()
        self.running = True
        self.db.save_mould_event_msg(self.mouldid,'water_leak_test_start')
        # Set relays
        self.call_testrig_api("relay/addon/r1/ON")
        self.call_testrig_api("relay/addon/r2/ON")
        for _ in range(25):
            if not self.running:
                return
            time.sleep(1) 
            
        self.call_testrig_api("relay/addon/r5/ON")
        self.call_testrig_api("relay/addon/r6/ON")
        self.call_testrig_api("relay/addon/r7/ON")
        self.call_testrig_api("relay/addon/r8/ON")
        
        for _ in range(5):
            if not self.running:
                return
            time.sleep(1) 
            
        print(f"\nSetting pressure to 2 bar...")
        self.call_testrig_api(f"/set-pressure-start-pump/2")
        for _ in range(5):
            if not self.running:
                return
            time.sleep(1) 
        if self.running:
            self.run()
    
    def on_run(self):
        print("Water leak test is running...")
        self.publish_status()
        self.db.save_mould_event_msg(self.mouldid,'water_leak_test_fill_start')
        
        START_PRESSURE = 2 # Starting pressure in bars
        TIMEOUT = 240       # Timeout in seconds to wait for target pressure
        CHECK_INTERVAL = 1  # Interval in seconds to check pressure reading
        
        current_pressure = START_PRESSURE
        
        start_time = time.time()
        pressure_reached = False

        while self.running and time.time() - start_time < TIMEOUT:
            pressure_response = self.call_testrig_api("/read-pressure", "GET")

            if pressure_response and "pressure" in pressure_response:
                pressure_value = pressure_response["pressure"]
                print(f"Current pressure: {pressure_value} bar (Target: {current_pressure} bar)")
                    
                if self.custom_round(pressure_value) >= current_pressure:
                    print(f"Target pressure {current_pressure} bar reached!")
                    pressure_reached = True
                    for _ in range(5):
                            if not self.running:
                                return
                            time.sleep(1)
                    # Trigger the relays
                    self.call_testrig_api("relay/addon/r5/OFF")
                    self.call_testrig_api("relay/addon/r6/OFF")
                    self.call_testrig_api("relay/addon/r7/OFF")
                    self.call_testrig_api("relay/addon/r8/OFF")
                    break
                elif pressure_value == 0 or pressure_value < 0:
                    print("Pressure reading is zero. Something is wrong!")
                    self.fail()
                    return
                else:
                    pass

            time.sleep(CHECK_INTERVAL)

        if not pressure_reached:
            print(f"Failed to reach target pressure {current_pressure} bar within {TIMEOUT} seconds. Triggering fail state.")
            self.fail()
            return

        print(f"\nWater Test Fill State Complete with 2 bar!")
        self.db.save_mould_event_msg(self.mouldid,'water_leak_test_fill_complete')   
        
        print(f"\nSetting pressure to target bar...")
        self.call_testrig_api(f"/set-pressure-start-pump/{self.max_water_pressure}")
        self.db.save_mould_event_msg(self.mouldid,'water_leak_test_set_pressure_start')   
        
        current_pressure = self.max_water_pressure
        
        start_time = time.time()
        pressure_reached = False

        while self.running and time.time() - start_time < TIMEOUT:
            pressure_response = self.call_testrig_api("/read-pressure", "GET")

            if pressure_response and "pressure" in pressure_response:
                pressure_value = pressure_response["pressure"]
                print(f"Current pressure: {pressure_value} bar (Target: {current_pressure} bar)")
                    
                if self.custom_round(pressure_value) >= current_pressure:
                    print(f"Target pressure {current_pressure} bar reached!")
                    pressure_reached = True
                    self.db.save_mould_event_msg(self.mouldid,'water_leak_test_set_pressure_complete')   
                    
                    # Wait for 5 seconds and stop the pump
                    for _ in range(5):
                            if not self.running:
                                return
                            time.sleep(1)
                    self.call_testrig_api("stop") 
                    
                    # Stabilization phase
                    self.db.save_mould_event_msg(self.mouldid,'water_leak_test_set_stabilize_start')   
                    print('Water leak test - Stabilization phase')
                    for _ in range(self.pressure_stabilization_timeout):
                            if not self.running:
                                return
                            time.sleep(1)
                    self.db.save_mould_event_msg(self.mouldid,'water_leak_test_set_stabilize_complete')   
                    
                    # Measurement Phase
                    print('Water leak test - Measurement phase')
                    self.db.save_mould_event_msg(self.mouldid,'water_leak_test_measure_start')
                    for _ in range(self.pressure_measure_time):
                            if not self.running:
                                return
                            time.sleep(1)
                    self.db.save_mould_event_msg(self.mouldid,'water_leak_test_measure_complete')
                    break
                elif pressure_value == 0 or pressure_value < 0:
                    print("Pressure reading is zero. Something is wrong!")
                    self.fail()
                    return
                else:
                    pass

            time.sleep(CHECK_INTERVAL)

        if not pressure_reached:
            print(f"Failed to reach target pressure {current_pressure} bar within {TIMEOUT} seconds. Triggering fail state.")
            self.fail()
            return

        print(f"\nWater Test Leak Test Complete!")     
        self.complete()
        
    def on_stop(self):
        print("Water-leak test stopped.")
        self.db.save_mould_event_msg(self.mouldid,'water_leak_test_stop')
        self.publish_status()
        self.stop_requested = True
        self.running = False
        self.call_testrig_api("stop")
        self.call_testrig_api("relay/addon/r5/OFF")
        self.call_testrig_api("relay/addon/r6/OFF")
        self.call_testrig_api("relay/addon/r7/OFF")
        self.call_testrig_api("relay/addon/r8/OFF")
        time.sleep(1)
        # Check status before turning off r1 and r2
        try:
            status = self.call_testrig_api("relay/addon/status","GET")
            if isinstance(status, dict) and "response" in status:
                if status["response"][-2:] == "11":
                    self.call_testrig_api("relay/addon/r1/ON")
                    self.call_testrig_api("relay/addon/r2/OFF")
                    for _ in range(25):
                        time.sleep(1)
                
            else:
                print("Invalid status response from addon relay.")
        except Exception as e:
            print(f"Error checking relay status: {e}")
        self.reset()

    def on_complete(self):
        print("Water-leak test completed successfully.")
        self.running = False
        # Reset relays
        self.call_testrig_api("stop")
        #self.call_testrig_api("relay/addon/r1/ON")
        #self.call_testrig_api("relay/addon/r2/OFF")
        #time.sleep(25)
        self.call_testrig_api("relay/addon/r5/OFF")
        self.call_testrig_api("relay/addon/r6/ON")
        self.call_testrig_api("relay/addon/r7/OFF")
        self.call_testrig_api("relay/addon/r8/ON")
        self.db.save_mould_event_msg(self.mouldid,'water_leak_test_complete')
        self.publish_status()

    def on_fail(self):
        print("Water-leak test encountered an error.")
        if self.stop_requested:
            print("Skip error handling due to user stop.")
            return
        self.running = False
        self.call_testrig_api("stop")
        self.call_testrig_api("relay/addon/r5/OFF")
        self.call_testrig_api("relay/addon/r6/OFF")
        self.call_testrig_api("relay/addon/r7/OFF")
        self.call_testrig_api("relay/addon/r8/OFF")
        time.sleep(1)
        # Check status before turning off r1 and r2
        try:
            status = self.call_testrig_api("relay/addon/status","GET")
            if isinstance(status, dict) and "response" in status:
                if status["response"][-2:] == "11":
                    self.call_testrig_api("relay/addon/r1/ON")
                    self.call_testrig_api("relay/addon/r2/OFF")
                    for _ in range(25):
                        time.sleep(1)
                
            else:
                print("Invalid status response from addon relay.")
        except Exception as e:
            print(f"Error checking relay status: {e}")
        self.db.save_mould_event_msg(self.mouldid,'water_leak_test_error')
        self.publish_status()
        self.reset()

    def on_reset(self):
        print("Water-leak test reset. Ready to start again.")
        print(f"on_reset() triggered. Current FSM state: {self.state}")
        self.stop_requested = False
        self.running = False
        # Reset relays
        '''self.call_testrig_api("stop")
        self.call_testrig_api("relay/addon/r5/OFF")
        self.call_testrig_api("relay/addon/r6/OFF")
        self.call_testrig_api("relay/addon/r7/OFF")
        self.call_testrig_api("relay/addon/r8/OFF")
        time.sleep(5)
        self.call_testrig_api("relay/addon/r1/ON")
        self.call_testrig_api("relay/addon/r2/OFF")
        time.sleep(25)'''
        self.state = "idle"
        self.publish_status()
    
    def is_complete(self):
        return self.state == "completed"

    def handle_message(self, message):
        """Trigger state transition based on message."""
        if message == "start":
            self.start()
        elif message == "run":
            self.run()
        elif message == "stop":
            self.stop()
        elif message == "complete":
            self.complete()
        elif message == "fail":
            self.fail()
        elif message == "reset":
            self.reset()
        print(f"Current State: {self.state}")

# State machine for Air Leak Test       
class AirLeakTestStateMachine:
    # Define states
    states = ["idle", "starting", "running", "stopped" ,"completed", "error"]
    
    def __init__(self, db=None):
        self.machine = Machine(model=self, states=AirLeakTestStateMachine.states, initial="idle")
        self.mouldid = None
        self.db = db
        self.testrig_api_url = "http://testrig-api:8000/"
        self.valkey_client = valkey.Valkey(host="valkey", port=6379)
        self.token_url = "http://testrig-api:8000/token"  # Adjust endpoint if different
        self.auth_token = None
        self.token_expiry = 0
        self.running = False
        self.stop_requested = False
        
        # Define transitions
        self.machine.add_transition(trigger="start", source="idle", dest="starting", after="on_start")
        self.machine.add_transition(trigger="run", source="starting", dest="running", after="on_run")
        self.machine.add_transition(trigger="stop", source="*", dest="stopped", after="on_stop")
        self.machine.add_transition(trigger="complete", source="running", dest="completed", after="on_complete")
        self.machine.add_transition(trigger="fail", source="*", dest="error", after="on_fail")
        self.machine.add_transition(trigger="reset", source=["error", "stopped", "completed", "idle"], dest="idle", after="on_reset")
    
    def get_auth_token(self):
        """
        Generate and cache the authentication token using hardcoded secret key.
        """
        SECRET_KEY = "ZJrUo3Wig7yCbO33P_BzMmd4eyCZ-laQfYY0L9Bz3vVGtGdTtYYupO-nSxur402pe2OcJg2uV3nSalnPPrkmAg%"
        ALGORITHM = "HS256"
        EXPIRY_MINUTES = 120

        current_time = time.time()

        if self.auth_token and current_time < self.token_expiry:
            return self.auth_token

        print("Generating new auth token...")

        try:
            payload = {
                "sub": "client_auth",           # subject (can be customized)
                "iat": int(current_time),        # issued at
                "exp": int(current_time) + EXPIRY_MINUTES * 60  # expiry time
            }

            token = jwt.encode(payload, SECRET_KEY, algorithm=ALGORITHM)

            self.auth_token = token
            self.token_expiry = current_time + EXPIRY_MINUTES * 60 - 60  # refresh 1 min before expiry

            print(f"Token generated! Expires in {EXPIRY_MINUTES * 60} seconds.")
            return self.auth_token

        except Exception as e:
            print(f"Exception when generating token: {e}")
            return None
    
    def custom_round(self,val):
        fractional_part = val - int(val)
        
        if fractional_part > 0.95:
            return math.ceil(val)  # Round UP to next integer
        else:
            return round(val, 1)   # Round to 1 decimal place (or keep as is)
        
    def start_test(self, max_pressure_loss=0.6, pressure_stabilization_timeout=60,pressure_measure_time=60 ):
        """
        Start the flow test with the given pressure set points.
        """
        self.max_pressure_loss = max_pressure_loss
        self.pressure_stabilization_timeout = pressure_stabilization_timeout
        self.pressure_measure_time = pressure_measure_time
        print('max_pressure_loss:', self.max_pressure_loss)
        print(f"Pressure stabilize time: {self.pressure_stabilization_timeout}, Pressure measure time: {self.pressure_measure_time}")
        self.start()  # Call the original 'start' trigger
        
    def call_testrig_api(self, endpoint: str, method: str = "POST", retry: bool = True):
        url = f"{self.testrig_api_url}{endpoint}"
        token = self.get_auth_token()

        if not token:
            print("No valid token available. Aborting API call.")
            return None

        headers = {
            "Authorization": f"Bearer {token}"
        }

        try:
            if method.upper() == "GET":
                response = requests.get(url, headers=headers)
            else:
                response = requests.post(url, headers=headers)

            if response.status_code == 200:
                print(f"Response from {url}: {response.json()}")
                return response.json()

            elif response.status_code == 401 and retry:
                print("Unauthorized! Refreshing token and retrying...")
                self.auth_token = None
                return self.call_testrig_api(endpoint, method, retry=False)

            else:
                print(f"Failed API call: {response.status_code} - {response.text}")
                return None

        except requests.RequestException as e:
            print(f"Error calling testrig API: {e}")
            return None
        
    def publish_status(self):
        """Publish the current state to Valkey (Redis)."""
        channel = "test_status"
        message = f"{self.mouldid}:air_leak_test:{self.state}"
        self.valkey_client.publish(channel, message)
        print(f"Published to {channel}: {message}")

    def on_start(self):
        """Logic when start is triggered."""
        print("Starting Air-pressure test...")
        self.running = True
        self.publish_status()
        self.db.save_mould_event_msg(self.mouldid,'air_leak_test_start')
        # Set relays
        self.db.save_mould_event_msg(self.mouldid,'air_leak_test_purge_start')
        self.call_testrig_api("relay/addon/r1/ON")
        self.call_testrig_api("relay/addon/r2/ON")
        for _ in range(25):
            if not self.running:
                return
            time.sleep(1) 
        self.call_testrig_api("relay/addon/r5/OFF")
        self.call_testrig_api("relay/addon/r6/ON")
        self.call_testrig_api("relay/addon/r7/OFF")
        self.call_testrig_api("relay/addon/r8/ON")
        for _ in range(5):
            if not self.running:
                return
            time.sleep(1) 
        print(f"\nPurging water out...")
        self.call_testrig_api("relay/main/6/ON")
        for _ in range(30):
            if not self.running:
                return
            time.sleep(1) 
        self.call_testrig_api("relay/addon/r6/OFF")
        self.call_testrig_api("relay/addon/r8/OFF")
        self.db.save_mould_event_msg(self.mouldid,'air_leak_test_purge_complete')
        if self.running:
            self.run()
    
    def on_run(self):
        print("Air leak test is running...")
        #self.db.save_mould_event_msg(self.mouldid,'water_leak_test_start')
        self.publish_status()
        
        START_PRESSURE = 1 # Starting pressure in bars
        TIMEOUT = 240       # Timeout in seconds to wait for target pressure
        CHECK_INTERVAL = 1  # Interval in seconds to check pressure reading
        
        current_pressure = START_PRESSURE
        
        start_time = time.time()
        pressure_reached = False
        
        self.db.save_mould_event_msg(self.mouldid,'air_leak_test_set_pressure_start')

        while self.running and time.time() - start_time < TIMEOUT:
            pressure_response = self.call_testrig_api("/read-pressure", "GET")

            if pressure_response and "pressure" in pressure_response:
                pressure_value = pressure_response["pressure"]
                print(f"Current pressure: {pressure_value} bar (Target: {current_pressure} bar)")
                    
                if self.custom_round(pressure_value) >= current_pressure:
                    print(f"Target pressure {current_pressure} bar reached!")
                    pressure_reached = True
                    self.db.save_mould_event_msg(self.mouldid,'air_leak_test_set_pressure_complete')
                    # Trigger the relays
                    self.call_testrig_api("relay/main/6/OFF")
                    
                    # Stabilize time
                    self.db.save_mould_event_msg(self.mouldid,'air_leak_test_set_stabilize_start')
                    print("Air leak test stabilizing...")
                    for _ in range(self.pressure_stabilization_timeout):
                            if not self.running:
                                return
                            time.sleep(1)
                    self.db.save_mould_event_msg(self.mouldid,'air_leak_test_set_stabilize_complete')
                    
                    # Measure the readings
                    self.db.save_mould_event_msg(self.mouldid,'air_leak_test_set_measure_start')
                    print("Measure the readings")
                    for _ in range(self.pressure_measure_time):
                            if not self.running:
                                return
                            time.sleep(1)
                    self.db.save_mould_event_msg(self.mouldid,'air_leak_test_set_measure_complete')
  
                    break
                elif pressure_value == 0 or pressure_value < 0:
                    print("Pressure reading is zero. Something is wrong!")
                    self.fail()
                    return
                else:
                    pass

            time.sleep(CHECK_INTERVAL)

        if not pressure_reached:
            print(f"Failed to reach target pressure {current_pressure} bar within {TIMEOUT} seconds. Triggering fail state.")
            self.fail()
            return

        print(f"\nAir Fill test complete!")
        self.complete()
        # Add logic for running the test
        
    def on_stop(self):
        print("Air-leak test stopped.")
        self.db.save_mould_event_msg(self.mouldid,'air_leak_test_stop')
        self.publish_status()
        self.stop_requested = True
        self.running = False
        self.call_testrig_api("relay/main/6/OFF")
        self.call_testrig_api("relay/addon/r5/ON")
        
        # Check status before turning off r1 and r2
        try:
            status = self.call_testrig_api("relay/addon/status","GET")
            if isinstance(status, dict) and "response" in status:
                if status["response"][-2:] == "11":
                    self.call_testrig_api("relay/addon/r1/ON")
                    self.call_testrig_api("relay/addon/r2/OFF")
                    for _ in range(25):
                        time.sleep(1)
                
            else:
                print("Invalid status response from addon relay.")
        except Exception as e:
            print(f"Error checking relay status: {e}")
        #self.call_testrig_api("relay/addon/r1/ON")
        #self.call_testrig_api("relay/addon/r2/OFF")
        #for _ in range(25):
            #if not self.running:
                #return
            #time.sleep(1)
        self.reset()
        

    def on_complete(self):
        print("Air-leak test completed successfully.")
        self.running = False
        self.call_testrig_api("relay/addon/r5/ON")
        self.db.save_mould_event_msg(self.mouldid,'air_leak_test_complete')

    def on_fail(self):
        print("Air-leak test encountered an error.")
        if self.stop_requested:
            print("Skip error handling due to user stop.")
            return
        self.running = False
        self.call_testrig_api("relay/main/6/OFF")
        self.call_testrig_api("relay/addon/r5/ON")
        # Check status before turning off r1 and r2
        try:
            status = self.call_testrig_api("relay/addon/status","GET")
            if isinstance(status, dict) and "response" in status:
                if status["response"][-2:] == "11":
                    self.call_testrig_api("relay/addon/r1/ON")
                    self.call_testrig_api("relay/addon/r2/OFF")
                    for _ in range(25):
                        time.sleep(1)
                
            else:
                print("Invalid status response from addon relay.")
        except Exception as e:
            print(f"Error checking relay status: {e}")
            
        self.db.save_mould_event_msg(self.mouldid,'air_leak_test_error')
        self.publish_status()
        self.reset()

    def on_reset(self):
        print("Air-leak test reset. Ready to start again.")
        print(f"on_reset() triggered. Current FSM state: {self.state}")
        self.stop_requested = False
        self.running = False
        self.publish_status()
        
    def is_complete(self):
        return self.state == "completed"

    def handle_message(self, message):
        """Trigger state transition based on message."""
        if message == "start":
            self.start()
        elif message == "run":
            self.run()
        elif message == "stop":
            self.stop()
        elif message == "complete":
            self.complete()
        elif message == "fail":
            self.fail()
        elif message == "reset":
            self.reset()
        print(f"Current State: {self.state}")
        
class HeatTestStateMachine:
    # Define states
    states = ["idle", "starting", "running", "stopped"  ,"completed", "error"]
    
    def __init__(self, db=None):
        self.machine = Machine(model=self, states=HeatTestStateMachine.states, initial="idle")
        self.mouldid = None
        self.db = db
        self.testrig_api_url = "http://testrig-api:8000/"
        self.valkey_client = valkey.Valkey(host="valkey", port=6379)
        self.token_url = "http://testrig-api:8000/token"  # Adjust endpoint if different
        self.auth_token = None
        self.token_expiry = 0
        self.running = False
        self.stop_requested = False
        
        # Define transitions
        self.machine.add_transition(trigger="start", source="idle", dest="starting", after="on_start")
        self.machine.add_transition(trigger="run", source="starting", dest="running", after="on_run")
        self.machine.add_transition(trigger="stop", source="*", dest="stopped", after="on_stop")
        self.machine.add_transition(trigger="complete", source="running", dest="completed", after="on_complete")
        self.machine.add_transition(trigger="fail", source="*", dest="error", after="on_fail")
        self.machine.add_transition(trigger="reset", source=["error", "stopped", "completed", "idle"], dest="idle", after="on_reset")
        
    def get_auth_token(self):
        """
        Generate and cache the authentication token using hardcoded secret key.
        """
        SECRET_KEY = "ZJrUo3Wig7yCbO33P_BzMmd4eyCZ-laQfYY0L9Bz3vVGtGdTtYYupO-nSxur402pe2OcJg2uV3nSalnPPrkmAg%"
        ALGORITHM = "HS256"
        EXPIRY_MINUTES = 120

        current_time = time.time()

        if self.auth_token and current_time < self.token_expiry:
            return self.auth_token

        print("Generating new auth token...")

        try:
            payload = {
                "sub": "client_auth",           # subject (can be customized)
                "iat": int(current_time),        # issued at
                "exp": int(current_time) + EXPIRY_MINUTES * 60  # expiry time
            }

            token = jwt.encode(payload, SECRET_KEY, algorithm=ALGORITHM)

            self.auth_token = token
            self.token_expiry = current_time + EXPIRY_MINUTES * 60 - 60  # refresh 1 min before expiry

            print(f"Token generated! Expires in {EXPIRY_MINUTES * 60} seconds.")
            return self.auth_token

        except Exception as e:
            print(f"Exception when generating token: {e}")
            return None
     
    def call_testrig_api(self, endpoint: str, method: str = "POST", retry: bool = True):
        url = f"{self.testrig_api_url}{endpoint}"
        token = self.get_auth_token()

        if not token:
            print("No valid token available. Aborting API call.")
            return None

        headers = {
            "Authorization": f"Bearer {token}"
        }

        try:
            if method.upper() == "GET":
                response = requests.get(url, headers=headers)
            else:
                response = requests.post(url, headers=headers)

            if response.status_code == 200:
                print(f"Response from {url}: {response.json()}")
                return response.json()

            elif response.status_code == 401 and retry:
                print("Unauthorized! Refreshing token and retrying...")
                self.auth_token = None
                return self.call_testrig_api(endpoint, method, retry=False)

            else:
                print(f"Failed API call: {response.status_code} - {response.text}")
                return None

        except requests.RequestException as e:
            print(f"Error calling testrig API: {e}")
            return None
        
    def publish_status(self):
        """Publish the current state to Valkey (Redis)."""
        channel = "test_status"
        message = f"{self.mouldid}:heat_test:{self.state}"
        self.valkey_client.publish(channel, message)
        print(f"Published to {channel}: {message}")
        
    def start_test(self, tcu_temperature=30.0, cooling_cutoff_temperature=28.0 ):
        """
        Start the flow test with the given pressure set points.
        """
        self.tcu_temperature = tcu_temperature
        self.cooling_cutoff_temperature = cooling_cutoff_temperature
        print('TCU Temperature:', self.tcu_temperature)
        print('Cooling Cutoff Temperature:', self.cooling_cutoff_temperature)
        self.start()  # Call the original 'start' trigger
        
    def custom_round(self,val):
        fractional_part = val - int(val)
        
        if fractional_part > 0.95:
            return math.ceil(val)  # Round UP to next integer
        else:
            return round(val, 1)   # Round to 1 decimal place (or keep as is)

    def on_start(self):
        """Logic when start is triggered."""
        print("Starting heat test...")
        self.running = True
        self.publish_status()
        self.db.save_mould_event_msg(self.mouldid,'heat_test_start')
        self.db.save_mould_event_msg(self.mouldid,'heat_test_filling_pipes_start')
        # Set relays
        self.call_testrig_api("relay/addon/r1/ON")
        self.call_testrig_api("relay/addon/r2/ON")
        for _ in range(25):
            if not self.running:
                return
            time.sleep(1) 
        self.call_testrig_api("relay/addon/r5/ON")
        self.call_testrig_api("relay/addon/r7/ON")
        self.call_testrig_api("relay/addon/r6/OFF")
        self.call_testrig_api("relay/addon/r8/ON")
        self.call_testrig_api("relay/addon/r4/ON") #Filter valve
        self.call_testrig_api("relay/main/5/ON") #Empty water
        self.call_testrig_api("/set-pressure-start-pump/1") # Start pump at 1 bar
        for _ in range(90):
            if not self.running:
                return
            time.sleep(1) 
        print('Heat test filling pipes complete')
        self.db.save_mould_event_msg(self.mouldid,'heat_test_filling_pipes_complete')
        if self.running:
            self.run()
        # Add actual logic here (e.g., initializing test, setting up sensors)
    
    def on_run(self):
        print("Heat Test heating process in progress")
        self.publish_status()
        self.db.save_mould_event_msg(self.mouldid,'heat_test_heating_start')
        self.call_testrig_api("relay/main/1/ON") #TCU on
        self.call_testrig_api("relay/main/4/ON") #Tank fill on
        self.call_testrig_api("relay/addon/r7/OFF")
        self.call_testrig_api("relay/addon/r8/OFF")
        self.call_testrig_api("stop") 
        
        for _ in range(5):
            if not self.running:
                return
            time.sleep(1) 
        self.call_testrig_api("relay/addon/r3/ON") #Open the TCU valve
        for _ in range(30):
            if not self.running:
                return
            time.sleep(1) 
        self.call_testrig_api("relay/addon/r5/OFF")
        #Wait for heating to complete
        TIMEOUT = 1000
        CHECK_INTERVAL = 1
        target_temperature = self.tcu_temperature #30
        start_time = time.time()
        temp_status = False
        while self.running and time.time() - start_time < TIMEOUT:
            temp_response = self.call_testrig_api("read-outlet-temperature", "GET")

            if temp_response and "outlet" in temp_response:
                temp_value = temp_response["outlet"]
                print('Current Outlet Temperature', temp_value)    
                    
                if self.custom_round(temp_value) >= target_temperature:
                    print(f"Target temp {target_temperature} degree Celcius reached!")
                    self.db.save_mould_event_msg(self.mouldid,'heat_test_heating_complete')
                    temp_status = True
                    self.call_testrig_api("relay/main/5/OFF") # Tank Empty off
                    self.call_testrig_api("relay/addon/r3/OFF") # TCU Valve off
                    for _ in range(5):
                        if not self.running:
                            return
                        time.sleep(1) 
                    self.call_testrig_api("relay/main/1/OFF") #TCU off
                    break
                elif temp_value < 0:
                    print("Temp reading is negative. Something is wrong!")
                    self.fail()
                    return
                else:
                    pass

            time.sleep(CHECK_INTERVAL)
            
        if not self.running:
            return
        
        print('TCU Heating process complete')
        print("Heat Test cooling process in progress")
        self.db.save_mould_event_msg(self.mouldid,'heat_test_cooling_start')
        self.call_testrig_api("/set-pressure-start-pump/2") # Start pump at 2 bar
        for _ in range(5):
            if not self.running:
                return
            time.sleep(1) 
        self.call_testrig_api("relay/addon/r1/ON")
        self.call_testrig_api("relay/addon/r2/OFF")
        for _ in range(25):
            if not self.running:
                return
            time.sleep(1) 
        
        #Wait for heating to complete
        # Implement better cooling
        TIMEOUT = 3600
        CHECK_INTERVAL = 1
        target_temperature = self.cooling_cutoff_temperature #28
        start_time = time.time()
        temp_status = False
        while self.running and time.time() - start_time < TIMEOUT:
            temp_response = self.call_testrig_api("read-outlet-temperature", "GET")
            inlet_temp = self.call_testrig_api("read-inlet-temperature", "GET")

            if temp_response and "outlet" in temp_response:
                temp_value = temp_response["outlet"]
                print('Current outlet Temperature', temp_value) 
                
            if inlet_temp and "inlet" in inlet_temp:
                inlet_temp_value = inlet_temp["inlet"]
                target_temp = float(inlet_temp_value)  + 5.0
                print('Current Inlet Temperature', inlet_temp_value)    
                    
                if self.custom_round(temp_value) <= target_temp: #target_temperature:
                    print(f"Target temp {target_temperature} degree Celcius reached!")
                    temp_status = True
                    self.call_testrig_api("relay/main/5/OFF") # Tank Empty off
                    self.call_testrig_api("relay/addon/r3/OFF") # TCU Valve off
                    for _ in range(5):
                        if not self.running:
                            return
                        time.sleep(1) 
                    self.call_testrig_api("relay/main/1/OFF") #TCU off
                    break
                elif temp_value == 0 or temp_value < 0:
                    print("Temp reading is zero. Something is wrong!")
                    self.fail()
                    return
                else:
                    pass

            time.sleep(CHECK_INTERVAL)
            
        if not self.running:
            return
        
        self.db.save_mould_event_msg(self.mouldid,'heat_test_cooling_complete')
        print('Heat test cooling process complete')
        
        self.complete()
        # Add logic for running the test
        
    def on_stop(self):
        print("Heat test stopped.")
        self.stop_requested = True
        self.running = False
         #Stop the pump
        self.call_testrig_api("stop")
        self.call_testrig_api("relay/main/5/OFF") # Tank Empty off
        self.call_testrig_api("relay/addon/r3/OFF") # TCU Valve off
        time.sleep(5)
        self.call_testrig_api("relay/main/1/OFF") #TCU off
        # Open the return value
        self.call_testrig_api("relay/addon/r1/ON")
        self.call_testrig_api("relay/addon/r2/OFF")
        time.sleep(25)
        self.db.save_mould_event_msg(self.mouldid,'heat_test_stop')
        self.publish_status()
        self.reset()

    def on_complete(self):
        print("Heat test completed successfully.")
        self.running = False
        self.call_testrig_api("stop")
        self.db.save_mould_event_msg(self.mouldid,'heat_test_complete')
        self.publish_status()

    def on_fail(self):
        print("Heat test encountered an error.")
        if self.stop_requested:
            print("Skip error handling due to user stop.")
            return
        self.running = False
        #Stop the pump
        self.call_testrig_api("stop")
        self.call_testrig_api("relay/main/5/OFF") # Tank Empty off
        self.call_testrig_api("relay/addon/r3/OFF") # TCU Valve off
        time.sleep(5)
        self.call_testrig_api("relay/main/1/OFF") #TCU off
        # Open the return value
        self.call_testrig_api("relay/addon/r1/ON")
        self.call_testrig_api("relay/addon/r2/OFF")
        time.sleep(25)
        self.publish_status()
        self.reset()

    def on_reset(self):
        print("Heat test reset. Ready to start again.")
        self.stop_requested = False
        self.running = False
        self.state="idle"
        self.publish_status()
        
    def is_complete(self):
        return self.state == "completed"

    def handle_message(self, message):
        """Trigger state transition based on message."""
        if message == "start":
            self.start()
        elif message == "run":
            self.run()
        elif message == "stop":
            self.stop()
        elif message == "complete":
            self.complete()
        elif message == "fail":
            self.fail()
        elif message == "reset":
            self.reset()
        print(f"Current State: {self.state}")