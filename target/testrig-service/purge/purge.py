from transitions import Machine
import valkey
import requests
import time
import math
from database.database import DBConnect
import os 
import jwt

# Instance of the database
db = DBConnect(host='postgres', database=os.environ.get('POSTGRES_DB'), user=os.environ.get('POSTGRES_USER'), password=os.environ.get('POSTGRES_PASSWORD'))

class PurgeMould:
    # Define states
    states = ["idle", "starting", "running", "stopped" ,"completed", "error"]
    
    def __init__(self, db=None):
        self.machine = Machine(model=self, states=PurgeMould.states, initial="idle")
        self.mouldid = None       
        self.db = db
        self.testrig_api_url = "http://testrig-api:8000/"
        self.valkey_client = valkey.Valkey(host="valkey", port=6379)
        self.token_url = "http://testrig-api:8000/token"  # Adjust endpoint if different
        self.auth_token = None
        self.token_expiry = 0
        self.running = False
        
        # Define transitions
        self.machine.add_transition(trigger="start", source="idle", dest="starting", after="on_start")
        self.machine.add_transition(trigger="run", source="starting", dest="running", after="on_run")
        self.machine.add_transition(trigger="stop", source="*", dest="stopped", after="on_stop")
        self.machine.add_transition(trigger="complete", source="running", dest="completed", after="on_complete")
        self.machine.add_transition(trigger="fail", source="*", dest="error", after="on_fail")
        self.machine.add_transition(trigger="reset", source=["error", "stopped", "completed"], dest="idle", after="on_reset")
        
    def get_auth_token(self):
        """
        Generate and cache the authentication token using hardcoded secret key.
        """
        SECRET_KEY = "ZJrUo3Wig7yCbO33P_BzMmd4eyCZ-laQfYY0L9Bz3vVGtGdTtYYupO-nSxur402pe2OcJg2uV3nSalnPPrkmAg%"
        ALGORITHM = "HS256"
        EXPIRY_MINUTES = 120

        current_time = time.time()

        if self.auth_token and current_time < self.token_expiry:
            return self.auth_token

        print("Generating new auth token...")

        try:
            payload = {
                "sub": "client_auth",           # subject (can be customized)
                "iat": int(current_time),        # issued at
                "exp": int(current_time) + EXPIRY_MINUTES * 60  # expiry time
            }

            token = jwt.encode(payload, SECRET_KEY, algorithm=ALGORITHM)

            self.auth_token = token
            self.token_expiry = current_time + EXPIRY_MINUTES * 60 - 60  # refresh 1 min before expiry

            print(f"Token generated! Expires in {EXPIRY_MINUTES * 60} seconds.")
            return self.auth_token

        except Exception as e:
            print(f"Exception when generating token: {e}")
            return None
        
    
    def start_test(self):
        """
        Start the purge process.
        """
        print("Starting mould purge")
        self.start()  # Call the original 'start' trigger
        
    def call_testrig_api(self, endpoint: str, method: str = "POST", retry: bool = True):
        url = f"{self.testrig_api_url}{endpoint}"
        token = self.get_auth_token()

        if not token:
            print("No valid token available. Aborting API call.")
            return None

        headers = {
            "Authorization": f"Bearer {token}"
        }

        try:
            if method.upper() == "GET":
                response = requests.get(url, headers=headers)
            else:
                response = requests.post(url, headers=headers)

            if response.status_code == 200:
                print(f"Response from {url}: {response.json()}")
                return response.json()

            elif response.status_code == 401 and retry:
                print("Unauthorized! Refreshing token and retrying...")
                self.auth_token = None
                return self.call_testrig_api(endpoint, method, retry=False)

            else:
                print(f"Failed API call: {response.status_code} - {response.text}")
                return None

        except requests.RequestException as e:
            print(f"Error calling testrig API: {e}")
            return None

    def publish_status(self):
        """Publish the current state to Valkey (Redis)."""
        channel = "purge_status"
        message = f"purge:{self.state}"
        self.valkey_client.publish(channel, message)
        print(f"Published to {channel}: {message}")
           
    def on_start(self):
        """Logic when start is triggered."""
        self.running = True
        self.publish_status()
        
        # Stop pump if running
        self.call_testrig_api("stop")
        
        # Call inlet and outlet water purge
        self.call_testrig_api("relay/addon/r6/ON")
        self.call_testrig_api("relay/addon/r8/ON")
        
        for _ in range(5):
            if not self.running:
                return
            time.sleep(1)
        
        if self.running:
            self.run()
    
  
    def on_run(self):
        print("Purge is running...")
        self.publish_status()
        
        # Closing the main valve
        self.call_testrig_api("relay/addon/r1/ON")
        self.call_testrig_api("relay/addon/r2/ON")
        for _ in range(25):
            if not self.running:
                return
            time.sleep(1) 
            
        # Start compressed air regulated
        self.call_testrig_api("relay/main/6/ON")
        
        for _ in range(45):
            if not self.running:
                return
            time.sleep(1) 
            
        # close outlet water purge
        self.call_testrig_api("relay/main/6/OFF")
        self.call_testrig_api("relay/addon/r8/OFF")
        
        for _ in range(5):
            if not self.running:
                return
            time.sleep(1) 
        
        # Enable high pressure air 
        self.call_testrig_api("relay/main/7/ON")
        
        for _ in range(60):
            if not self.running:
                return
            time.sleep(1) 
            
        # Close compressed air and water inlet purge
        self.call_testrig_api("relay/main/7/OFF")
        self.call_testrig_api("relay/addon/r6/OFF")

        if self.running:
            self.complete()

        
    def on_stop(self):
        self.publish_status()
        self.running = False
        print("Purge stopped.")
        self.reset()

    def on_complete(self):
        self.publish_status()
        self.call_testrig_api("relay/addon/r1/ON")
        self.call_testrig_api("relay/addon/r2/OFF")
        for _ in range(25):
            if not self.running:
                return
            time.sleep(1) 
        self.running = False
        print("Purge completed successfully.")

    def on_fail(self):
        self.publish_status()
        self.running = False
        print("Purge encountered an error.")
        self.reset()

    def on_reset(self):
        self.state = "idle"
        self.running = False
        self.call_testrig_api("relay/main/6/OFF")
        self.call_testrig_api("relay/main/7/OFF")
        self.call_testrig_api("relay/addon/r1/ON")
        self.call_testrig_api("relay/addon/r2/OFF")
        for _ in range(25):
            if not self.running:
                return
            time.sleep(1) 
        self.publish_status()
        print("Purge reset. Ready to start again.")
    
    def is_complete(self):
        return self.state == "completed"

    def handle_message(self, message):
        """Trigger state transition based on message."""
        if message == "start":
            self.start()
        elif message == "run":
            self.run()
        elif message == "stop":
            self.stop()
        elif message == "complete":
            self.complete()
        elif message == "fail":
            self.fail()
        elif message == "reset":
            self.reset()
        print(f"Current State: {self.state}")