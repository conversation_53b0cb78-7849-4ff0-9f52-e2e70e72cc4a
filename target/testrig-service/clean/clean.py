from transitions import Machine
import valkey
import requests
import time
import math
from database.database import DBConnect
import os 
import jwt

# Instance of the database
db = DBConnect(host='postgres', database=os.environ.get('POSTGRES_DB'), user=os.environ.get('POSTGRES_USER'), password=os.environ.get('POSTGRES_PASSWORD'))

class ColdCleanMould:
    # Define states
    states = ["idle", "starting", "running", "stopped" ,"completed", "error"]
    
    def __init__(self, db=None):
        self.machine = Machine(model=self, states=ColdCleanMould.states, initial="idle")
        self.mouldid = None       
        self.db = db
        self.testrig_api_url = "http://testrig-api:8000/"
        self.valkey_client = valkey.Valkey(host="valkey", port=6379)
        self.token_url = "http://testrig-api:8000/token"  # Adjust endpoint if different
        self.auth_token = None
        self.token_expiry = 0
        self.running = False
        
        # Define transitions
        self.machine.add_transition(trigger="start", source="idle", dest="starting", after="on_start")
        self.machine.add_transition(trigger="run", source="starting", dest="running", after="on_run")
        self.machine.add_transition(trigger="stop", source="*", dest="stopped", after="on_stop")
        self.machine.add_transition(trigger="complete", source="running", dest="completed", after="on_complete")
        self.machine.add_transition(trigger="fail", source="*", dest="error", after="on_fail")
        self.machine.add_transition(trigger="reset", source=["error", "stopped", "completed"], dest="idle", after="on_reset")
        
    def get_auth_token(self):
        """
        Generate and cache the authentication token using hardcoded secret key.
        """
        SECRET_KEY = "ZJrUo3Wig7yCbO33P_BzMmd4eyCZ-laQfYY0L9Bz3vVGtGdTtYYupO-nSxur402pe2OcJg2uV3nSalnPPrkmAg%"
        ALGORITHM = "HS256"
        EXPIRY_MINUTES = 120

        current_time = time.time()

        if self.auth_token and current_time < self.token_expiry:
            return self.auth_token

        print("Generating new auth token...")

        try:
            payload = {
                "sub": "client_auth",           # subject (can be customized)
                "iat": int(current_time),        # issued at
                "exp": int(current_time) + EXPIRY_MINUTES * 60  # expiry time
            }

            token = jwt.encode(payload, SECRET_KEY, algorithm=ALGORITHM)

            self.auth_token = token
            self.token_expiry = current_time + EXPIRY_MINUTES * 60 - 60  # refresh 1 min before expiry

            print(f"Token generated! Expires in {EXPIRY_MINUTES * 60} seconds.")
            return self.auth_token

        except Exception as e:
            print(f"Exception when generating token: {e}")
            return None
        
    
    def start_test(self):
        """
        Start the clean process.
        """
        print("Starting cold clean")
        self.start()  # Call the original 'start' trigger
        
    def call_testrig_api(self, endpoint: str, method: str = "POST", retry: bool = True):
        url = f"{self.testrig_api_url}{endpoint}"
        token = self.get_auth_token()

        if not token:
            print("No valid token available. Aborting API call.")
            return None

        headers = {
            "Authorization": f"Bearer {token}"
        }

        try:
            if method.upper() == "GET":
                response = requests.get(url, headers=headers)
            else:
                response = requests.post(url, headers=headers)

            if response.status_code == 200:
                print(f"Response from {url}: {response.json()}")
                return response.json()

            elif response.status_code == 401 and retry:
                print("Unauthorized! Refreshing token and retrying...")
                self.auth_token = None
                return self.call_testrig_api(endpoint, method, retry=False)

            else:
                print(f"Failed API call: {response.status_code} - {response.text}")
                return None

        except requests.RequestException as e:
            print(f"Error calling testrig API: {e}")
            return None

    def publish_status(self):
        """Publish the current state to Valkey (Redis)."""
        channel = "clean_status"
        message = f"coldclean:{self.state}"
        self.valkey_client.publish(channel, message)
        print(f"Published to {channel}: {message}")
           
    def on_start(self):
        """Logic when start is triggered."""
        self.running = True
        
        #Close compressed air and purge pump
        self.call_testrig_api("relay/main/6/OFF")
        self.call_testrig_api("relay/main/7/OFF")
        # close outlet air and water purge
        self.call_testrig_api("relay/addon/r5/OFF")
        self.call_testrig_api("relay/addon/r6/OFF")
        self.call_testrig_api("relay/addon/r7/OFF")
        self.call_testrig_api("relay/addon/r8/OFF")
        self.publish_status()
        
        if self.running:
            self.run()
    
  
    def on_run(self):
        print("Cold cleaning is running...")
        self.publish_status()
        
        try:
            status = self.call_testrig_api("relay/addon/status","GET")
            if isinstance(status, dict) and "response" in status:
                if status["response"][-2:] == "11":
                    self.call_testrig_api("relay/addon/r1/ON")
                    self.call_testrig_api("relay/addon/r2/OFF")
                    for _ in range(25):
                        if not self.running:
                            return
                        time.sleep(1)
                
            else:
                print("Invalid status response from addon relay.")
        except Exception as e:
            print(f"Error checking relay status: {e}")
        
        # Open the main valve
        #self.call_testrig_api("relay/addon/r1/ON")
        #self.call_testrig_api("relay/addon/r2/OFF")
        #for _ in range(25):
            #if not self.running:
            #    return
            #time.sleep(1) 
            
        # start pump
        self.call_testrig_api("relay/addon/r4/ON")
        self.call_testrig_api("relay/main/5/ON") # Start the purge pump

        # run till stop is called
        print("Waiting for stop event to complete the process...")
        while self.running:
            time.sleep(1)

        
    def on_stop(self):
        self.publish_status()
        self.running = False
        print("Cold clean stopped.")
        self.reset()

    def on_complete(self):
        self.publish_status()
        self.call_testrig_api("relay/addon/r1/ON")
        self.call_testrig_api("relay/addon/r2/OFF")
        for _ in range(25):
            if not self.running:
                return
            time.sleep(1) 
        #self.call_testrig_api("relay/addon/r4/OFF")
        self.call_testrig_api("relay/main/5/OFF")
        self.running = False
        print("Cold clean completed successfully.")

    def on_fail(self):
        self.publish_status()
        #self.call_testrig_api("stop")
        self.running = False
        print("Cold clean encountered an error.")
        self.reset()

    def on_reset(self):
        self.state = "idle"
        self.running = False
        #self.call_testrig_api("relay/addon/r4/OFF")
        self.call_testrig_api("relay/main/5/OFF")
        self.publish_status()
        print("Cold clean reset. Ready to start again.")
    
    def is_complete(self):
        return self.state == "completed"

    def handle_message(self, message):
        """Trigger state transition based on message."""
        if message == "start":
            self.start()
        elif message == "run":
            self.run()
        elif message == "stop":
            self.stop()
        elif message == "complete":
            self.complete()
        elif message == "fail":
            self.fail()
        elif message == "reset":
            self.reset()
        print(f"Current State: {self.state}")
        

class HeatCleanMould:
    # Define states
    states = ["idle", "starting", "running", "stopped"  ,"completed", "error"]
    
    def __init__(self, db=None):
        self.machine = Machine(model=self, states=HeatCleanMould.states, initial="idle")
        self.mouldid = None
        self.db = db
        self.testrig_api_url = "http://testrig-api:8000/"
        self.valkey_client = valkey.Valkey(host="valkey", port=6379)
        self.token_url = "http://testrig-api:8000/token"  # Adjust endpoint if different
        self.auth_token = None
        self.token_expiry = 0
        self.running = False
        self.stop_requested = False
        
        # Define transitions
        self.machine.add_transition(trigger="start", source="idle", dest="starting", after="on_start")
        self.machine.add_transition(trigger="run", source="starting", dest="running", after="on_run")
        self.machine.add_transition(trigger="stop", source="*", dest="stopped", after="on_stop")
        self.machine.add_transition(trigger="complete", source="running", dest="completed", after="on_complete")
        self.machine.add_transition(trigger="fail", source="*", dest="error", after="on_fail")
        self.machine.add_transition(trigger="reset", source=["error", "stopped", "completed", "idle"], dest="idle", after="on_reset")
        
    def get_auth_token(self):
        """
        Generate and cache the authentication token using hardcoded secret key.
        """
        SECRET_KEY = "ZJrUo3Wig7yCbO33P_BzMmd4eyCZ-laQfYY0L9Bz3vVGtGdTtYYupO-nSxur402pe2OcJg2uV3nSalnPPrkmAg%"
        ALGORITHM = "HS256"
        EXPIRY_MINUTES = 120

        current_time = time.time()

        if self.auth_token and current_time < self.token_expiry:
            return self.auth_token

        print("Generating new auth token...")

        try:
            payload = {
                "sub": "client_auth",           # subject (can be customized)
                "iat": int(current_time),        # issued at
                "exp": int(current_time) + EXPIRY_MINUTES * 60  # expiry time
            }

            token = jwt.encode(payload, SECRET_KEY, algorithm=ALGORITHM)

            self.auth_token = token
            self.token_expiry = current_time + EXPIRY_MINUTES * 60 - 60  # refresh 1 min before expiry

            print(f"Token generated! Expires in {EXPIRY_MINUTES * 60} seconds.")
            return self.auth_token

        except Exception as e:
            print(f"Exception when generating token: {e}")
            return None
     
    def call_testrig_api(self, endpoint: str, method: str = "POST", retry: bool = True):
        url = f"{self.testrig_api_url}{endpoint}"
        token = self.get_auth_token()

        if not token:
            print("No valid token available. Aborting API call.")
            return None

        headers = {
            "Authorization": f"Bearer {token}"
        }

        try:
            if method.upper() == "GET":
                response = requests.get(url, headers=headers)
            else:
                response = requests.post(url, headers=headers)

            if response.status_code == 200:
                print(f"Response from {url}: {response.json()}")
                return response.json()

            elif response.status_code == 401 and retry:
                print("Unauthorized! Refreshing token and retrying...")
                self.auth_token = None
                return self.call_testrig_api(endpoint, method, retry=False)

            else:
                print(f"Failed API call: {response.status_code} - {response.text}")
                return None

        except requests.RequestException as e:
            print(f"Error calling testrig API: {e}")
            return None
        
    def publish_status(self):
        """Publish the current state to Valkey (Redis)."""
        channel = "clean_status"
        message = f"heatclean:{self.state}"
        self.valkey_client.publish(channel, message)
        print(f"Published to {channel}: {message}")
        
    def start_test(self, tcu_temperature):
        """
        Start the flow test with the given pressure set points.
        """
        self.tcu_temperature = tcu_temperature
        print('TCU Temperature:', self.tcu_temperature)
        self.start()  # Call the original 'start' trigger
        
    def custom_round(self,val):
        fractional_part = val - int(val)
        
        if fractional_part > 0.95:
            return math.ceil(val)  # Round UP to next integer
        else:
            return round(val, 1)   # Round to 1 decimal place (or keep as is)

    def on_start(self):
        """Logic when start is triggered."""
        print("Starting heat clean...")
        self.running = True
        self.publish_status()
        # Set relays
        self.call_testrig_api("relay/addon/r1/ON")
        self.call_testrig_api("relay/addon/r2/ON")
        for _ in range(25):
            if not self.running:
                return
            time.sleep(1) 
        self.call_testrig_api("relay/addon/r5/ON")
        self.call_testrig_api("relay/addon/r7/ON")
        self.call_testrig_api("relay/addon/r6/OFF")
        self.call_testrig_api("relay/addon/r8/ON")
        self.call_testrig_api("relay/addon/r4/ON") #Filter valve
        self.call_testrig_api("relay/main/5/ON") #Empty water
        self.call_testrig_api("/set-pressure-start-pump/1") # Start pump at 1 bar
        for _ in range(90):
            if not self.running:
                return
            time.sleep(1) 
        print('Heat clean filling pipes complete')
        if self.running:
            self.run()
        # Add actual logic here (e.g., initializing test, setting up sensors)
    
    def on_run(self):
        print("Heat Clean heating process in progress")
        self.publish_status()
        self.call_testrig_api("relay/main/1/ON") #TCU on
        self.call_testrig_api("relay/main/4/ON") #Tank fill on
        self.call_testrig_api("relay/addon/r7/OFF")
        self.call_testrig_api("relay/addon/r8/OFF")
        self.call_testrig_api("stop")  
        
        for _ in range(5):
            if not self.running:
                return
            time.sleep(1) 
        self.call_testrig_api("relay/addon/r3/ON") #Open the TCU valve
        for _ in range(30):
            if not self.running:
                return
            time.sleep(1) 
        self.call_testrig_api("relay/addon/r5/OFF")
        #Wait for heating to complete
        '''TIMEOUT = 1000
        CHECK_INTERVAL = 1
        target_temperature = self.tcu_temperature #30
        start_time = time.time()
        temp_status = False
        while self.running and time.time() - start_time < TIMEOUT:
            temp_response = self.call_testrig_api("read-outlet-temperature", "GET")

            if temp_response and "outlet" in temp_response:
                temp_value = temp_response["outlet"]
                print('Current Outlet Temperature', temp_value)    
                    
                if self.custom_round(temp_value) >= target_temperature:
                    print(f"Target temp {target_temperature} degree Celcius reached!")
                    temp_status = True
                    self.call_testrig_api("relay/main/5/OFF") # Tank Empty off
                    self.call_testrig_api("relay/addon/r3/OFF") # TCU Valve off
                    for _ in range(5):
                        if not self.running:
                            return
                        time.sleep(1) 
                    self.call_testrig_api("relay/main/1/OFF") #TCU off
                    break
                elif temp_value < 0:
                    print("Temp reading is negative. Something is wrong!")
                    self.fail()
                    return
                else:
                    pass

            time.sleep(CHECK_INTERVAL)'''
            
        if not self.running:
            return
                   
        # run till stop is called
        print("Waiting for stop event to complete the process...")
        while self.running:
            time.sleep(1)

        
    def on_stop(self):
        print("Heat Clean stopped.")
        self.stop_requested = True
        self.running = False
         #Stop the pump
        self.call_testrig_api("stop")
        self.call_testrig_api("relay/main/5/OFF") # Tank Empty off
        self.call_testrig_api("relay/addon/r3/OFF") # TCU Valve off
        time.sleep(5)
        self.call_testrig_api("relay/main/1/OFF") #TCU off
        # Open the return value
        self.call_testrig_api("relay/addon/r1/ON")
        self.call_testrig_api("relay/addon/r2/OFF")
        time.sleep(25)
        self.publish_status()
        self.reset()

    def on_complete(self):
        print("Heat clean completed successfully.")
        self.running = False
        self.call_testrig_api("stop")
        self.publish_status()

    def on_fail(self):
        print("Heat clean encountered an error.")
        if self.stop_requested:
            print("Skip error handling due to user stop.")
            return
        self.running = False
        #Stop the pump
        self.call_testrig_api("stop")
        self.call_testrig_api("relay/main/5/OFF") # Tank Empty off
        self.call_testrig_api("relay/addon/r3/OFF") # TCU Valve off
        time.sleep(5)
        self.call_testrig_api("relay/main/1/OFF") #TCU off
        # Open the return value
        self.call_testrig_api("relay/addon/r1/ON")
        self.call_testrig_api("relay/addon/r2/OFF")
        time.sleep(25)
        self.publish_status()
        self.reset()

    def on_reset(self):
        print("Heat clean reset. Ready to start again.")
        self.stop_requested = False
        self.running = False
        self.state="idle"
        self.publish_status()
        
    def is_complete(self):
        return self.state == "completed"

    def handle_message(self, message):
        """Trigger state transition based on message."""
        if message == "start":
            self.start()
        elif message == "run":
            self.run()
        elif message == "stop":
            self.stop()
        elif message == "complete":
            self.complete()
        elif message == "fail":
            self.fail()
        elif message == "reset":
            self.reset()
        print(f"Current State: {self.state}")
        
        
class FilterMould:
    # Define states
    states = ["idle", "starting", "running", "stopped" ,"completed", "error"]
    
    def __init__(self, db=None):
        self.machine = Machine(model=self, states=ColdCleanMould.states, initial="idle")
        self.mouldid = None       
        self.db = db
        self.testrig_api_url = "http://testrig-api:8000/"
        self.valkey_client = valkey.Valkey(host="valkey", port=6379)
        self.token_url = "http://testrig-api:8000/token"  # Adjust endpoint if different
        self.auth_token = None
        self.token_expiry = 0
        self.running = False
        
        # Define transitions
        self.machine.add_transition(trigger="start", source="idle", dest="starting", after="on_start")
        self.machine.add_transition(trigger="run", source="starting", dest="running", after="on_run")
        self.machine.add_transition(trigger="stop", source="*", dest="stopped", after="on_stop")
        self.machine.add_transition(trigger="complete", source="running", dest="completed", after="on_complete")
        self.machine.add_transition(trigger="fail", source="*", dest="error", after="on_fail")
        self.machine.add_transition(trigger="reset", source=["error", "stopped", "completed", "idle"], dest="idle", after="on_reset")

    def get_auth_token(self):
        """
        Generate and cache the authentication token using hardcoded secret key.
        """
        SECRET_KEY = "ZJrUo3Wig7yCbO33P_BzMmd4eyCZ-laQfYY0L9Bz3vVGtGdTtYYupO-nSxur402pe2OcJg2uV3nSalnPPrkmAg%"
        ALGORITHM = "HS256"
        EXPIRY_MINUTES = 120

        current_time = time.time()

        if self.auth_token and current_time < self.token_expiry:
            return self.auth_token

        print("Generating new auth token...")

        try:
            payload = {
                "sub": "client_auth",           # subject (can be customized)
                "iat": int(current_time),        # issued at
                "exp": int(current_time) + EXPIRY_MINUTES * 60  # expiry time
            }

            token = jwt.encode(payload, SECRET_KEY, algorithm=ALGORITHM)

            self.auth_token = token
            self.token_expiry = current_time + EXPIRY_MINUTES * 60 - 60  # refresh 1 min before expiry

            print(f"Token generated! Expires in {EXPIRY_MINUTES * 60} seconds.")
            return self.auth_token

        except Exception as e:
            print(f"Exception when generating token: {e}")
            return None
        
    
    def start_test(self):
        """
        Start the filter process.
        """
        print("Starting Filter cleaning process")
        self.start()  # Call the original 'start' trigger
        
    def call_testrig_api(self, endpoint: str, method: str = "POST", retry: bool = True):
        url = f"{self.testrig_api_url}{endpoint}"
        token = self.get_auth_token()

        if not token:
            print("No valid token available. Aborting API call.")
            return None

        headers = {
            "Authorization": f"Bearer {token}"
        }

        try:
            if method.upper() == "GET":
                response = requests.get(url, headers=headers)
            else:
                response = requests.post(url, headers=headers)

            if response.status_code == 200:
                print(f"Response from {url}: {response.json()}")
                return response.json()

            elif response.status_code == 401 and retry:
                print("Unauthorized! Refreshing token and retrying...")
                self.auth_token = None
                return self.call_testrig_api(endpoint, method, retry=False)

            else:
                print(f"Failed API call: {response.status_code} - {response.text}")
                return None

        except requests.RequestException as e:
            print(f"Error calling testrig API: {e}")
            return None

    def publish_status(self):
        """Publish the current state to Valkey (Redis)."""
        channel = "filter_status"
        message = f"filter:{self.state}"
        self.valkey_client.publish(channel, message)
        print(f"Published to {channel}: {message}")
           
    def on_start(self):
        """Logic when start is triggered."""
        self.running = True
        #self.publish_status()
               
        if self.running:
            self.run()
    
  
    def on_run(self):
        print("Filter cleaning is running...")
        
        self.publish_status()
                    
        # Turn on filter vlave
        self.call_testrig_api("relay/addon/r4/ON")
              
        # start purge pump
        self.call_testrig_api("relay/main/5/ON")

        print("Running for 10 minutes or until stop is called...")

        # Sleep 10 minutes (600 seconds) in 1-second intervals
        for _ in range(600):
            if not self.running:
                print("Filter cleaning stopped before completion.")
                return
            time.sleep(1)

        # After successful 10 minutes
        print("Filter cleaning duration completed.")
        self.complete()
   
    def on_stop(self):
        self.publish_status()
        self.running = False
        #self.call_testrig_api("relay/addon/r4/OFF")
        self.call_testrig_api("relay/main/5/OFF")
        print("Filter clean stopped.")
        #if self.state in ["error", "stopped", "completed"]:
        self.reset()

    def on_complete(self):
        #self.publish_status()
        #self.call_testrig_api("relay/addon/r4/OFF")
        self.call_testrig_api("relay/main/5/OFF")
        self.running = False
        print("Filter clean completed successfully.")

    def on_fail(self):
        self.publish_status()
        #self.call_testrig_api("relay/addon/r4/OFF")
        self.call_testrig_api("relay/main/5/OFF")
        self.running = False
        print("Filter clean encountered an error.")
        self.reset()

    def on_reset(self):
        self.state = "idle"
        self.running = False 
        #self.call_testrig_api("relay/addon/r4/OFF")
        self.call_testrig_api("relay/main/5/OFF")
        #self.publish_status()
        print("Filter clean reset. Ready to start again.")
    
    def is_complete(self):
        return self.state == "completed"

    def handle_message(self, message):
        """Trigger state transition based on message."""
        if message == "start":
            self.start()
        elif message == "run":
            self.run()
        elif message == "stop":
            self.stop()
        elif message == "complete":
            self.complete()
        elif message == "fail":
            self.fail()
        elif message == "reset":
            self.reset()
        print(f"Current State: {self.state}")