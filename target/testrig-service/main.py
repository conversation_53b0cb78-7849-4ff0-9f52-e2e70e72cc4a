import valkey
import threading
from tests.tests import FlowTestStateMachine , WaterLeakTestStateMachine, AirLeakTestStateMachine, HeatTestStateMachine
from purge.purge import PurgeMould
from clean.clean import ColdCleanMould, HeatCleanMould, FilterMould
from recycle.recycle import WaterEmpty, WaterFill
from database.database import DBConnect
from monitor.monitor import Monitor
import psycopg2
import psycopg2.extras
import os
import json
import time

# Load the JSON
CONFIG = "/config/config.json"

def load_config():
    """Loads configuration from a JSON file, falling back to defaults."""
    try:
        with open(CONFIG, "r") as f:
            return json.load(f)
    except (FileNotFoundError, json.JSONDecodeError):
        print("Warning: Using default config.")

config_data = load_config()

db = DBConnect(host='postgres', database=os.environ.get('POSTGRES_DB'), user=os.environ.get('POSTGRES_USER'), password=os.environ.get('POSTGRES_PASSWORD'))

# Create statemachine        
flow_test_machine = FlowTestStateMachine(db)
water_leak_test_machine = WaterLeakTestStateMachine(db)
air_leak_test_machine = AirLeakTestStateMachine(db)
heat_test_machine = HeatTestStateMachine(db)

purge_instance = None
clean_instance = None
recycle_instance = None
filter_instance = None
scheduler_started = False
scheduler_stop_event = threading.Event()

# Mapping test names to state machines
test_machines = {
    "flow_test": flow_test_machine,
    "air_leak_test": air_leak_test_machine,
    "water_leak_test": water_leak_test_machine,
    "heat_test": heat_test_machine
}

# Track running tests per mouldid
running_test = None
stop_requested = False

def run_test(db, mouldid, test):
    """
    Run a specific test for a mouldid, ensuring it completes.
    """
    global running_test
    
    #  Load the config data
    config_data = load_config()
    
    if test not in test_machines:
        print(f"Warning: No state machine found for test '{test}'")
        return

    machine = test_machines[test]
    running_test = machine
    
    # Always reset if previous state is not idle
    if machine.state != "idle":
        print(f"Resetting state machine for {test} from state '{machine.state}'")
        machine.reset()
        time.sleep(1)  # Optional: small delay to ensure clean reset

    try:
        print(f"Starting {test} for mould {mouldid}...")

        # Fetch pressure set points if it's a flow test
        pressure_set_points = None
        if test == "flow_test":
            pressure_set_points = db.fetch_pressure_set_points(mouldid)
            if not pressure_set_points:
                print(f"Warning: No pressure set points found for mould {mouldid}. Using defaults.")
                pressure_set_points = {"pressure_set_point_min": 1, "pressure_set_point_max": 3}  # Default values

        # Start the state machine with pressure set points if needed
        if test == "flow_test":
            machine.mouldid = mouldid
            machine.start_test(
                pressure_set_point_min=pressure_set_points["pressure_set_point_min"],
                pressure_set_point_max=pressure_set_points["pressure_set_point_max"],
                flow_stabilization_timeout = config_data['flow_stabilization_timeout'],
                flow_stabilization_limit = config_data['flow_stabilization_limit']
            )
        elif test == "water_leak_test":
            machine.mouldid = mouldid
            water_pressure =  db.fetch_max_water_pressure(mouldid)
            machine.start_test(
                max_pressure_loss=config_data["max_pressure_loss"],
                max_water_pressure=water_pressure["max_water_pressure"],
                pressure_stabilization_timeout = config_data['pressure_stabilization_timeout'],
                pressure_measure_time = config_data['pressure_measure_time']
            )
        elif test == "air_leak_test":
            machine.mouldid = mouldid
            machine.start_test(
                max_pressure_loss=config_data["max_pressure_loss"],
                pressure_stabilization_timeout = config_data['pressure_stabilization_timeout'],
                pressure_measure_time = config_data['pressure_measure_time']
            )
        else:
            machine.mouldid = mouldid
            machine.start_test(
                tcu_temperature=config_data["tcu_temperature"],
                cooling_cutoff_temperature = config_data['cooling_cutoff_temperature']
            )

        # Blocking loop until test completes
        while not machine.is_complete():
            if running_test is None:  # Stop immediately if signal received
                print(f"Stopping {test} for mould {mouldid}.")
                machine.stop()  # Ensure test stops gracefully
                return
                
        '''while not machine.is_complete():
            if stop_requested and machine.state not in ["stopped", "idle", "error"]:
                print(f"Stop requested. Stopping {test} for mould {mouldid}.")
                machine.stop()
                return
            time.sleep(0.5)'''


        print(f"{test} completed for mould {mouldid}.")

    except Exception as e:
        print(f"Error while running {test}: {e}")
        if running_test and running_test.state in ["error", "stopped"]:
            running_test.reset()
        running_test = None
    finally:
        running_test = None  # Reset after completion
    
def run_test_sequence(db, mouldid):
    """
    Runs tests sequentially in a separate thread while allowing interruption.
    """
    global running_test, stop_requested
    
    stop_requested = False
    
    tests = db.fetch_tests_for_mould(mouldid)

    if not tests:
        print(f"No test data found for mould {mouldid}.")
        running_test = None
        return

    print(f"Starting test sequence for mould {mouldid}: {tests}")

    for test in tests:
        if stop_requested:
            print(f"Test sequence interrupted before {test} for mould {mouldid}.")
            break

        print(f"Running {test} for mould {mouldid}.")
        run_test(db, mouldid, test)
        if running_test and running_test.state in ["error", "stopped"]:
                running_test.reset()
                running_test = None

        if stop_requested:
            print(f"Test sequence interrupted after {test} for mould {mouldid}.")
            break
        
    db.save_mould_event_msg(mouldid,'stop')
        
    for test in test_machines.values():
        if test.state != "idle":
            test.reset()   

    # Remove mouldid from tracking after completing or stopping
    running_test = None  # Reset after stopping
    stop_requested = False
    print(f"Test sequence completed for mould {mouldid}.")

def process_mould_message(db, mouldid, action):
    """
    Starts or stops test sequences for a given mouldid.
    """
    print(f"Processing mould {mouldid} with action '{action}'...")
    
    global running_test, stop_requested

    if action == "stop":
        stop_requested = True
        if running_test is not None:
            print(f"Stopping active test: {running_test.__class__.__name__} for mould {mouldid}...")
            running_test.stop()  # Stop the active state machine
            running_test = None  # Reset active test
        else:
            print(f"No active test to stop for mould {mouldid}.")

        return

    elif action == "start":
        db.save_mould_event_msg(mouldid,'start')
        if running_test is not None:
            print(f"A test is already running. Cannot start a new test for mould {mouldid}.")
            if running_test.state in ["error", "stopped"]:
                    print(f"Previous test failed. Resetting and starting new test for {mouldid}.")
                    running_test.reset()
                    running_test = None

        # Start a new test sequence in a separate thread
        test_thread = threading.Thread(target=run_test_sequence, args=(db, mouldid))
        test_thread.start()
        
    print(f"Action '{action}' processed for mould {mouldid}.")

        
def listen_for_mould_events(db):
    """
    Subscribe to a general topic 'mould_events' to detect when a mould topic is published.
    Dynamically subscribe to the published mould topic when needed.
    """
    vk = valkey.Valkey(host="valkey", port=6379)
    pubsub = vk.pubsub()

    general_topic = "mould_events"
    pubsub.subscribe(general_topic)
    print(f"Listening on {general_topic} for mould events...")

    for message in pubsub.listen():
        if message["type"] == "message":
            data = message["data"].decode() if isinstance(message["data"], bytes) else message["data"]
            
            # Extract mouldid and action from the message format "mouldid:start"
            if ":" in data:
                mouldid, action = data.split(":", 1)
                process_mould_message(db,mouldid,action)
                print(f"Received event for mould {mouldid}: {action}")

    vk.close()
    
def listen_for_purge_events(db):
    global purge_instance  # if outside a function, use nonlocal if nested

    vk = valkey.Valkey(host="valkey", port=6379)
    pubsub = vk.pubsub()

    purge_topic = "purge_events"
    pubsub.subscribe(purge_topic)
    print(f"Listening on {purge_topic} for purge events...")

    for message in pubsub.listen():
        if message["type"] == "message":
            action = message["data"].decode()

            print(f"Received event: {action}")

            if action == "start":
                if purge_instance is None or not purge_instance.running:
                    purge_instance = PurgeMould(db)
                    thread = threading.Thread(target=purge_instance.start_test, daemon=True)
                    thread.start()
                else:
                    print("Purge is already running.")
            elif action == "stop":
                if purge_instance and purge_instance.running:
                    purge_instance.stop()
                else:
                    print("No running purge to stop.")
                    
def listen_for_clean_events(db):
    global clean_instance  # Tracks the active cleaning instance
    
     #  Load the config data
    config_data = load_config()

    vk = valkey.Valkey(host="valkey", port=6379)
    pubsub = vk.pubsub()

    clean_topic = "clean_events"
    pubsub.subscribe(clean_topic)
    print(f"Listening on {clean_topic} for clean events...")

    for message in pubsub.listen():
        if message["type"] == "message":
            action = message["data"].decode()
            print(f"Received event: {action}")

            if action in ["hotcleanstart", "coldcleanstart"]:
                if clean_instance is None or not clean_instance.running:
                    if action == "hotcleanstart":
                        clean_instance = HeatCleanMould(db)

                        # Fetch config values
                        tcu_temp = config_data.get("tcu_temperature", 50)

                        thread = threading.Thread(
                            target=clean_instance.start_test,
                            kwargs={
                                "tcu_temperature": tcu_temp
                            },
                            daemon=True
                        )
                        thread.start()
                    elif action == "coldcleanstart":
                        clean_instance = ColdCleanMould(db)

                        thread = threading.Thread(target=clean_instance.start_test, daemon=True)
                        thread.start()
                else:
                    print("A cleaning operation is already running.")
            elif action == "stop":
                if clean_instance and clean_instance.running:
                    clean_instance.stop()
                    clean_instance = None
                else:
                    print("No running cleaning process to stop.")
                    

def listen_for_recycle_events():
    global recycle_instance  # Tracks the active cleaning instance
    
    vk = valkey.Valkey(host="valkey", port=6379)
    pubsub = vk.pubsub()

    recycle_topic = "recycle_events"
    pubsub.subscribe(recycle_topic)
    print(f"Listening on {recycle_topic} for recyle events...")

    for message in pubsub.listen():
        if message["type"] == "message":
            action = message["data"].decode()
            print(f"Received event: {action}")

            if action in ["waterfillstart", "wateremptystart"]:
                if recycle_instance is None or not recycle_instance.running:
                    if action == "waterfillstart":
                        recycle_instance = WaterFill()
                        
                        thread = threading.Thread(target=recycle_instance.start_test, daemon=True)
                        thread.start()

                    elif action == "wateremptystart":
                        recycle_instance = WaterEmpty()

                        thread = threading.Thread(target=recycle_instance.start_test, daemon=True)
                        thread.start()
                else:
                    print("A recycle operation is already running.")
            elif action == "stop":
                if recycle_instance and recycle_instance.running:
                    recycle_instance.stop()
                    recycle_instance = None
                else:
                    print("No running recycle process to stop.")



def listen_for_filterclean_events(db):
    global filter_instance, scheduler_started

    vk = valkey.Valkey(host="valkey", port=6379)
    pubsub = vk.pubsub()

    filter_topic = "filter_events"
    pubsub.subscribe(filter_topic)
    print(f"Listening on {filter_topic} for filter clean events...")

    for message in pubsub.listen():
        if message["type"] == "message":
            action = message["data"].decode().strip()
            print(f"Received filter clean event: {action}")
            
            if action == "start":
                # Start immediate filter clean if not already running
                if filter_instance is None or not filter_instance.running:
                    print("Starting immediate filter clean...")
                    filter_instance = FilterMould(db)
                    threading.Thread(target=filter_instance.start_test, daemon=True).start()
                else:
                    print("Filter clean already in progress.")

                # Start scheduler only once
                if not scheduler_started:
                    scheduler_started = True
                    print("Starting filter clean scheduler...")
                    threading.Thread(target=start_filterclean_scheduler, args=(db,), daemon=True).start()
                else:
                    print("Filter clean scheduler already running.")

            elif action == "stop":
                if filter_instance and filter_instance.running:
                    print("Stopping current filter clean...")
                    filter_instance.stop()
                    filter_instance = None
                else:
                    print("No filter clean is currently running.")
                
                # Stop the scheduler
                if scheduler_started:
                    print("Stopping filter clean scheduler...")
                    scheduler_stop_event.set()
                   


def start_filterclean_scheduler(db):
    def scheduler():
        global filter_instance, scheduler_started

        while not scheduler_stop_event.is_set():
            print("Waiting until next filter clean run...")
            scheduler_stop_event.wait(timeout=3600)  # Wait for 1 hour or until stop is requested

            if scheduler_stop_event.is_set():
                print("Filter clean scheduler stopping...")
                break

            if filter_instance is None or not filter_instance.running:
                print("Starting scheduled filter clean...")
                filter_instance = FilterMould(db)
                thread = threading.Thread(target=filter_instance.start_test, daemon=True)
                thread.start()
            else:
                print("Previous filter clean is still running. Skipping this hour.")

        scheduler_started = False  # Allow restart later
        scheduler_stop_event.clear()  # Reset for future use

    threading.Thread(target=scheduler, daemon=True).start()



    
#Monitor for emergency
mon = Monitor()
thread = threading.Thread(target=mon.monitor_emergency, daemon=True)
thread.start()

#Monitor for flow overshoot
thread = threading.Thread(target=mon.monitor_flow, daemon=True)
thread.start()

#Listen to purge events
threading.Thread(target=listen_for_purge_events, args=(db,), daemon=True).start()

#Listen to clean events
threading.Thread(target=listen_for_clean_events, args=(db,), daemon=True).start()

#Listen to recyle events
threading.Thread(target=listen_for_recycle_events, daemon=True).start()

# Listen to filterclean stop events
threading.Thread(target=listen_for_filterclean_events, args=(db,), daemon=True).start()

# Start hourly filterclean background scheduler
#start_filterclean_scheduler(db)
threading.Thread(target=listen_for_filterclean_events, args=(db,), daemon=True).start()
  
#subscribe("water_leak_test", "air_leak_test", "flow_test", "heat_test")
listen_for_mould_events(db)



