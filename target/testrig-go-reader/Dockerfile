FROM golang:1.23 AS builder
ARG ARCH=arm64

WORKDIR /app

# Copy the module files first (for caching)
COPY ./reader/go.mod ./reader/go.sum ./

# Download dependencies
RUN go mod tidy

# Now copy the rest of the source
COPY ./reader .

# Build
RUN echo "Using ARCH: $ARCH" && \
    CGO_ENABLED=0 GOOS=linux GOARCH=${ARCH} go build -a -installsuffix cgo -ldflags="-w -s" -o /go/bin/reader

############################
# STAGE 2
############################
FROM scratch
COPY --from=builder /go/bin/reader /go/bin/reader
ENTRYPOINT ["/go/bin/reader"]
