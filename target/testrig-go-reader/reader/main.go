package main

import (
	"log"
	"os"
	control "reader/controls"
	interpreter "reader/interpreter"
	postgres "reader/postgres"
	serialcom "reader/serialcom"
	ethernetcom "reader/ethernetcom"
	"strconv"
	"strings"
	"time"
	"github.com/lib/pq"
	"encoding/json"
)

type Manifold struct {
	MAC        string
	Protocol   int
	Data       [28]float32
	Inlet      [2]float32
	SensorLow  float32
	SensorHigh float32
}

type Machine struct {
	Time      int64
	MAC       string
	Manifolds []Manifold
}

//MR-11082024-Structure to capture notifications from PG
type PGNotify struct {
	ManifoldMAC  string `json:"manifoldmac"`
	Low   float32    `json:"sensor_limit_low"`
	High float32 `json:"sensor_limit_high"`
}

func main() {
	useMode := os.Getenv("USE_MODE")
	log.Print("UseMode")
	log.Print(useMode)

	if(useMode == "3") {
	//Use Ethernet Interface
	machine := new(Machine)

	machine.MAC = interpreter.GetMAC(ethernetcom.GetMFIOMAC())
	log.Print("Getting Machine MAC adresse: \n")
	log.Print(machine.MAC)

	log.Print("Scanning for manifolds ...")
	ids := ethernetcom.ManifoldScan()

	// Check if the string slice is empty
	if len(ids) == 0 {
        //log.Print("Manifolds are not found.")
		log.Fatal("Manifolds are not found")
    } else {
        log.Print("Manifolds Found.")
    }

	manifoldMinEnv, _ := strconv.ParseFloat(os.Getenv("MANIFOLD_MIN"), 32)
	manifoldMaxEnv, _ := strconv.ParseFloat(os.Getenv("MANIFOLD_MAX"), 32)
	manifoldMin := float32(manifoldMinEnv)
	manifoldMax := float32(manifoldMaxEnv)

	log.Print("Initialization database connection..")
	db := postgres.InitDB(
		os.Getenv("DB_USER"), os.Getenv("DB_PASSWORD"),
		os.Getenv("DB_HOST"), os.Getenv("DB_NAME"),
		os.Getenv("DB_SSLMODE"))
	defer db.Close()

	log.Print("Deleting exiting sensor config")
	postgres.DeleteSensorRange(db)
	time.Sleep(1000 * time.Millisecond)

	for _, id := range ids {
		manifold := new(Manifold)
		manifold.MAC = interpreter.GetMAC(ethernetcom.GetMac(id))
		manifold.Protocol = interpreter.GetProtocol(ethernetcom.GetProtocol(id))
		manifold.SensorLow = manifoldMin
		manifold.SensorHigh = manifoldMax
		machine.Manifolds = append(machine.Manifolds, *manifold)
		//Set the sensor range onto the database
		saveTime := int64(time.Nanosecond) * time.Now().UnixNano() / int64(time.Millisecond)
		err := postgres.SendSensorRange(db, saveTime, manifold.MAC, manifoldMin, manifoldMax)
		if err != nil {
			log.Fatal(err)
		}
		time.Sleep(300 * time.Millisecond)

		log.Printf("Found: %s", manifold.MAC)
	}

	log.Print("Program start")

	var tmpStatus control.Status

	// Initialize the counter
	Maincounter := 0

	ticker := time.Tick(1 * time.Second)

	//***********- Get the event from postgress
	reportProblem := func(ev pq.ListenerEventType, err error) {
			if err != nil {
				log.Fatal(err.Error())
			}
	}
	
	conninfo := postgres.GetConnectionString(os.Getenv("DB_USER"), os.Getenv("DB_PASSWORD"),
												os.Getenv("DB_HOST"), os.Getenv("DB_NAME"),
												os.Getenv("DB_SSLMODE"))
	
	listener := pq.NewListener(conninfo, 10*time.Second, 2400*time.Hour, reportProblem)
	
	err := listener.Listen("sensor_limits")
		if err != nil {
			log.Fatal(err)
	}
	
	// Create an instance of the struct to hold the parsed data
	var myData []PGNotify

	for range ticker{

		log.Print(machine.MAC)

		machine.Time = int64(time.Nanosecond) * time.Now().UnixNano() / int64(time.Millisecond)

		status := control.GetMFIOStatus()
		log.Print("TMP: ", tmpStatus, "STAT: ", status)

		stmt := "INSERT INTO samples VALUES "

		if tmpStatus != status {
			postgres.SendStatus(db, machine.Time, machine.MAC, status)
		}

		if( Maincounter >= 10) {
			log.Fatal("No connection")
		}

		counter :=0

		//Populate the notification sent from PG
		go postgres.WaitForNotification(listener)

		result := postgres.GetPGNotifyLimits()
		
		if (result != "") {

				var myTempData PGNotify
				// Use the Unmarshal function from the encoding/json package to parse the JSON string
				err := json.Unmarshal([]byte(result), &myTempData)
				if err != nil {
					log.Print("Error parsing JSON:", err)
					return
				}
				log.Print("PG Notify MAC",myTempData.ManifoldMAC)
				log.Print("PG Notify Low",myTempData.Low)
				log.Print("PG Notify High",myTempData.High)

				if len(myData) == 0 {
					myData = append(myData, myTempData)
				} else {
					found := false
					for _, value := range myData {
						if (value.ManifoldMAC == myTempData.ManifoldMAC) {
							found = true
							break
						}
					} 

					if (found == false) {
						myData = append(myData, myTempData)
					} else {
						for i:= 0 ; i < len(myData) ; i++ {
							if (myData[i].ManifoldMAC == myTempData.ManifoldMAC) {
								myData[i].Low = myTempData.Low
								myData[i].High = myTempData.High
							}
						}
					}
				}
		}

		for i, manifold := range machine.Manifolds {

			//MR-11012024-Commented read limits from DB
			//sensorlimit := postgres.GetSensorLimits(db)
			//log.Print("Sensor Limit: ", sensorlimit)
			//for _, limit := range sensorlimit {
			//	if strings.ToUpper(limit.ManifoldMAC) == manifold.MAC {
			//		manifold.SensorLow = limit.Low
			//		manifold.SensorHigh = limit.High
			//	}
			//}
			
			//MR-11012024-Update the notification changes from PG
			if len(myData) != 0 {
				for _, value := range myData {
					if strings.ToUpper(value.ManifoldMAC) == manifold.MAC {
						manifold.SensorLow = value.Low
						manifold.SensorHigh = value.High
					}
				}
			
			}

			log.Print("Sensor Low: ", manifold.SensorLow)
			log.Print("Sensor High: ", manifold.SensorHigh)

			log.Print(manifold.MAC)


			answer := ethernetcom.GetData(ids[i])

			//Check the integrity of the return data
			if len(answer) == 0 {
				counter ++
				continue
			}
			
			if !strings.Contains(answer, "CRC") {
				counter ++
				continue	
			}
			

			manifold.Data, manifold.Inlet = interpreter.CalculateData(
				answer,
				manifold.SensorLow,
				manifold.SensorHigh)

			log.Print(manifold.Data)
			log.Print(manifold.Inlet)

			stmt = postgres.AppendData(
				stmt, machine.Time,
				machine.MAC, manifold.MAC,
				manifold.Data, manifold.Inlet)

		}

		// Increament the main counter
		if counter >0 {
			Maincounter ++
		} else {
			Maincounter = 0
		}
		

		if len(stmt) >= 2 {
			stmt = stmt[:len(stmt)-2]
			stmt += ";"
			postgres.SendData(db, stmt)
		}

		//time.Sleep(1 * time.Second) //***********-Commented
		tmpStatus = status
	}

	db.Close()

	} else if (useMode == "2") {

	//Use Serial Interface old and DEMO mode
	machine := new(Machine)

	// Create new Serialcommunication
	serialport := os.Getenv("SERIALCOM_PORT")
	baud, _ := strconv.Atoi(os.Getenv("SERIALCOM_BAUD"))
	c1 := serialcom.Config(serialport, baud)

	log.Print("Serialport = " + serialport)
	log.Printf("baud = %d", baud)

	machine.MAC = interpreter.GetMAC(serialcom.Writelegacy(c1, "v"))
	log.Print("Getting Machine MAC adresse: \n")
	log.Print(machine.MAC)

	log.Print("Scanning for manifolds ...")
	ids := serialcom.ManifoldScanlegacy(c1)

	// Check if the string slice is empty
	if len(ids) == 0 {
        //log.Print("Manifolds are not found.")
		log.Fatal("Manifolds are not found")
    } else {
        log.Print("Manifolds Found.")
    }

	manifoldMinEnv, _ := strconv.ParseFloat(os.Getenv("MANIFOLD_MIN"), 32)
	manifoldMaxEnv, _ := strconv.ParseFloat(os.Getenv("MANIFOLD_MAX"), 32)
	manifoldMin := float32(manifoldMinEnv)
	manifoldMax := float32(manifoldMaxEnv)

	log.Print("Initialization database connection..")
	db := postgres.InitDB(
		os.Getenv("DB_USER"), os.Getenv("DB_PASSWORD"),
		os.Getenv("DB_HOST"), os.Getenv("DB_NAME"),
		os.Getenv("DB_SSLMODE"))
	defer db.Close()

	log.Print("Deleting exiting sensor config")
	postgres.DeleteSensorRange(db)
	time.Sleep(1000 * time.Millisecond)

	for _, id := range ids {
		manifold := new(Manifold)
		manifold.MAC = interpreter.GetMAC(serialcom.Writelegacy(c1, "v "+id))
		manifold.Protocol = interpreter.GetProtocol(serialcom.Writelegacy(c1, "p "+id))
		manifold.SensorLow = manifoldMin
		manifold.SensorHigh = manifoldMax
		machine.Manifolds = append(machine.Manifolds, *manifold)

		//Set the sensor range onto the database
		saveTime := int64(time.Nanosecond) * time.Now().UnixNano() / int64(time.Millisecond)
		err := postgres.SendSensorRange(db, saveTime, manifold.MAC, manifoldMin, manifoldMax)
		if err != nil {
			log.Fatal(err)
		}
		time.Sleep(300 * time.Millisecond)
		
		log.Printf("Found: %s", manifold.MAC)
	}

	log.Print("Program start")

	var tmpStatus control.Status

	//Initialize the main counter
	Maincounter :=0

	ticker := time.Tick(1 * time.Second)

	//***********- Get the event from postgress
	reportProblem := func(ev pq.ListenerEventType, err error) {
		if err != nil {
			log.Fatal(err.Error())
		}
	}

	conninfo := postgres.GetConnectionString(os.Getenv("DB_USER"), os.Getenv("DB_PASSWORD"),
											os.Getenv("DB_HOST"), os.Getenv("DB_NAME"),
											os.Getenv("DB_SSLMODE"))

	listener := pq.NewListener(conninfo, 10*time.Second, 2400*time.Hour, reportProblem)

	err := listener.Listen("sensor_limits")
	if err != nil {
		log.Fatal(err)
	}

	// Create an instance of the struct to hold the parsed data
	var myData []PGNotify

	for range ticker{

		log.Print(machine.MAC)

		machine.Time = int64(time.Nanosecond) * time.Now().UnixNano() / int64(time.Millisecond)

		status := control.GetStatuslegacy(c1)

		log.Print("TMP: ", tmpStatus, "STAT: ", status)

		stmt := "INSERT INTO samples VALUES "

		if tmpStatus != status {
			postgres.SendStatus(db, machine.Time, machine.MAC, status)
		}

		log.Print("Iterate through manifolds")

		if( Maincounter >= 10) {
			log.Fatal("No connection")
		}
		//Initialize the counter
		counter := 0

		//Populate the notification sent from PG
		go postgres.WaitForNotification(listener)

		result := postgres.GetPGNotifyLimits()
		
		if (result != "") {

				var myTempData PGNotify
				// Use the Unmarshal function from the encoding/json package to parse the JSON string
				err := json.Unmarshal([]byte(result), &myTempData)
				if err != nil {
					log.Print("Error parsing JSON:", err)
					return
				}
				log.Print("PG Notify MAC",myTempData.ManifoldMAC)
				log.Print("PG Notify Low",myTempData.Low)
				log.Print("PG Notify High",myTempData.High)

				if len(myData) == 0 {
					myData = append(myData, myTempData)
				} else {
					found := false
					for _, value := range myData {
						if (value.ManifoldMAC == myTempData.ManifoldMAC) {
							found = true
							break
						}
					} 

					if (found == false) {
						myData = append(myData, myTempData)
					} else {
						for i:= 0 ; i < len(myData) ; i++ {
							if (myData[i].ManifoldMAC == myTempData.ManifoldMAC) {
								myData[i].Low = myTempData.Low
								myData[i].High = myTempData.High
							}
						}
					}
				}
		}
	

		for i, manifold := range machine.Manifolds {

			//MR-11012024-Commented read limits from DB
			//sensorlimit := postgres.GetSensorLimits(db)
			//log.Print("Sensor Limit: ", sensorlimit)
			//for _, limit := range sensorlimit {
			//	if strings.ToUpper(limit.ManifoldMAC) == manifold.MAC {
			//		manifold.SensorLow = limit.Low
			//		manifold.SensorHigh = limit.High
			//	}
			//}
			
			//MR-11012024-Update the notification changes from PG
			if len(myData) != 0 {
				for _, value := range myData {
					if strings.ToUpper(value.ManifoldMAC) == manifold.MAC {
						manifold.SensorLow = value.Low
						manifold.SensorHigh = value.High
					}
				}
			
			}

			log.Print("Sensor Low: ", manifold.SensorLow)
			log.Print("Sensor High: ", manifold.SensorHigh)

			log.Print(manifold.MAC)

			answer := serialcom.Writelegacy(c1, "r "+ids[i])

			// Check the integrity of the return data
			if len(answer) == 0 {
				counter ++
				continue
			}

			if !strings.Contains(answer, "CRC") {
				counter ++
				continue	
			}

			log.Print("Read Resp:", answer)

			manifold.Data, manifold.Inlet = interpreter.CalculateData(
				answer,
				manifold.SensorLow,
				manifold.SensorHigh)

			log.Print(manifold.Data)
			log.Print(manifold.Inlet)

			stmt = postgres.AppendData(
				stmt, machine.Time,
				machine.MAC, manifold.MAC,
				manifold.Data, manifold.Inlet)

		}

		// Increament the main counter
		if counter >0 {
			Maincounter ++
		} else {
			Maincounter = 0
		}

		if len(stmt) >= 2 {
			stmt = stmt[:len(stmt)-2]
			stmt += ";"
			postgres.SendData(db, stmt)
		}

		//time.Sleep(1 * time.Second) //***********-Commented
		tmpStatus = status
	}

	db.Close()

  } else {

	//Use M12 for Mouldlive v2
	machine := new(Machine)

	// Create new Serialcommunication
	serialport := os.Getenv("SERIALCOM_PORT")
	baud, _ := strconv.Atoi(os.Getenv("SERIALCOM_BAUD"))
	c1 := serialcom.Config(serialport, baud)

	log.Print("Serialport = " + serialport)
	log.Printf("baud = %d", baud)

	machine.MAC = os.Getenv("MAC_ADDRESS_ETH1") //interpreter.GetMAC(serialcom.Write(c1, "v"))
	log.Print("Getting Machine MAC adresse: \n")
	log.Print(machine.MAC)

	log.Print("Scanning for manifolds ...")
	ids := serialcom.ManifoldScan(c1)

	// Check if the string slice is empty
	if len(ids) == 0 {
        //log.Print("Manifolds are not found.")
		log.Fatal("Manifolds are not found")
    } else {
        log.Print("Manifolds Found.")
    }

	manifoldMinEnv, _ := strconv.ParseFloat(os.Getenv("MANIFOLD_MIN"), 32)
	manifoldMaxEnv, _ := strconv.ParseFloat(os.Getenv("MANIFOLD_MAX"), 32)
	manifoldMin := float32(manifoldMinEnv)
	manifoldMax := float32(manifoldMaxEnv)

	log.Print("Initialization database connection..")
	db := postgres.InitDB(
		os.Getenv("DB_USER"), os.Getenv("DB_PASSWORD"),
		os.Getenv("DB_HOST"), os.Getenv("DB_NAME"),
		os.Getenv("DB_SSLMODE"))
	defer db.Close()

	log.Print("Deleting exiting sensor config")
	postgres.DeleteSensorRange(db)
	time.Sleep(1000 * time.Millisecond)

	for _, id := range ids {
		manifold := new(Manifold)
		manifold.MAC = interpreter.GetMAC(serialcom.Write(c1, "v "+id))
		time.Sleep(300 * time.Millisecond)
		manifold.Protocol = interpreter.GetProtocol(serialcom.Write(c1, "p "+id))
		manifold.SensorLow = manifoldMin
		manifold.SensorHigh = manifoldMax
		machine.Manifolds = append(machine.Manifolds, *manifold)

		//Set the sensor range onto the database
		saveTime := int64(time.Nanosecond) * time.Now().UnixNano() / int64(time.Millisecond)
		err := postgres.SendSensorRange(db, saveTime, manifold.MAC, manifoldMin, manifoldMax)
		if err != nil {
			log.Fatal(err)
		}
		time.Sleep(300 * time.Millisecond)

		log.Printf("Found: %s", manifold.MAC)
	}

	log.Print("Program start")

	//var tmpStatus control.Status

	// Initialize the main counter
	Maincounter :=0

	ticker := time.Tick(1 * time.Second)

	//***********- Get the event from postgress
	reportProblem := func(ev pq.ListenerEventType, err error) {
			if err != nil {
				log.Fatal(err.Error())
			}
	}
	
	conninfo := postgres.GetConnectionString(os.Getenv("DB_USER"), os.Getenv("DB_PASSWORD"),
												os.Getenv("DB_HOST"), os.Getenv("DB_NAME"),
												os.Getenv("DB_SSLMODE"))
	
	listener := pq.NewListener(conninfo, 10*time.Second, 2400*time.Hour, reportProblem)
	
	err := listener.Listen("sensor_limits")
		if err != nil {
			log.Fatal(err)
	}
	
	// Create an instance of the struct to hold the parsed data
	var myData []PGNotify

	for range ticker{
		log.Print("Machine MAC")
		log.Print(machine.MAC)

		machine.Time = int64(time.Nanosecond) * time.Now().UnixNano() / int64(time.Millisecond)

		//MR-11072023-Commented Status
		//status := control.GetStatus(c1)
		//log.Print("TMP: ", tmpStatus, "STAT: ", status)

		stmt := "INSERT INTO samples VALUES "

		//MR-11072023-Commented
		//if tmpStatus != status {
		//	postgres.SendStatus(db, machine.Time, machine.MAC, status)
		//}

		if( Maincounter >= 10) {
			log.Fatal("No connection")
		}
		//Initialize the counter
		counter := 0

		//Populate the notification sent from PG
		go postgres.WaitForNotification(listener)

		result := postgres.GetPGNotifyLimits()
		
		if (result != "") {

				var myTempData PGNotify
				// Use the Unmarshal function from the encoding/json package to parse the JSON string
				err := json.Unmarshal([]byte(result), &myTempData)
				if err != nil {
					log.Print("Error parsing JSON:", err)
					return
				}
				log.Print("PG Notify MAC",myTempData.ManifoldMAC)
				log.Print("PG Notify Low",myTempData.Low)
				log.Print("PG Notify High",myTempData.High)

				if len(myData) == 0 {
					myData = append(myData, myTempData)
				} else {
					found := false
					for _, value := range myData {
						if (value.ManifoldMAC == myTempData.ManifoldMAC) {
							found = true
							break
						}
					} 

					if (found == false) {
						myData = append(myData, myTempData)
					} else {
						for i:= 0 ; i < len(myData) ; i++ {
							if (myData[i].ManifoldMAC == myTempData.ManifoldMAC) {
								myData[i].Low = myTempData.Low
								myData[i].High = myTempData.High
							}
						}
					}
				}
		}

		for i, manifold := range machine.Manifolds {

			//MR-11012024-Commented read limits from DB
			//sensorlimit := postgres.GetSensorLimits(db)
			//log.Print("Sensor Limit: ", sensorlimit)
			//for _, limit := range sensorlimit {
			//	if strings.ToUpper(limit.ManifoldMAC) == manifold.MAC {
			//		manifold.SensorLow = limit.Low
			//		manifold.SensorHigh = limit.High
			//	}
			//}

			//MR-11012024-Update the notification changes from PG
			if len(myData) != 0 {
				for _, value := range myData {
					if strings.ToUpper(value.ManifoldMAC) == manifold.MAC {
						manifold.SensorLow = value.Low
						manifold.SensorHigh = value.High
					}
				}
			
			}

			log.Print("Sensor Low: ", manifold.SensorLow)
			log.Print("Sensor High: ", manifold.SensorHigh)

			log.Print(manifold.MAC)

			answer := serialcom.Write(c1, "r "+ids[i])

			// Check Integrity of the return data
			if len(answer) == 0 {
				counter ++
				continue
			}

			if !strings.Contains(answer, "CRC") {
				counter ++
				continue	
			}

			//MR-05072024-Validate the answer
			if serialcom.IsValidResponse(answer) == false {
				log.Print("Response is not Valid", answer)
				counter ++
				continue
		    }
			
			counter = 0
			log.Print("Read Resp:", answer)

			manifold.Data, manifold.Inlet = interpreter.CalculateData(
				answer,
				manifold.SensorLow,
				manifold.SensorHigh)

			log.Print(manifold.Data)
			log.Print(manifold.Inlet)

			stmt = postgres.AppendData(
				stmt, machine.Time,
				machine.MAC, manifold.MAC,
				manifold.Data, manifold.Inlet)
			
		}
			// Increament the main counter
			if counter >0 {
				Maincounter ++
			} else {
				Maincounter = 0
			}

			if len(stmt) >= 2 {
				stmt = stmt[:len(stmt)-2]
				stmt += ";"
				postgres.SendData(db, stmt)
			}

		//time.Sleep(1 * time.Second) //MR-08082024-Comment sleep
		//tmpStatus = status //MR-11072023-commented
	}

	db.Close()

  }
}
