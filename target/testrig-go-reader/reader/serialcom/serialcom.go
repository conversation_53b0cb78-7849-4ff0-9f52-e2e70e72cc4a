package serialcom

import (
	"log"
	"os"
	"regexp"
	"strings"
	"time"
	"fmt"
	"github.com/tarm/serial"
	"hash/crc32"
    "encoding/binary"
	"strconv"
)

// Config configures the settings for the serialport
// config.Name sets the port to use fx /dev/ttyUSB0
// config.Baud sets the buadrate
// config.ReadTimeout is set to close the connection at a specific time if deadlock
func Config(port string, baud int) (config *serial.Config) {
	config = new(serial.Config)
	config.Name = port                           // /dev/ttyUSB0
	config.Baud = baud                           //  115200
	config.ReadTimeout = time.Millisecond * 3000 //  must be atlest ~2500 secs for scan
	return config
}

// Open opens serialport with specified config using mqtt.Config
func open(c *serial.Config) (serialport *serial.Port) {

	serialport, err := serial.OpenPort(c)
	if err != nil {
		log.Fatal(err)
	}
	return serialport
}

// Write sends node commands and returns answer
// v returns Machine Version
// v <ID> returns Manifold Version
// r <ID> returns Data from manifolds
// p returns machine protocol
// p <ID> returns manifold protocol */
func Write(config *serial.Config, msg string) (answer string) {

	//log.Print("inside write")
	msgInbytes := make([]byte, 1)
	s := open(config)
	defer s.Close()

	if s != nil {
		_, err := s.Write([]byte(msg + " \r\n"))
		if err != nil {
			log.Fatal(err)
		}
	}
	//log.Print("Written Command")
	Command, _ := regexp.MatchString(`^[^o].*`, msg)

	if Command {
		//MR-11072023-Skip the initial
		//log.Print("Skipping unneeded characters") 
		SkipMsg := false
		for !SkipMsg {
			_, err := s.Read(msgInbytes)
			if err != nil {
				log.Fatal(err)
			}
			tmp := " "
			tmp = string(msgInbytes)

			if strings.TrimSpace(tmp) == "v" || strings.TrimSpace(tmp) == "p" ||  strings.TrimSpace(tmp) == "r" || msgInbytes[0] != '\n'{
				//log.Print(string(msgInbytes))
				continue
			} else {
				SkipMsg = true
				msgInbytes = make([]byte, 1)
			}

		}
		//log.Print("Reading Values")

		for msgInbytes[0] != '\n' {
			//log.Print("Inside read Loop")
			_, err := s.Read(msgInbytes)
			if err != nil {
				log.Print(err)
				return ""
			}
			answer += string(msgInbytes)
			//log.Print(answer)

		}
	}
	//log.Print(answer)
	return answer
}

// Write sends node commands and returns answer
// v returns Machine Version
// v <ID> returns Manifold Version
// r <ID> returns Data from manifolds
// p returns machine protocol
// p <ID> returns manifold protocol */
func Writelegacy(config *serial.Config, msg string) (answer string) {

	msgInbytes := make([]byte, 1)
	s := open(config)
	defer s.Close()

	if s != nil {
		_, err := s.Write([]byte(msg + " \r\n"))
		if err != nil {
			log.Fatal(err)
		}
	}
	Command, _ := regexp.MatchString(`^[^o].*`, msg)

	if Command {
		for msgInbytes[0] != '\n' {
			_, err := s.Read(msgInbytes)
			if err != nil {
				log.Print(err)
				return ""
			}

			answer += string(msgInbytes)

		}
	}
	return answer
}



// ManifoldScan = Scans and returns all manifolds using "s" command
func ManifoldScan(config *serial.Config) (manifolds []string) {

	msgInbytes := make([]byte, 1)
	msg := " "
	fullMsg := false
	s := open(config)

	if s != nil {
		_, err := s.Write([]byte("s \r\n"))
		if err != nil {
			log.Fatal(err)

		}
	}

	//var count int
	for !fullMsg {
		_, err := s.Read(msgInbytes)
		if err != nil {
			break
			log.Fatal(err)

		}


		//MR-11072023-Skip s character
		tmp := " "
		tmp = string(msgInbytes)
		if strings.TrimSpace(tmp) == "s" {
			continue
		}

		msg += string(msgInbytes)

		//log.Print(msg)

	}
	s.Close()
	manifolds = strings.Fields(msg)
	//log.Print(manifolds)
	return manifolds
}

// ManifoldScan = Scans and returns all manifolds using "s" command
func ManifoldScanlegacy(config *serial.Config) (manifolds []string) {

	msgInbytes := make([]byte, 1)
	msg := " "
	fullMsg := false
	s := open(config)

	if s != nil {
		_, err := s.Write([]byte("s \r\n"))
		if err != nil {
			log.Fatal(err)

		}
	}

	for !fullMsg {

		_, err := s.Read(msgInbytes)
		if err != nil {
			log.Fatal(err)

		}

		msg += string(msgInbytes)

		if len(msg) >= 3 {
			if msg[len(msg)-1] == '\n' &&
				msg[len(msg)-2] == '\r' &&
				msg[len(msg)-3] == '\n' {
				fullMsg = true
			}
		}

	}
	s.Close()
	manifolds = strings.Fields(msg)
	return manifolds
}



// Args = Reads parater when file is run ./serialcom <argument>
func Args() string {

	var args, sep string
	for i := 1; i < len(os.Args); i++ {
		args += sep + os.Args[i]
		sep = " "
	}
	return args
}

//MR-05072024-Validate return data
// Function to validate the response
func IsValidResponse(response string) bool {
    // Split the input string at " CRC: "
	parts := strings.Split(response, " CRC: ")
	if len(parts) != 2 {
		log.Print("Invalid input format")
		return false
	}

	//log.Print("CRC", parts[1])

	dataPart := strings.TrimPrefix(parts[0], "Data: ")

	strValues := strings.Split(dataPart, ",")

	// Create a slice to store uint32 values
	var uintValues []uint32

	// Convert each string to uint32
	for _, str := range strValues {
		// Trim any leading or trailing whitespace
		str = strings.TrimSpace(str)

		// Convert the string to a 32-bit unsigned integer
		value, err := strconv.ParseUint(str, 10, 32)
		if err != nil {
			fmt.Println("Error converting:", err)
		}

		// Append the converted value to the slice
		uintValues = append(uintValues, uint32(value))
	}

	// Convert uint32 data to byte slice
    buf := make([]byte, len(uintValues)*4)
    for i, v := range uintValues {
        binary.LittleEndian.PutUint32(buf[i*4:], v)
    }

    // Calculate CRC32
    crc := crc32.ChecksumIEEE(buf)

    //fmt.Printf("CRC32: 0x%08X\n", crc)

    // Trim whitespace and newlines
	cleanedInput := strings.TrimSpace(parts[1])

	// Remove the 0x prefix
	cleanedInput = strings.TrimPrefix(cleanedInput, "0x")

	// Convert the hex string to uint32
    targetCRC, err := strconv.ParseUint(cleanedInput, 16, 32)
    if err != nil {
        log.Fatalf("Failed to parse CRC string: %v", err)
    }

	// Compare the calculated CRC with the target CRC
    if crc == uint32(targetCRC) {
        fmt.Printf("CRC Match! Calculated CRC: 0x%08X, Target CRC: 0x%08X\n", crc, targetCRC)
		return true
    } else {
        fmt.Printf("CRC Mismatch. Calculated CRC: 0x%08X, Target CRC: 0x%08X\n", crc, targetCRC)
		return false
    }
    
    // Split the data part by commas
    /*dataValues := strings.Split(dataPart, ",")
    
    // Count the number of non-empty data values
    count := 0
    for _, value := range dataValues {
        if strings.TrimSpace(value) != "" {
            count++
        }
    }
    
	log.Print("Data count", count)

    // Check if the count matches the expected number
    if count == 28 || count == 32 {
        return true
    } else {
		return false
	} */

}



