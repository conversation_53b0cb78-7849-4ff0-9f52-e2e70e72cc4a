package interpreter

import (
	"fmt"
	"log"
	"strings"
)

var previousFlow0 float32 = 0

func flowEquation(value float32, flowMin float32, flowMax float32) float32 {
	var val = ((value/65535)*5-0.5)*((flowMax-flowMin)/(3.5-0.5)) + flowMin
	return (val)
}

func tempEquation(value float32) float32 {
	return ((value/65535)*5 - 0.5) * (100 / (3.5 - 0.5))
}

func pressureEquation(value float32) float32 {
	var val = ((((value / 65535) * 5.0) - 0.5) * (10.0 / 3.0))
	return val
}

// GetMAC = returns Mac and Protocol of the Machine
func GetMAC(msg string) (MAC string) {
	//_, err := fmt.Sscanf(msg, "MouldFlo Node Version: 1125 trunk, MAC: %s SN: 6328, Protocol:", &MAC)
	fmt.Sscanf(msg[strings.LastIndex(msg, "MAC:"):], "MAC:%s", &MAC)
	MAC = MAC[:len(MAC)-1]
	return MAC
}

// GetProtocol = returns Protocol number
func GetProtocol(msg string) (protocol int) {
	_, err := fmt.Sscanf(msg, "Protocol: %d", &protocol)
	if err != nil {
		log.Fatal(err)
	}
	return protocol
}

// CalculateData = Returns arrays with sets of data read from the serialcom.
// crc is not used but configured
func CalculateData(msg string, flowMin float32, flowMax float32) (result [28]float32, inlet [2]float32) {

	crc := 0
	var tmpPressure [6]float32 //***********-Add additional pressure channels
	var data [32]float32
	validChannels := 0

	length, _ := fmt.Sscanf(msg, "Data: %f, %f, %f, %f, %f, %f, %f, %f, %f, %f, %f, %f, %f, %f, %f, %f, %f, %f, %f, %f, %f, %f, %f, %f, %f, %f, %f, %f, %f, %f, %f, %f CRC: <crc>",
		&data[0], &data[1], &data[2], &data[3],
		&data[4], &data[5], &data[6], &data[7],
		&data[8], &data[9], &data[10], &data[11],
		&data[12], &data[13], &data[14], &data[15],
		&data[18], &data[19], &data[16], &data[17], // [17]<->[19], [18]<->[20]
		&data[20], &data[21], &data[22], &data[23],
		&data[24], &data[25], &data[26], &data[27], &data[28], &data[29], &data[30], &data[31])

	// Read and convert CRC from hex to int
	fmt.Sscanf(msg[strings.LastIndex(msg, "0x"):], "0x%x", &crc)

	// Temporary variable to hold pressure if channel count is valid
	tmpPressure[0] = pressureEquation(data[20]) // Channel 11
	tmpPressure[1] = pressureEquation(data[22]) // Channel 12
	tmpPressure[2] = pressureEquation(data[24]) // Channel 13
	tmpPressure[3] = pressureEquation(data[26]) // Channel 14
	//***********-Add support for new manifold type
	tmpPressure[4] = pressureEquation(data[8])  // Channel 5
	tmpPressure[5] = pressureEquation(data[10]) // Channel 6

	//***********-Backup logic to calculate flow
	for i := 0; i < length-4; i++ {
		// Calculate Flow
		if i%2 == 0 {
			if data[i] > 6553 && flowEquation(data[i], 2, 40) > 1 {
				// Determine flowMin and flowMax based on the iteration index
				var flowMin, flowMax float32
				switch {
				case i < 3:
					flowMin, flowMax = 20.0, 400.0
				case (i > 3 && i < 19):
					flowMin, flowMax = 2.0, 40.0
				case (i > 19 && i < 23):
					flowMin, flowMax = 1.0, 20.0
				default:
					// Handle cases beyond the 12th iteration if necessary
				}
				flow := flowEquation(data[i], flowMin, flowMax)
				//if i == 0 {
				//	flow = previousFlow0*0.95 + flow*0.05
				//	previousFlow0 = flow
				//}
				data[i] = flow
			} else {
				data[i] = 0 // ***********-Make flow to 0 instead of -1
			}
		} else {
			// Calculate Temperature and count channels
			if data[i] > 2621 && tempEquation(data[i]) >= 0 {
				data[i] = tempEquation(data[i])
				validChannels++
			} else {
				data[i] = -1
			}
		}
	}

	// Change flow channel to pressure depending on channel count
	/*if (validChannels == 4 && data[5] == -1 && data[7] == -1) { //***********-Set the pressure for 2 channel manifold
		data[20] = tmpPressure[0]
		data[22] = tmpPressure[1]
	} else if (validChannels == 6 && data[9] != -1 && data[11] != -1) { //***********-Support to MFN type
		data[20] = tmpPressure[4]
		data[21] = data[9]
		data[22] = tmpPressure[5]
		data[23] = data[11]
		data[8]=0
		data[9]=-1
		data[10]=0
		data[11]=-1
	} else if validChannels == 6 {
		data[20] = tmpPressure[0]
		data[22] = tmpPressure[1]
	} else if validChannels == 10 {
		data[20] = tmpPressure[0]
		data[22] = tmpPressure[1]
	} else if validChannels == 13 {
		data[24] = tmpPressure[2]
		data[26] = tmpPressure[3]
	}*/

	data[24] = tmpPressure[2]
	data[26] = tmpPressure[3]

	for i := 0; i < length-4; i++ {
		result[i] = data[i]
	}

	for c, i := 0, length-4; i < length-2; i, c = i+1, c+1 {
		inlet[c] = data[i] / 1000
	}

	/*if (validChannels == 4 && data[5] == -1 && data[7] == -1) {//***********-Support 2 channel manifolds
		inlet[0] = data[21]
		inlet[1] = 0
	} else {
		for c, i := 0, length-4; i < length-2; i, c = i+1, c+1 {
			inlet[c] = data[i] / 1000
			}
	} */

	return result, inlet
}
