package ethernetcom

import (
	"io/ioutil"
	"log"
	"net/http"
	"strings"
	"text/template"
 )

 func ManifoldScan() (manifolds []string) {
	resp, err := http.Get("http://192.168.1.23/if/index.shtml?cmd=s")
	if err != nil {
	   log.Fatalln(err)
	}

	//We Read the response body on the line below.
	body, err := ioutil.ReadAll(resp.Body)
	if err != nil {
	   log.Fatalln(err)
	}

	//Convert the body to type string
	response := string(body)
	
	nodeIDs := strings.Fields(response)

	nodeIDs = nodeIDs[:len(nodeIDs) - 1]

	//log.Print(nodeIDs)

	//Return the manifolds
	manifolds = nodeIDs

	return manifolds

	//getExecuteData(nodeIDs)

 }

 func GetMFIOStatus()(answer string){
	resp, err := http.Get("http://192.168.1.23/if/index.shtml?cmd=m")
	if err != nil {
	   log.Fatalln(err)
	}

	//We Read the response body on the line below.
	body, err := ioutil.ReadAll(resp.Body)
	if err != nil {
	   log.Fatalln(err)
	}

	//Convert the body to type string
	responseData := string(body)

	answer = responseData

	return answer

	//getExecuteData(nodeIDs)

 }

//func getExecuteData(param []string){
//	for range time.Tick(time.Second * 1) {
//		for index,element := range param{
//			fmt.Println(index)
//			fmt.Println(element)
//			getData(element)
//			getMac(element)
//			getProtocol(element)
//
//		}
//	}
//}

 func GetData(s string) (answer string){
	getString := format("http://192.168.1.23/if/index.shtml?cmd=r&id={{.}}", s)
	resp, err := http.Get(getString) 
	if err != nil {
	   log.Fatalln(err)
	}

	//We Read the response body on the line below.
	body, err := ioutil.ReadAll(resp.Body)
	if err != nil {
	   log.Fatalln(err)
	}

	//Convert the body to type string
	responseData := string(body)

	answer = responseData

	return answer

	//log.Print(responseData)
 }

 func GetMac(s string) (answer string) {
	getString := format("http://192.168.1.23/if/index.shtml?cmd=v&id={{.}}", s)
	resp, err := http.Get(getString)
	if err != nil {
	   log.Fatalln(err)
	}

	//We Read the response body on the line below.
	body, err := ioutil.ReadAll(resp.Body)
	if err != nil {
	   log.Fatalln(err)
	}

	//Convert the body to type string
	responseData := string(body)

	answer = responseData

	return answer

	//log.Print(responseData)
 }

 func GetProtocol(s string) (answer string) {
	getString := format("http://192.168.1.23/if/index.shtml?cmd=p&id={{.}}", s)
	resp, err := http.Get(getString)
	if err != nil {
	   log.Fatalln(err)
	}

	//We Read the response body on the line below.
	body, err := ioutil.ReadAll(resp.Body)
	if err != nil {
	   log.Fatalln(err)
	}

	//Convert the body to type string
	responseData := string(body)

	answer = responseData

	return answer
	//log.Print(responseData)
 }

 func GetMFIOMAC() (answer string) {

	resp, err := http.Get("http://192.168.1.23/if/index.shtml?cmd=v")
	if err != nil {
	   log.Fatalln(err)
	}

	//We Read the response body on the line below.
	body, err := ioutil.ReadAll(resp.Body)
	if err != nil {
	   log.Fatalln(err)
	}

	//Convert the body to type string
	responseData := string(body)

	answer = responseData

	return answer

	//log.Print(responseData)
 }

 func format(s string, v interface{}) string {
	t, b := new(template.Template), new(strings.Builder)
	template.Must(t.Parse(s)).Execute(b, v)
	return b.String()
 }