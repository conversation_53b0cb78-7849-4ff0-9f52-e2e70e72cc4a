package postgres

import (
	"database/sql"
	"fmt"
	"log"
	"encoding/json"
	"time"
	"bytes"
	control "reader/controls"

	// pq is used only for drivers
	// https://godoc.org/github.com/lib/pq
	"github.com/lib/pq"
)

type SensorLimit struct {
	ManifoldMAC string
	Low         float32
	High        float32
}

//***********-Declare a global variable
var sensor_limits string = ""

// InitDB = Initializes a connection to the data base
// Then return the database for query use
// dbinfo is a url // "*********************************"
// <user>, <password>,<host/IP> <dbname> <sslmode>
func InitDB(dbusername, dbpassword, dbhost, dbname, sslmode string) (db *sql.DB) {

	dbinfo := fmt.Sprintf("postgres://%s:%s@%s/%s?sslmode=%s",
		dbusername, dbpassword, dbhost, dbname, sslmode)

	db, err := sql.Open("postgres", dbinfo)
	if err != nil {
		log.Fatal(err)
	} else {
		log.Print("Connected to database.")
	}

	return db

}

func GetConnectionString(dbusername, dbpassword, dbhost, dbname, sslmode string) (string) {
	dbinfo := fmt.Sprintf("postgres://%s:%s@%s/%s?sslmode=%s",
		dbusername, dbpassword, dbhost, dbname, sslmode)
	
	return dbinfo
}

func GetSensorLimits(db *sql.DB) (limits []SensorLimit) {

	sqlStatement := `SELECT manifoldmac, sensor_limit_low,sensor_limit_high
	FROM sensor_limits;`

	rows, err := db.Query(sqlStatement)
	if err != nil {
		log.Print(err)
	}
	defer rows.Close()
	for rows.Next() {
		var limit SensorLimit
		if err := rows.Scan(
			&limit.ManifoldMAC,
			&limit.Low,
			&limit.High); err != nil {
			log.Print(err)
		}
		limits = append(limits, limit)
	}

	rows.Close()
	if err := rows.Err(); err != nil {
		log.Print(err)
	}
	return limits

}

// SendData Queries the machine data and
// Places it into the database
func AppendData(stmt string, time int64, machineMAC, manifoldMAC string, data [28]float32, inlet [2]float32) string {

	stmt += fmt.Sprintf("(TO_TIMESTAMP(%d::double precision / 1000), '%s', '%s', %f, %f, %f, %f, %f, %f, %f, %f, %f, %f, %f, %f, %f, %f, %f, %f, %f, %f, %f, %f, %f, %f, %f, %f, %f, %f, %f, %f, %f,%f), ",
		time,
		machineMAC,
		manifoldMAC,
		data[0], data[1], // Flow / Temp 1
		data[2], data[3], // Flow / Temp 2
		data[4], data[5], // Flow / Temp 3
		data[6], data[7], // Flow / Temp 4
		data[8], data[9], // Flow / Temp 5
		data[10], data[11], // Flow / Temp 6
		data[12], data[13], // Flow / Temp 7
		data[14], data[15], // Flow / Temp 8
		data[16], data[17], // Flow / Temp 9
		data[18], data[19], // Flow / Temp 10
		data[20], data[21], // Flow / Temp 11
		data[22], data[23], // Flow / Temp 12
		data[24], data[25], // Flow / Temp 13
		data[26], data[27], // Flow / Temp 14
		inlet[0], inlet[1])
	return stmt
}

func SendData(db *sql.DB, stmt string) {
	db.Exec(stmt)
}

//MR-29092023-Send sensor range data for manifolds
/*func SendSensorRange(db *sql.DB, time int64, manifoldMAC string, flow_lower_range float32, flow_upper_range float32) {
	stmt := fmt.Sprintf("INSERT INTO sensor_limits VALUES(TO_TIMESTAMP(%d::double precision / 1000), '%s', %f , %f);",
		time,
		manifoldMAC,
		flow_lower_range,
		flow_upper_range)
	
	db.Exec(stmt)
	fmt.Println("Data inserted successfully")
}*/

//MR-24092024-Updated SendSensorRange
func SendSensorRange(db *sql.DB, time int64, manifoldMAC string, flow_lower_range float32, flow_upper_range float32) error {
    // Begin the transaction
    tx, err := db.Begin()
    if err != nil {
        return fmt.Errorf("failed to begin transaction: %w", err)
    }

    // Prepare the insert statement
    stmt := fmt.Sprintf("INSERT INTO sensor_limits VALUES(TO_TIMESTAMP(%d::double precision / 1000), '%s', %f, %f);",
        time, manifoldMAC, flow_lower_range, flow_upper_range)

    // Execute the statement within the transaction
    _, err = tx.Exec(stmt)
    if err != nil {
        tx.Rollback() // Roll back the transaction on error
        return fmt.Errorf("failed to execute insert: %w", err)
    }

    // Commit the transaction
    err = tx.Commit()
    if err != nil {
        return fmt.Errorf("failed to commit transaction: %w", err)
    }

    fmt.Println("Data inserted successfully and transaction committed.")
    return nil
}


//MR-02102023-Delete the data from sensor_limits database
func DeleteSensorRange(db *sql.DB) {
	stmt := "DELETE FROM sensor_limits;"
	db.Exec(stmt)
}


func SendStatus(db *sql.DB, time int64, machineMAC string, status control.Status) {

	stmt := fmt.Sprintf("INSERT INTO machine_status VALUES(TO_TIMESTAMP(%d::double precision / 1000), '%s', %d , %d , %d, %t , %t , %t , %t , %t );",
		time,
		machineMAC,
		status.RED,
		status.YELLOW,
		status.GREEN,
		status.RELAY0,
		status.RELAY1,
		status.INPUT0,
		status.INPUT1,
		status.CLEARBTN)

	db.Exec(stmt)
}

//MR-10012024-Function to get notification from DB changes
func WaitForNotification(l *pq.Listener){
	for {
		select {
		case n := <-l.Notify:
			log.Print("Received data from channel [", n.Channel, "] :")
			// Prepare notification payload for pretty print
			var prettyJSON bytes.Buffer
			err := json.Indent(&prettyJSON, []byte(n.Extra), "", "\t")
			if err != nil {
				log.Print("Error processing JSON: ", err)
				return
			}
			//log.Print(string(prettyJSON.Bytes()))
			sensor_limits = string(prettyJSON.Bytes())
			return 
		case <-time.After(90 * time.Second):
			log.Print("Received no events for 90 seconds, checking connection")
			go func() {
				l.Ping()
			}()
			return
		}
	}
}

//***********-Get the sensor type for notifications from PG
func GetPGNotifyLimits()string {
	if sensor_limits != "" {
		temp := sensor_limits
		sensor_limits = ""
		return temp
	} else {
		return sensor_limits
	}
}