package controls

import (
	"fmt"

	serialcom "reader/serialcom"

	ethernetcom "reader/ethernetcom"

	"github.com/tarm/serial"
)

// Status holds the values of the MFIO control box
// 0 = OFF
// 1 = ON
// 2 = Blink
type Status struct {
	RED      int
	YELLOW   int
	GREEN    int
	RELAY0   bool
	RELAY1   bool
	INPUT0   bool
	INPUT1   bool
	CLEARBTN bool
}

// GetStatus retuns the values of all the controls on the MFIO
func GetStatus(config *serial.Config) (status Status) {
	msg := serialcom.Write(config, "m")
	fmt.Sscanf(msg, "%d %d %d %t %t %t %t %t",
		&status.RED,
		&status.YELLOW,
		&status.GREEN,
		&status.RELAY0,
		&status.RELAY1,
		&status.INPUT0,
		&status.INPUT1,
		&status.CLEARBTN)
	return status
}

// GetStatus retuns the values of all the controls on the MFIO in legacy MODE
func GetStatuslegacy(config *serial.Config) (status Status) {
	msg := serialcom.Writelegacy(config, "m")
	fmt.Sscanf(msg, "%d %d %d %t %t %t %t %t",
		&status.RED,
		&status.YELLOW,
		&status.GREEN,
		&status.RELAY0,
		&status.RELAY1,
		&status.INPUT0,
		&status.INPUT1,
		&status.CLEARBTN)
	return status
}

//Get status from ethernet interface
func GetMFIOStatus() (status Status) {
	msg := ethernetcom.GetMFIOStatus()
	fmt.Sscanf(msg, "%d %d %d %t %t %t %t %t",
		&status.RED,
		&status.YELLOW,
		&status.GREEN,
		&status.RELAY0,
		&status.RELAY1,
		&status.INPUT0,
		&status.INPUT1,
		&status.CLEARBTN)
	return status
}
