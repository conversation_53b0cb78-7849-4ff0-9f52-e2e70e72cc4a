#!/bin/sh

# SOURCES
# https://www.nxp.com/docs/en/user-guide/IMXLXYOCTOUG.pdf

# Conf file
# ./sources/meta-compulab-bsp/meta-bsp-imx8mq/conf/machine/cl-som-imx8.conf

export MACHINE=iot-gate-imx8
export DISTRO=fsl-imx-xwayland
export IMAGE=fsl-image-base
export IMAGE_FSTYPES=sdcard.gz
export SERIAL_CONSOLES="115200 ttymxc2"
export MACHINE_FEATURES+=" usbgadget usbhost"
export BRANCH=devel_ga-rev1.1
export LC_ALL=en_US.UTF-8
export LANG=en_US.UTF-8

DATA=/data
export HOME=$DATA
export PATH=$PATH:$DATA:$DATA/sources/poky/scripts:$DATA/sources/poky/bitbake/bin

git config --global user.email "<EMAIL>"
git config --global user.name "MI build"

cd $DATA

if [ ! -d sources/meta-bsp-imx8mq ] ; then

#	echo yes | repo init -u git://source.codeaurora.org/external/imx/imx-manifest.git -b imx-linux-sumo -m imx-4.14.98-2.0.0_ga.xml
	echo yes | repo init -u git://source.codeaurora.org/external/imx/imx-manifest.git  -b imx-linux-zeus -m imx-5.4.24-2.1.0.xml
	repo sync

	git clone -b iot-gate-imx8_r2.0 https://github.com/compulab-yokneam/meta-bsp-imx8mm.git sources/meta-bsp-imx8mm/
	# git clone -b master https://github.com/compulab-yokneam/meta-bsp-imx8mq.git sources/meta-bsp-imx8mq/
fi

cd $DATA
cp -av patches/* sources/meta-bsp-imx8mm/recipes-kernel/linux/compulab/imx8mm/
MACHINE=${MACHINE} source sources/meta-bsp-imx8mm/tools/setup-imx8mm-env -b build-cmdline

bitbake -k core-image-full-cmdline
# bitbake -k linux-imx
# bitbake  linux-imx -c menuconfig

