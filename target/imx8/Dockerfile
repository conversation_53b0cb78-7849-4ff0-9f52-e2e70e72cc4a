FROM ubuntu:16.04

RUN apt-get update && \
	apt-get install -y gawk wget git-core diffstat unzip texinfo gcc-multilib \
					   locales build-essential chrpath socat cpio python python3 \
					   python3-pip python3-pexpect xz-utils debianutils iputils-ping \
					   libsdl1.2-dev xterm curl tar

RUN rm /bin/sh && ln -s bash /bin/sh
RUN echo "en_US.UTF-8 UTF-8" >> /etc/locale.gen
RUN locale-gen
RUN locale-gen en_US.UTF-8 && update-locale LC_ALL=en_US.UTF-8 LANG=en_US.UTF-8

RUN curl -s https://packagecloud.io/install/repositories/github/git-lfs/script.deb.sh | bash
RUN apt install -y git-lfs
RUN curl https://storage.googleapis.com/git-repo-downloads/repo > /bin/repo
RUN chmod a+x /bin/repo
