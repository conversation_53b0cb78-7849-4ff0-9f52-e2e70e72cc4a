PATCHES=$$(pwd)/meta-bsp-imx8mm/recipes-kernel/linux/compulab/imx8mm
NXP_RELEASE=rel_imx_5.4.24_2.1.0
CPL_BRANCH=iot-gate-imx8_r2.0

#
# KERNEL BUILD, this is used
#
imx-kernel-build:
	docker build docker -t imx-kernel-build

sources: imx-kernel-build
	git clone -b $${CPL_BRANCH} https://github.com/compulab-yokneam/meta-bsp-imx8mm.git
	git clone https://source.codeaurora.org/external/imx/linux-imx.git
	git -C linux-imx checkout -b linux-compulab $${NXP_RELEASE}
	git -C linux-imx am $${PATCHES}/*.patch

image: sources
	docker run --rm \
		-e "ARCH=arm64" \
		-e "CROSS_COMPILE=/usr/bin/aarch64-linux-gnu-" \
		-e "MACHINE=iot-gate-imx8" \
		-u $$(id -u):$$(id -g) \
		-v $$(pwd)/linux-imx:/data/kernel imx-kernel-build \
		make -j4 -C /data/kernel


packages: sources
	docker run --rm \
		-e "ARCH=arm64" \
		-e "CROSS_COMPILE=/usr/bin/aarch64-linux-gnu-" \
		-e "MACHINE=iot-gate-imx8" \
		-u $$(id -u):$$(id -g) \
		-v $$(pwd)/linux-imx:/data/kernel imx-kernel-build \
		make -C /data/kernel -j4 bindeb-pkg
