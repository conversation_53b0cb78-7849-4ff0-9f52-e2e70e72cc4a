ARG REPOS
FROM ${REPOS}

COPY qemu-arm-static /usr/bin/qemu-arm-static

ENV DEBIAN_FRONTEND=noninteractive

#RUN echo "Acquire::http::Proxy \"http://*********:3142\";" | tee /etc/apt/apt.conf.d/01proxy
RUN apt-get update && \
    apt-get install -yq --no-install-recommends \
        python3 \
        python3-dev \
        libpq-dev \
        openssl \
        python3-pip \
        python3-pandas \
        python3-numpy \
        python3-scipy \
        rsync \
        lua5.2 \
        lua-filesystem \
        nano \
        build-essential \
        libxml2-dev \
        libxslt-dev \
        socat \
        minicom \
        python3-matplotlib \
        openssh-client

RUN pip3 install --no-cache-dir setuptools
RUN pip3 install --no-cache-dir serial \
                 jsonschema \
                 flask \
                 jsonify \
                 requests \
                 paho-mqtt \
                 pymongo \
                 psycopg2 \
                 pytz \
                 jsonmerge \
                 uvicorn \
                 fastapi \
                 pyserial \
                 python-multipart \
                 python-jose \
                 transitions \
                 pydantic \
                 redis \
                 httpx \
                 valkey \
                 matplotlib \
                 xlrd \
                 openpyxl \
                 PyJWT 