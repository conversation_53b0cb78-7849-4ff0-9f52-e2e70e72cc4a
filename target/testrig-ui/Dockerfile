### Updated Dockerfile to use Nginx

# Build stage
FROM --platform=linux/aarch64 node:20 AS build
WORKDIR /app
COPY package*.json ./
RUN npm install
COPY . ./
RUN npx ng build --configuration=production

# Production stage
FROM --platform=linux/aarch64 nginx:alpine

# Copy custom nginx.conf
COPY nginx.conf /etc/nginx/nginx.conf

COPY --from=build /app/dist/test-rig/browser /usr/share/nginx/html

# Expose port 80
EXPOSE 80
CMD ["nginx", "-g", "daemon off;"]
