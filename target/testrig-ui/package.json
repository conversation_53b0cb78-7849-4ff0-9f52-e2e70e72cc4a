{"name": "test-rig", "version": "0.0.0", "scripts": {"ng": "ng", "start": "ng serve", "build": "ng build", "watch": "ng build --watch --configuration development", "test": "ng test"}, "private": true, "dependencies": {"@angular-slider/ngx-slider": "^17.0.2", "@angular/animations": "^17.3.12", "@angular/cdk": "^20.1.2", "@angular/common": "^17.3.0", "@angular/compiler": "^17.3.0", "@angular/core": "^17.3.0", "@angular/forms": "^17.3.0", "@angular/material": "^20.1.2", "@angular/platform-browser": "^17.3.0", "@angular/platform-browser-dynamic": "^17.3.0", "@angular/router": "^17.3.0", "@zxing/library": "^0.21.3", "bootstrap": "^5.3.3", "bs-stepper": "^1.7.0", "html2canvas": "^1.4.1", "jspdf": "^3.0.0", "jwt-decode": "^4.0.0", "moment": "^2.30.1", "moment-timezone": "^0.5.48", "notiflix": "^3.2.7", "rxjs": "~7.8.0", "tslib": "^2.3.0", "zone.js": "~0.14.3"}, "devDependencies": {"@angular-devkit/build-angular": "^17.3.3", "@angular/cli": "^18", "@angular/compiler-cli": "^17.3.0", "@types/jasmine": "~5.1.0", "jasmine-core": "~5.1.0", "karma": "~6.4.0", "karma-chrome-launcher": "~3.2.0", "karma-coverage": "~2.2.0", "karma-jasmine": "~5.1.0", "karma-jasmine-html-reporter": "~2.1.0", "typescript": "~5.4.2"}}