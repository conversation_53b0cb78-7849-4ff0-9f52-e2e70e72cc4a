<!doctype html>
<html lang="en">
<head>
  <meta charset="utf-8">
  <title>TestRig</title>
  <base href="/">
  <meta name="viewport" content="width=device-width, initial-scale=1">
  <link rel="icon" type="image/x-icon" href="favicon.ico">
</head>
<body>
  <app-root></app-root>
</body>
<script>
  function printDiv(divId) {
    let container = document.getElementById(divId);
    let pages = container.querySelectorAll('.page-break');

    // Clone the container so original DOM is untouched
    let clonedContainer = container.cloneNode(true);
    let clonedPages = clonedContainer.querySelectorAll('.page-break');

    // Add footer to cloned pages only
    clonedPages.forEach((page) => {
      let footer = document.createElement('div');
      footer.className = 'mt-auto text-center';
      footer.innerHTML = `<label><i><b>Confidential</b> – For your eyes only – cannot be shared outside your company</i></label>`;
      page.appendChild(footer);
    });
  
    // Get head content (styles etc.)
    let headContent = document.head.innerHTML;
    // Open new window and write content
    let newWindow = window.open('', '_blank');
    newWindow.document.write(`
      <html>
        <head>${headContent}</head>
        <body>${clonedContainer.innerHTML}</body>
      </html>
    `);

    setTimeout(() => {
      newWindow.print();
    }, 500);

    function googleTranslateElementInit() {
      setTimeout(() => {  
        var translate = new google.translate.TranslateElement({
        pageLanguage: 'fr',
        // includedLanguages: 'en,es,zh-CN,da,de,fr,it,pl,sv,cy,ja,hu,th,cs'
        },
        'google_translate_element'
        );
      }, 5000);
    }
  }
</script>
</html>
