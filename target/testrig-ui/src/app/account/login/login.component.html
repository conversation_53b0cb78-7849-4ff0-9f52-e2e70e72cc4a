<section class="login-module" *ngIf="!userLoggedIn">
  <div class="container">
    <div class="row justify-content-center align-items-center vh-100">
      <div class="col-md-10 col-lg-8">
        <!-- White Background Container -->
        <div class="p-4 shadow rounded bg-white">
          
          <!-- Header -->
          <div class="text-center mb-4">
            <img src="assets/images/logo.svg" alt="logo" width="150px" class="mb-3" />
            <h4>{{ "Welcome" | translate }}</h4>
          </div>

          <!-- Row: Form | Divider | User List -->
          <div class="d-flex flex-wrap flex-md-nowrap justify-content-between">
            
            <!-- Login Form -->
            <div class="w-100 w-md-50 pe-md-4 mb-4 mb-md-0">
              <form [formGroup]="loginForm" (ngSubmit)="onLogin()">
                <div class="card-header pb-4">
                  <h2 class="h4 font-weight-bold text-dark">{{"Login"| translate}}</h2>
                  <p class="text-muted small">{{"Log in to your account"| translate}}</p>
                </div>
                <div class="form-floating mb-4">
                  <div class="d-flex align-items-center gap-3 p-3 bg-light rounded border border-theme">
                    <div class="avatar bg-secondary text-white d-flex justify-content-center align-items-center rounded-circle" style="width: 40px; height: 40px;">
                      {{ loginForm.get('email')?.value?.charAt(0).toUpperCase() || "U" }}
                    </div>
                    <div>
                      <p class="fw-bold text-dark mb-0">{{ loginForm.get('email')?.value || "No Username Entered" | translate }}</p>
                      <p class="text-muted small mb-0">{{"Selected User"|translate}}</p>
                    </div>
                  </div>
                  <input hidden
                    type="text"
                    class="form-control"
                    id="floatingInput"
                    placeholder="username"
                    formControlName="email"
                  />
                </div>
                <div class="form-floating mb-4">
                  <input
                    type="password"
                    class="form-control"
                    id="floatingPassword"
                    placeholder="Password"
                    formControlName="password"
                    [ngClass]="{'is-disabled': this.loginForm.value.email == ''}"
                  />
                  <label for="floatingPassword">{{ "Password" | translate }}</label>
                </div>
                <button
                  type="submit"
                  [disabled]="loginButton == 'Processing'"
                  [ngClass]="{'is-disabled': this.loginForm.value.email == ''}"
                  class="btn btn-primary w-100"
                >
                  {{ loginButton | translate }}
                </button>
              </form>
            </div>

            <!-- Divider -->
            <div class="d-none d-md-flex align-items-stretch px-3">
              <div class="vr bg-dark opacity-25" style="width: 2px;"></div>
            </div>

            <!-- User List -->
            <div class="w-100 w-md-50 ps-md-4">
              <h5 class="mb-3 fw-bold">{{"Select User to login"|translate}}</h5>
              <div class="scrollable-user-list" >
                <div class="d-flex flex-column gap-3">
                  <div
                    *ngFor="let user of usersList"
                    class="d-flex align-items-center gap-3 p-3 bg-light rounded border "
                    (click)="selectUser(user)"
                    style="cursor: pointer;"
                  >
                    <div
                      class="avatar bg-secondary text-white d-flex justify-content-center align-items-center rounded-circle"
                      style="width: 40px; height: 40px;"
                    >
                      {{ user.charAt(0).toUpperCase() }}
                    </div>
                    <div>
                      <p class="fw-bold text-dark mb-0">{{ user }}</p>
                      <p class="text-muted small mb-0">{{"Click to select"|translate}}</p>
                    </div>
                  </div>
                </div>
              </div>
            </div>

          </div>
        </div>
      </div>
    </div>
  </div>
</section>