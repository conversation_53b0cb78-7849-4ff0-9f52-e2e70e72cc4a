import Notiflix from 'notiflix';
import { Router } from '@angular/router';
import { Component, OnInit } from '@angular/core';
import { AuthService } from '../../services/auth.service';
import { FormBuilder, FormGroup, ReactiveFormsModule, Validators } from '@angular/forms';
import { jwtDecode } from 'jwt-decode';
import { MachineService } from '../../services/machine.service';
import { CommonModule } from '@angular/common';
import { processing } from '../../utils/enums';
import { MouldService } from '../../services/mould.service';
import { TranslatePipe, TranslateService } from '../../translate';

@Component({
  selector: 'app-login',
  standalone: true,
  imports: [ReactiveFormsModule, CommonModule, TranslatePipe],
  templateUrl: './login.component.html',
  styleUrl: './login.component.scss'
})

export class LoginComponent implements OnInit{
  usersList: any = []
  loginButton: string = 'Continue'
  loginForm: FormGroup; // FormGroup is a class that tracks the value and validity state of a group of FormControl instances.
  constructor(private formbBuilder: FormBuilder, private authService: AuthService, private readonly router: Router,
    private machineService: MachineService, private mouldService: MouldService, private translate: TranslateService) {
    // If user is already logged in, redirect to mould page which is the landing page
    if(this.authService.isLoggedIn())
      this.router.navigate([`/mould`]);
    
    // Initialize the login form with email and password fields
    this.loginForm = this.formbBuilder.group({
      email: ['', Validators.required],
      password: ['', Validators.required]
    });

    this.getListOfUsers()
  }
  
  ngOnInit(): void { }
  selectUser(user: string): void {
    this.loginForm.patchValue({ email: user });
  }

  getListOfUsers(flag=false){
    this.authService.users().subscribe({
      next: (loginResult)=>{
          this.usersList = loginResult['usernames']
          if(flag){
            window.location.reload()
          }
          // const token = loginResult.access_token
          // const decodedToken: any = jwtDecode(token);
          // localStorage.setItem('Bearer Token', JSON.stringify(token));
          // localStorage.setItem('username', JSON.stringify(decodedToken.sub));
          // localStorage.setItem('userRole', JSON.stringify(loginResult.role));
      },
      error: (err)=>{
        setTimeout(()=>{
          this.getListOfUsers(true)
        },10000)
      }
    })
      
  }
 
  get userLoggedIn(){
    return this.authService.isLoggedIn()
  }

  loadUser(username: string){
    this.loginForm.patchValue({ email: username }); 
  }
  /**
   *  Function to handle the login form submission and set token in local storage if the form is valid
   */
  onLogin() {
    
    if (this.loginForm.valid) {
      this.loginButton = processing.processing
      Notiflix.Loading.circle();
      const userEmail = this.loginForm.value.email
      const userPassword = this.loginForm.value.password
      // Reset the form
      let data = {
        "username": userEmail,
        "password": userPassword
      }

      this.authService.loginApi(data).subscribe(loginResult =>{
        const token = loginResult.access_token
        const decodedToken: any = jwtDecode(token);
        localStorage.setItem('Bearer Token', JSON.stringify(token));
        localStorage.setItem('username', JSON.stringify(decodedToken.sub));
        localStorage.setItem('userRole', JSON.stringify(loginResult.role));
        this.machineService.loadAllData()
        this.machineService.verifyPumpReady()
        this.machineService.verifyEmergencyAlert()
        this.machineService.verifyFilterPressure()
        this.mouldService.verifyMouldTestStatus()
        this.mouldService.verifyPurgeStatus()
        this.router.navigate([`/mould`]);
        Notiflix.Notify.success(this.translate.instant('Logged in successfully!'));
        Notiflix.Loading.remove();
        this.loginButton = 'Continue'
        // this.mouldService.autoFilterCleaning()
      },
      err =>{
        Notiflix.Loading.remove();
        this.loginButton = 'Continue'
        // Notiflix.Notify.failure(err?.error?.detail);
      })
    }
  }

}
