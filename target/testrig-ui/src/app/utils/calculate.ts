
import configs from './configs';

const viscosity: {
  temperature: number[];
  kinematicViscosity: number;
}[] = [
  {
    temperature: [0, 2.4],
    kinematicViscosity: 0.000001787
  },
  {
    temperature: [2.5, 7.4],
    kinematicViscosity: 0.000001519
  },
  {
    temperature: [7.5, 14.9],
    kinematicViscosity: 0.000001307
  },
  {
    temperature: [15, 24.9],
    kinematicViscosity: 0.000001004
  },
  {
    temperature: [25, 34.9],
    kinematicViscosity: 0.000000801
  },
  {
    temperature: [35, 44.9],
    kinematicViscosity: 0.000000658
  },
  {
    temperature: [45, 54.9],
    kinematicViscosity: 0.000000553
  },
  {
    temperature: [55, 64.9],
    kinematicViscosity: 0.000000475
  },
  {
    temperature: [65, 74.9],
    kinematicViscosity: 0.000000413
  },
  {
    temperature: [75, 84.9],
    kinematicViscosity: 0.000000365
  },
  {
    temperature: [85, 94.9],
    kinematicViscosity: 0.000000326
  },
  {
    temperature: [95, Infinity],
    kinematicViscosity: 0.00000029
  }
];

// export const round = (num: number) => Math.round((num + Number.EPSILON) * 100) / 100;

export function round(num:any):number {
  const multiplier = Math.pow(10, 1);
  const roundedNum = Math.round(num * multiplier) / multiplier;
  return Number(roundedNum.toFixed(1));
}

export const calcReynolds = (flow: number, diameter: number, temperature: number): number => {
  if (!diameter && diameter !=0 ) {
    return 0;
  }
  const dia = diameter / 1000;
  const A =  Math.PI * dia * dia * 0.25; //MR commented (Math.PI * diameter) ^ (2 / 4);
  const flow_m3_s = flow / 1000 / 60;
  const v_m = flow_m3_s / A; // MR Commented flow / A;
  const nu: any = viscosity.find(i => temperature >= i.temperature[0] && temperature < i.temperature[1])?.kinematicViscosity;
  return round(((v_m * dia) / nu));
};

export const calcDeltaT = (temp: number, inletTemp: number) => round(temp - inletTemp);

export const getFlowMinMax = (channelId: number, sensorType?: number): { min: number; max: number } => {
  let SelectedUnit = 'Metric';
  // if (localStorage.length > 0) {
  //   const unit = localStorage.getItem('SelectedUnit');
  //     if(unit) {
  //       SelectedUnit = JSON.parse(unit);
  //     }
  //     // if(SelectedUnit === 'Metric') {
  //     const values = configs.flows[1];
  //     return { min: configs.flow.min, max: configs.flow.max };
  //     // } else {
  //       // const values = configsimperial.flows[sensorType];
  //       // return values || { min: configsimperial.flow.min, max: configsimperial.flow.max };
  //       // }
  // }
  if(channelId < 3)
    return { min: 20, max: 400 };
  else if(channelId < 11)
    return { min: 2, max: 40 };
  else
    return { min: 1, max: 20 };
  
};

export const getMinMax = (
  channel_id: number,
  channelType: string,
  sensorType?: number
  ): { min: number; max: number } => {
  if (channelType === 'temperature') {
    let SelectedUnit = 'Metric';
    if (localStorage.length > 0) {

      const unit = localStorage.getItem('SelectedUnit');
      if(unit) {
        SelectedUnit = JSON.parse(unit);
      }
      return { min: configs.temperature.min, max: configs.temperature.max };
    }
    
  }
  return getFlowMinMax(channel_id, sensorType);
};

// export const checkIfEnabled = (value)=>{
  
//   if(value != 0){
//     return true
//   }
//   return false
// }

/**
 * Purpose: Calculate the heat transfered when mass undergoes a temperature change under specific heat capacity.
 * Heat transfered Q = mcΔT
 * m : Mass Flow Rate (kg/s)=(Flow Rate (L/min)× Density of Water (kg/L)/60 )
 * c : Specific Heat Capacity of Water (c): 4,186 J/(kg·°C)
 * Density of Water: 1 kg/L
 * @param flowRate 
 * @param deltaTemperature 
 * @returns 
 */
// export const energySavedPerChannel = (flowRate: float, deltaTemperature: number) => {
//   if (flowRate && !isNaN(deltaTemperature)) {
//     return round(((flowRate * 1) / 60) * 4186 * deltaTemperature);
//   }
// }