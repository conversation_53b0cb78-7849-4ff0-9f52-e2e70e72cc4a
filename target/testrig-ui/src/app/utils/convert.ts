export const getImperialFlowValue = (obj: number): number => {
  const Impflow = obj * 0.12641;
  return Impflow;
};
export enum InletType{
temp="temp",
pressure="pressure",
deltaPressure="deltaPressure"
}
export enum Units{Metric="Metric",Imperial="Imperial"}
export const getActualValue = (matricValue:any,inletType:InletType,units:any)=>{
if(inletType == InletType.temp){
  if(units == Units.Imperial)
    return getImperialTempValue(matricValue)
  else
    return matricValue
}else if(inletType == InletType.pressure){
  if(units == Units.Imperial)
    return getImperialPressureValue(matricValue)
  else
    return matricValue
}
}
export const getImperialTempValue = (obj: number): number => {
  const Imptemp = (obj * 1.8) + 32;
  return Imptemp;
};

export const getImperialPressureValue = (obj: number): number => {
  const ImpPressure = obj * 14.5037738;
  return ImpPressure;
};

export const getImperialDiameterValue = (obj: number): number => {
  const ImpPressure = obj * 0.03937;
  return ImpPressure;
};