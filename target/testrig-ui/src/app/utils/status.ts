import { Channel } from "../models/channel.model";
import { ManifoldChannel } from "../models/machine.model";
import { round } from "./calculate";
import { Status } from "./enums";

export const getFlowStatus = (config: ManifoldChannel, channel: Channel): Status => {
  // const flow = config?.sensors.find(i => i.type === 'flow');
  const flowValue = round(+channel?.values?.sensor_val);
  if (!config || !config.channel_enabled)
    return Status.Grey
  // let percentageValue = (config.channel_upper_limit - config.channel_lower_limit)/10
  // if(typeof(flowValue) == "string")
  //   return Status.Red
  // if((+flowValue > +config.channel_upper_limit) || (+flowValue < +config.channel_lower_limit))
  //   return Status.Red
  // else if((((+config.channel_upper_limit - +percentageValue) <= +flowValue) && (+config.channel_upper_limit >= +flowValue))
  //         || (((+config.channel_lower_limit + +percentageValue) >= +flowValue) && (+config.channel_lower_limit <= +flowValue)))
  //   return Status.Yellow
  return Status.Blue
};

// export const getTempStatus = (config: ManifoldChannel, channel: Channel): Status => {
//   const temp = config?.sensors.find(i => i.type === 'temperature');
//   if (!temp || !temp.limits_enabled)
//   return Status.Grey;
//   let percentageValue = (temp.max - temp.min)/10
//   const tempValue = round(channel?.values?.temp);
//   if((+tempValue > +temp.max) || (+tempValue < +temp.min))
//     return Status.Red 
//   else if((((+temp.max - +percentageValue) <= +tempValue) && (+temp.max >= +tempValue))
//     || (((+temp.min + +percentageValue) >= +tempValue) && (+temp.min <= +tempValue)))
//     return Status.Yellow
//   return Status.Green
// };


export const getStatus = (config: ManifoldChannel, channel: Channel): Status => {
    const flowStatus = getFlowStatus(config, channel);
    // const tempStatus = getTempStatus(config, channel);
    return Math.max(flowStatus);
  };