import * as moment from 'moment-timezone';
 
/**
* Converts a GMT/UTC time string to French time (Europe/Paris)
* @param gmtTime - A UTC time string (e.g., "2025-05-14T12:00:00Z")
* @returns Formatted French time (e.g., "2025-05-14T14:00:00+02:00")
*/
export function convertGMTtoFrenchTime(gmtTime: string): string {
  return moment.utc(gmtTime).tz('Europe/Paris').format('DD-MM-YYYY HH:mm:ss'); // ISO format
}