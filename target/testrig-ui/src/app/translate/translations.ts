// app/translate/translation.ts

import { InjectionToken } from '@angular/core';

// import translations
import { LANG_FR_NAME, LANG_FR_TRANS } from './lang-fr';


// translation token
export const TRANSLATIONS = new InjectionToken('translations');

// all traslations
export const dictionary = {
	[LANG_FR_NAME]: LANG_FR_TRANS,
};

// providers
export const TRANSLATION_PROVIDERS = [
	{ provide: TRANSLATIONS, useValue: dictionary },
];