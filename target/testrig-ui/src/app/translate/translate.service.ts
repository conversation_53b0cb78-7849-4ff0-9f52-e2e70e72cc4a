import {Injectable, Inject} from '@angular/core';
import { TRANSLATIONS } from './translations'; // import our opaque token

@Injectable()
export class TranslateService {
	public currentLang: string = 'en'; // default language
	
	// public get currentLang() {
	//   return this._currentLang;
	// }

  // inject our translations
	constructor(@Inject(TRANSLATIONS) private _translations: any) {
	}

	public use(lang: string): void {
		// set current language
		this.currentLang = lang;
	}

	public translate(key: string): string {
		// private perform translation
		let translation = key;
    
    if (this._translations[this.currentLang] && this._translations[this.currentLang][key]) {
			return this._translations[this.currentLang][key];
		}

		return translation;
	}

	public instant(key: string,value="") {
		// public perform translation
		if(value){
			let translatedData = this.translate(key); 
			translatedData = translatedData.replace('{{value}}', value);
			return translatedData;
		}
		return this.translate(key); 
	}
	public translateWithLang(key: string,lng=''): string {
		// private perform translation
		let translation = key;
		if(lng == 'en'){
				return key;
		}
    	if (this._translations[this.currentLang] && this._translations[this.currentLang][key]) {
			return this._translations[this.currentLang][key];
		}
		

		return translation;
	}
}