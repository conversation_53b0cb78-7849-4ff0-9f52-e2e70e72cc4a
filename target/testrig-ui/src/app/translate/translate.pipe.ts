// app/translate/translate.pipe.ts

import { Pipe, PipeTransform } from '@angular/core';
import { TranslateService } from './translate.service'; // Import directly from the service

@Pipe({
    name: 'translate',
    pure: false, // impure pipe, update value when we change language
    standalone: true 
})

export class TranslatePipe implements PipeTransform {

	constructor(private _translate: TranslateService) { }

	transform(value: string,string = 'fr'): any {
		if (!value) return;
		
		return this._translate.translateWithLang(value,string);
	}
}