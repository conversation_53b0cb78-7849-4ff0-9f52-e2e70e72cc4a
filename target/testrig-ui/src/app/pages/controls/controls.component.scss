@import '../../../styles/variables';

.intel-temp {
  &-title {
    color: $primary;
    font-size: 18px;
    font-weight: bold;
    line-height: 16px;
  }
  &-value {
    color: $blue;
    font-size: 12px;
    line-height: 16px;
    text-align: right;
  }
  i {
    font-size: 8px;
    color: $dark-grey;
  }
}

.loading {
  display: flex;
  width: 100%;
  height: 100%;
  align-items: center;
  justify-content: center;
  .lds-roller {
    display: inline-block;
    position: relative;
    width: 80px;
    height: 80px;
  }
  .lds-roller div {
    animation: lds-roller 1.2s cubic-bezier(0.5, 0, 0.5, 1) infinite;
    transform-origin: 40px 40px;
  }
  .lds-roller div:after {
    content: ' ';
    display: block;
    position: absolute;
    width: 7px;
    height: 7px;
    border-radius: 50%;
    background: $blue;
    margin: -4px 0 0 -4px;
  }
  .lds-roller div:nth-child(1) {
    animation-delay: -0.036s;
  }
  .lds-roller div:nth-child(1):after {
    top: 63px;
    left: 63px;
  }
  .lds-roller div:nth-child(2) {
    animation-delay: -0.072s;
  }
  .lds-roller div:nth-child(2):after {
    top: 68px;
    left: 56px;
  }
  .lds-roller div:nth-child(3) {
    animation-delay: -0.108s;
  }
  .lds-roller div:nth-child(3):after {
    top: 71px;
    left: 48px;
  }
  .lds-roller div:nth-child(4) {
    animation-delay: -0.144s;
  }
  .lds-roller div:nth-child(4):after {
    top: 72px;
    left: 40px;
  }
  .lds-roller div:nth-child(5) {
    animation-delay: -0.18s;
  }
  .lds-roller div:nth-child(5):after {
    top: 71px;
    left: 32px;
  }
  .lds-roller div:nth-child(6) {
    animation-delay: -0.216s;
  }
  .lds-roller div:nth-child(6):after {
    top: 68px;
    left: 24px;
  }
  .lds-roller div:nth-child(7) {
    animation-delay: -0.252s;
  }
  .lds-roller div:nth-child(7):after {
    top: 63px;
    left: 17px;
  }
  .lds-roller div:nth-child(8) {
    animation-delay: -0.288s;
  }
  .lds-roller div:nth-child(8):after {
    top: 56px;
    left: 12px;
  }
  @keyframes lds-roller {
    0% {
      transform: rotate(0deg);
    }
    100% {
      transform: rotate(360deg);
    }
  }
}

.cards {
  .row{
    row-gap: 20px;
  }
}

.sticky-content{ 
  min-height: auto;
  border-bottom: 0;
  padding: 0 0 25px;
  padding-top: 0;
}

.temprature {
  padding: 25px 30px;
  background: #f4f7f8;
  // position: sticky;
  // z-index: 11;
  // top: 0;

  .temprature-inner
  {
    @media screen and (max-width:767px)
    {
      flex-wrap: wrap;
      gap: 10px;
    }

   
  }
}


.app-wrapper.sticky-content .cards {
  padding: 0 30px;
  width: 100%;
  display: grid;
  grid-template-columns: repeat(6, 1fr);
  grid-template-rows: masonry;
  gap: 10px;
  row-gap: 20px;

  // display: flex;
  justify-content: center;  
  //   .main-box {
  //     width: 350px;
  // }
}



@media only screen and (max-width: 2400px) {

  .app-wrapper.sticky-content .cards {
    grid-template-columns: repeat(6, 1fr);
  }

}


@media only screen and (max-width: 1920px) {

  .app-wrapper.sticky-content .cards {
    grid-template-columns: repeat(6, 1fr);
  }

}

@media only screen and (max-width: 1440px) {

  .app-wrapper.sticky-content .cards {
    grid-template-columns: repeat(6, 1fr);
  }

}

@media only screen and (max-width: 1024px) {


  .app-wrapper.sticky-content .cards {
    grid-template-columns: repeat(2, 1fr);
  }

  .sticky-content{height: calc(100vh - 141px);}
}

@media only screen and (max-width: 768px) {

  .app-wrapper.sticky-content .cards {
    grid-template-columns: repeat(1, 1fr);
  }
  .app-wrapper.sticky-content .cards {padding: 0 20px;}

}

// Size of no data icon
.noDataSmallBlack{
  width: 180px;
  margin: 70px 0;
  filter: opacity(0.4);
}
.temprature-boxes {
  padding: 30px 10px;
  display: flex;
  justify-content: center;
  gap: 15px;

  .temp-box-inner {
    box-shadow: 0px 12px 20px 0px #E0F3FD;
    width: 170px;
    background: #fff;
    border-radius: 8px;
    // border-top: 4px solid $green;
    border-top: 4px solid $button-blue;
    height: auto;
    padding: 8px 12px 12px;
    span 
    {
      color: #9B9B9B;
      font-size: 14px;
      display: flex;
      gap: 8px;
      margin-bottom: 5px;
    }
    h6 
    {
      margin: 0;
      font-weight: 600;
      font-size: 18px;
    }
  }
}


.card-heading
{
    padding: 20px;
    border-bottom: 1px solid $border-color;

    h4 
    {
        color: $header-blue;
        font-size: 22px;
        font-weight: 500;
    }
}
.select-Module {
    padding: 20px;
    overflow: auto;
    height: calc(100vh - 290px);
    .list-group
    {
        list-style-type: none;
        display: flex;
        flex-direction: column;
        gap: 8px;

        li 
        {
            background-color: #F8F8F8;
            border:1px solid #F8F8F8;
            border-radius: 4px;
            color: #868585;
            font-size: 16px;
            display: inline-flex;
            justify-content: space-between;
            padding: 13px 20px;
            cursor: pointer;

            &.selected 
            {
                border-color: $button-blue;
                background-color: #F4FCFF;
                color: #000000;
            }
        }
    }
}
.test-module
{
    padding: 20px;
    border-top: 1px solid $border-color;

   
}