<div class="app-wrapper sticky-content" [ngClass]="testStatus? ' readonly-div': ''">

  <!-- If API is null or returning error then showing No data in Mould -->
  <div class="container text-center my-5 align-items-center opacity-75" *ngIf="dataInMould == 'noData'">
    <div class="alert alert-danger" role="alert" >
      No data in Mould!
    </div>
  </div>
  
  <!-- Data from API -->
  <div>
    <div class="temprature-boxes">
      <div class="temp-box-inner">
        <span>{{"Inlet Temperature"|translate}}</span>
        <h6 *ngIf="inletTemps">{{ inletTemps[0].toFixed(1) }} °C</h6>
        <h6 *ngIf="!inletTemps"> 0 </h6>
      </div>
      <div class="temp-box-inner">
        <span>{{"Inlet Pressure"|translate}}</span>
        <h6 *ngIf="pressure.inletPressure || pressure.inletPressure == 0">{{ pressure.inletPressure }} bar</h6>
        <h6 *ngIf="!pressure.inletPressure && pressure.inletPressure !=0"> 0 </h6>
      </div>
      <div class="temp-box-inner">
        <span> 
          <img  src="assets/images/traingle.svg">
          {{"Pressure"|translate}}</span>
        <h6 *ngIf="pressure.deltaPressure || pressure.deltaPressure == 0">{{ pressure.deltaPressure }} bar</h6>
        <h6 *ngIf="!pressure.deltaPressure && pressure.deltaPressure !=0"> 0 </h6>
      </div>
      <div class="temp-box-inner">
        <span> 
          {{"Filter Pressure"|translate}}</span>
        <h6 *ngIf="this.pressure.filterPressure || this.pressure.filterPressure == 0">{{ this.pressure.filterPressure }} bar</h6>
        <h6 *ngIf="!this.pressure.filterPressure && this.pressure.filterPressure !=0"> 0 </h6>
      </div>
    </div>

    
    <div class="cards" *ngIf="channel">
      <div class="main-box" *ngFor="let config of manifold?.channels; let i = index; trackBy: trackConfig" >
        <a class="text-decoration-none">
          <app-channel-card
            [channel]="channel.channels[i]"
            [channelNo]="i + 1"
            [config]="config"
            [inletTemp]="channel.inlet_temps[0]"
          ></app-channel-card>
        </a>
      </div>
    </div>

    <!-- Following div act as the skeleton -->
    <div class="cards" *ngIf="!channel">
      <div class="main-box" *ngFor="let _ of skeletonArray">
        <div class="channel-card" style="height: 260px;">
          <div class="channel-card-header size-2 position-relative">
            <div class="header-border b-status--1"></div>
          </div>
      
          <div class="channel-card-body">
            <div class="d-flex flex-column">
              <span class="label">{{"Reynolds"|translate}}</span>
              <span class="value"> - </span>
            </div>
            <div class="d-flex ml-auto" style="column-gap: 15px;">
              <div class="chart"  *ngFor="let temp of [1, 2]">
                <div class="chart-label">
                  <div class="chart-label-item">
                    <span class="chart-label-item-text" [innerHTML]="20"> </span>
                    <div class="chart-label-item-line"></div>
                  </div>
                  <div *ngFor="let temp of [1, 2, 3, 4]" class="chart-label-item">
                    <div class="chart-label-item-line"></div>
                  </div>
                  <div class="chart-label-item">
                    <span class="chart-label-item-text" [innerHTML]="400"></span>
                    <div class="chart-label-item-line"></div>
                  </div>
                  <div class="chart-label-item primary position-absolute" [ngStyle]="{ height: 100 + '%' }">
                    <div class="chart-label-item-line"></div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
  