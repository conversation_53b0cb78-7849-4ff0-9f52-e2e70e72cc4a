<div class="title" [innerHtml]="unit"></div>

<div class="chart">
  <div class="chart-label">
    <div class="chart-label-item">
      <span class="chart-label-item-text" [innerHTML]="from"> </span>
      <div class="chart-label-item-line"></div>
    </div>
    <div *ngFor="let temp of [1, 2, 3, 4]" class="chart-label-item">
      <div class="chart-label-item-line"></div>
    </div>
    <div class="chart-label-item">
      <span class="chart-label-item-text" [innerHTML]="to"></span>
      <div class="chart-label-item-line"></div>
    </div>
    <div class="chart-label-item primary position-absolute" [ngStyle]="{ height: (height > 100 ? 98 : height) + '%' }">
      <div class="chart-label-item-line"></div>
    </div>
    <!-- Lower limit -->
    <!-- <div
      *ngIf="min && type == 'flow'"
      class="chart-label-item blue position-absolute down-5"
      [ngStyle]="{ height: (minHeight > 0? minHeight + '%' : 0) }"
    >
      <span class="chart-label-item-text" [innerHTML]="min < from ? '< ' +  from : min "> </span>
      <div class="chart-label-item-line"></div>
    </div> -->

    <!-- Upper limit  -->
    <!-- <div
      *ngIf="max && type == 'flow'"
      class="chart-label-item blue position-absolute up-5"
      [ngStyle]="{ height: (maxHeight > 100 ? 98 : maxHeight) + '%' }"
    >
      <span class="chart-label-item-text" [innerHTML]="max >  to ? '>' + to : max"> </span>
      <div class="chart-label-item-line"></div>
    </div> -->
  </div>
  <div class="chart-value" [ngStyle]="{ height: (height > 100 ? 100 : height) + '%' }" [ngClass]="'b-status-' + status">
    <div class="chart-value-content ">
      <div class="width-value d-flex">{{ flowValue(value,{to,from}) }} </div>
      <!-- <div class="chart-value-content-unit" [innerHtml]="value ? unit : ''"></div> -->
    </div>
  </div>
</div>
