import { Component, Input } from '@angular/core';
import { <PERSON>ifold, ManifoldChannel, Sensor } from '../../../models/machine.model';
import { Channel } from '../../../models/channel.model';
import { Status } from '../../../utils/enums';
import { MachineService } from '../../../services/machine.service';
import { Router } from '@angular/router';
import { sensorTypes } from '../../config/config';
import { getMinMax, round } from '../../../utils/calculate';
import configs from '../../../utils/configs';
import { getImperialFlowValue, getImperialTempValue } from '../../../utils/convert';
import { getFlowStatus } from '../../../utils/status';
import { CommonModule } from '@angular/common';

@Component({
  selector: 'app-chart-verical',
  standalone: true,
  imports: [CommonModule],
  templateUrl: './chart-verical.component.html',
  styleUrl: './chart-verical.component.scss'
})
export class ChartVericalComponent {
  @Input()
  type: 'flow' | 'temperature';
  @Input()
  config: ManifoldChannel;
  @Input()
  channel: Channel;

  public channelAlert:any = {}
  public value: number;
  public height = 0;
  // public minHeight = 0;
  // public maxHeight = 0;
  public unit: string;
  // public min: number;
  // public max: number;
  public from: number;
  public to: number;
  public status: Status;
  public SelectedUnit = 'Metric';
  constructor(private readonly machineService: MachineService,private readonly router: Router) {
  }
  public sensorTypes:any; //= sensorTypesImperial;

  ngOnChanges() {
    this.sensorTypes =  sensorTypes;
    if (this.type && this.config && this.channel) {
      this.initChart();
    }
  }
  // public get manifold(): any {
    // return dummyManifoldData
    // return this.machineService.manifold;
  // }

  // public flowValue(value){
  //   if(this.type === 'flow') {
  //     const sensorTypesValue:number = this.manifold?.sensor_types
  //     this.sensorTypes = this.SelectedUnit === 'Metric' ? sensorTypes : sensorTypesImperial;    // Update sensorTypes accoriding to selected unt(metric/imperial)
  //     let sensorTypeObj = this.sensorTypes.find((i)=>i.value === sensorTypesValue);
  //     let senserValue = sensorTypeObj.label;
  //     let sensorVal = senserValue.split('-')[0]
  //     // if(value.toString().includes('.'))
  //     value = this.SelectedUnit === 'Metric'? value:getImperialFlowValue(value)  // Convert in imperial 
  //     return value < Number(sensorVal)? '< '+sensorVal: round(value)
  //   } else {
  //       if(value.toString().includes('.'))
  //         value = round(value)
  //       return value
  //   }
  // }
  public flowValue(value:any,limits:any, type?:any){
    // const sensorTypesValue:number = this.manifold?.sensor_types
    const sensorTypesValue:number = 1
    this.sensorTypes =  sensorTypes;    // Update sensorTypes accoriding to selected unt(metric/imperial)
    let sensorTypeObj = this.sensorTypes.find((i:any)=>i.value === sensorTypesValue);
    let senserValue = sensorTypeObj?.label;
    let sensorVal = senserValue?.split('-')[0]
    if(limits.to < value){
      this.machineService.globalShare.next({'alert-FOL':true})
      const channelIdkey = 'alert-FOL-' + this.channel.id;
      this.machineService.globalShare.next({ [channelIdkey]: true });
    }else{
      const channelIdkey = 'alert-FOL-' + this.channel.id;
      // this.machineService.globalShare.next({ [channelIdkey]: false });
    }


    // if(value.toString().includes('.'))
    // value = this.SelectedUnit === 'Metric'? round(value):round(getImperialFlowValue(value))  // Convert in imperial 
    return value < sensorVal? `< ${sensorVal}`: round(value)
  }



  private get flow(): any {
    // return this.config?.sensors.find(i => i.type === 'flow');
    return this.config;
  }

  private get temperature(): any {
    // return this.config?.sensors.find(i => i.type === 'temperature');
    return this.config
  }

  private initChart() {
    this.loadLocalStorage();
    let config = undefined;
    config = configs[this.type];
    const minMax = getMinMax(this.channel.id, this.type, 1 ); // here 1 is sensor type
    this.from = minMax.min;
    this.to = minMax.max;
    this.unit = config.unit;   
    
    if(this.SelectedUnit === 'Metric') {
      // this.min = this.type === 'flow' ? round(this.flow?.channel_lower_limit) : round(this.temperature?.channel_lower_limit);
      // this.max = this.type === 'flow' ? round(this.flow?.channel_upper_limit) : round(this.temperature?.channel_upper_limit);
      this.value =
      this.type === 'flow' ? this.channel?.values?.sensor_val : this.channel?.values?.temp;
      
    } else {
      // this.min = this.type === 'flow' ? round(getImperialFlowValue(this.flow?.channel_lower_limit)) : round(getImperialTempValue(this.temperature?.channel_lower_limit));
      // this.max = this.type === 'flow' ? round(getImperialFlowValue(this.flow?.channel_upper_limit)) : round(getImperialTempValue(this.temperature?.channel_upper_limit));
      this.value = this.type === 'flow' ? round(getImperialFlowValue(this.channel?.values?.sensor_val)) : round(getImperialTempValue(this.channel?.values?.temp));
    }
    this.status = getFlowStatus(this.config, this.channel);
    // this.minHeight = this.value ? ((this.min - this.from) * 100) / (this.to - this.from) : 0;
    // this.maxHeight = (this.value || this.value == 0) ? ((this.max - this.from) * 100) / (this.to - this.from) : 0;
    this.height = this.value
      ? Math.max(((this.value - this.from) * 100) / (this.to - this.from), 0)
      : 0;
  }

  public loadLocalStorage() {
    if (localStorage.length > 0) {

      const unit = localStorage.getItem('SelectedUnit')
      if(unit) {
        this.SelectedUnit = JSON.parse(unit);
      }
    }
  }

}
