import { Type } from "../../models/machine.model"

export let machine = {
    "mac": "60:bd:91:00:0e:a1",
    "manifolds": [
        {
            "channel_no": 8,
            "channels": [
                {
                    "diameter": "11",
                    "id": 1,
                    "name": "Channel 1",
                    "sensors": [
                        {
                            "limits_enabled": true,
                            "max": "20",
                            "min": "1",
                            "type": "flow"
                        },
                        {
                            "limits_enabled": true,
                            "max": "120",
                            "min": "2",
                            "type": "temperature"
                        }
                    ],
                    "ui_status": 0
                },
                {
                    "diameter": "11",
                    "id": 2,
                    "name": "Channel 2",
                    "sensors": [
                        {
                            "limits_enabled": true,
                            "max": "20",
                            "min": "1",
                            "type": "flow"
                        },
                        {
                            "limits_enabled": true,
                            "max": "120",
                            "min": "2",
                            "type": "temperature"
                        }
                    ],
                    "ui_status": 0
                },
                {
                    "diameter": "11",
                    "id": 3,
                    "name": "Channel 3",
                    "sensors": [
                        {
                            "limits_enabled": true,
                            "max": "20",
                            "min": "1",
                            "type": "flow"
                        },
                        {
                            "limits_enabled": true,
                            "max": "120",
                            "min": "2",
                            "type": "temperature"
                        }
                    ],
                    "ui_status": 0
                },
                {
                    "diameter": "11",
                    "id": 4,
                    "name": "Channel 4",
                    "sensors": [
                        {
                            "limits_enabled": true,
                            "max": "20",
                            "min": "1",
                            "type": "flow"
                        },
                        {
                            "limits_enabled": true,
                            "max": "120",
                            "min": "2",
                            "type": "temperature"
                        }
                    ],
                    "ui_status": 0
                },
                {
                    "diameter": "11",
                    "id": 5,
                    "name": "Channel 5",
                    "sensors": [
                        {
                            "limits_enabled": true,
                            "max": 20,
                            "min": "1",
                            "type": "flow"
                        },
                        {
                            "limits_enabled": true,
                            "max": "120",
                            "min": "2",
                            "type": "temperature"
                        }
                    ],
                    "ui_status": 0
                },
                {
                    "diameter": "11",
                    "id": 6,
                    "name": "Channel 6",
                    "sensors": [
                        {
                            "limits_enabled": true,
                            "max": 20,
                            "min": "1",
                            "type": "flow"
                        },
                        {
                            "limits_enabled": true,
                            "max": "120",
                            "min": "2",
                            "type": "temperature"
                        }
                    ],
                    "ui_status": 0
                },
                {
                    "diameter": "11",
                    "id": 7,
                    "name": "Channel 7",
                    "sensors": [
                        {
                            "limits_enabled": true,
                            "max": 20,
                            "min": "1",
                            "type": "flow"
                        },
                        {
                            "limits_enabled": true,
                            "max": "120",
                            "min": "2",
                            "type": "temperature"
                        }
                    ],
                    "ui_status": 0
                },
                {
                    "diameter": "11",
                    "id": 8,
                    "name": "Channel 8",
                    "sensors": [
                        {
                            "limits_enabled": true,
                            "max": "20",
                            "min": "1",
                            "type": "flow"
                        },
                        {
                            "limits_enabled": true,
                            "max": "120",
                            "min": "2",
                            "type": "temperature"
                        }
                    ],
                    "ui_status": 0
                },
                {
                    "diameter": "9",
                    "id": 9,
                    "name": "Channel 9",
                    "sensors": [
                        {
                            "limits_enabled": true,
                            "max": 20,
                            "min": "1",
                            "type": "flow"
                        },
                        {
                            "limits_enabled": true,
                            "max": 120,
                            "min": 2,
                            "type": "temperature"
                        }
                    ],
                    "ui_status": 0
                },
                {
                    "diameter": "9",
                    "id": 10,
                    "name": "Channel 10",
                    "sensors": [
                        {
                            "limits_enabled": true,
                            "max": 20,
                            "min": "1",
                            "type": "flow"
                        },
                        {
                            "limits_enabled": true,
                            "max": 120,
                            "min": 2,
                            "type": "temperature"
                        }
                    ],
                    "ui_status": 0
                },
                {
                    "id": 11,
                    "name": "channel 11",
                    "sensors": [
                        {
                            "limits_enabled": true,
                            "max": 20,
                            "min": "1",
                            "type": "flow"
                        },
                        {
                            "limits_enabled": true,
                            "max": 120,
                            "min": 2,
                            "type": "temperature"
                        }
                    ],
                    "ui_status": 0
                },
                {
                    "id": 12,
                    "name": "channel 12",
                    "sensors": [
                        {
                            "limits_enabled": true,
                            "max": 20,
                            "min": "1",
                            "type": "flow"
                        },
                        {
                            "limits_enabled": true,
                            "max": 120,
                            "min": 2,
                            "type": "temperature"
                        }
                    ],
                    "ui_status": 0
                }
            ],
            "flowChannels": 8,
            "limits": {
                "pressure": {
                    "delta": {
                        "enabled": true,
                        "max": "1",
                        "min": 0
                    },
                    "inlet": {
                        "enabled": true,
                        "max": "4",
                        "min": 1
                    }
                },
                "temp": {
                    "enabled": true,
                    "max": 115,
                    "min": 25
                }
            },
            "mac": "60:bd:91:00:18:01",
            "name": "Manifold-11 ",
            "pressureChannels": 2,
            "pressure_enabled": true,
            "sensor_types": 1,
            "ui_status": 0
        },
        {
            "channel_no": 4,
            "channels": [
                {
                    "diameter": 11,
                    "id": 1,
                    "name": "Channel 1",
                    "sensors": [
                        {
                            "limits_enabled": true,
                            "max": "20",
                            "min": "1",
                            "type": "flow"
                        },
                        {
                            "limits_enabled": true,
                            "max": "120",
                            "min": "2",
                            "type": "temperature"
                        }
                    ],
                    "ui_status": 0
                },
                {
                    "diameter": "11",
                    "id": 2,
                    "name": "Channel 2",
                    "sensors": [
                        {
                            "limits_enabled": true,
                            "max": "20",
                            "min": "1",
                            "type": "flow"
                        },
                        {
                            "limits_enabled": true,
                            "max": "120",
                            "min": "2",
                            "type": "temperature"
                        }
                    ],
                    "ui_status": 0
                },
                {
                    "diameter": "11",
                    "id": 3,
                    "name": "Channel 3",
                    "sensors": [
                        {
                            "limits_enabled": true,
                            "max": "20",
                            "min": "1",
                            "type": "flow"
                        },
                        {
                            "limits_enabled": true,
                            "max": "120",
                            "min": "2",
                            "type": "temperature"
                        }
                    ],
                    "ui_status": 0
                },
                {
                    "diameter": "11",
                    "id": 4,
                    "name": "Channel 4",
                    "sensors": [
                        {
                            "limits_enabled": true,
                            "max": "20",
                            "min": "1",
                            "type": "flow"
                        },
                        {
                            "limits_enabled": true,
                            "max": "120",
                            "min": "2",
                            "type": "temperature"
                        }
                    ],
                    "ui_status": 0
                },
                {
                    "diameter": "11",
                    "id": 5,
                    "name": "Channel 5",
                    "sensors": [
                        {
                            "limits_enabled": true,
                            "max": 20,
                            "min": 1,
                            "type": "flow"
                        },
                        {
                            "limits_enabled": true,
                            "max": 120,
                            "min": 2,
                            "type": "temperature"
                        }
                    ],
                    "ui_status": 0
                },
                {
                    "diameter": 11,
                    "id": 6,
                    "name": "Channel 6",
                    "sensors": [
                        {
                            "limits_enabled": false,
                            "max": 20,
                            "min": 1,
                            "type": "flow"
                        },
                        {
                            "limits_enabled": true,
                            "max": 120,
                            "min": 2,
                            "type": "temperature"
                        }
                    ],
                    "ui_status": 0
                },
                {
                    "diameter": "11",
                    "id": 7,
                    "name": "Channel 7",
                    "sensors": [
                        {
                            "limits_enabled": false,
                            "max": 20,
                            "min": 1,
                            "type": "flow"
                        },
                        {
                            "limits_enabled": true,
                            "max": 120,
                            "min": 2,
                            "type": "temperature"
                        }
                    ],
                    "ui_status": 0
                },
                {
                    "diameter": "11",
                    "id": 8,
                    "name": "Channel 8",
                    "sensors": [
                        {
                            "limits_enabled": false,
                            "max": 20,
                            "min": 1,
                            "type": "flow"
                        },
                        {
                            "limits_enabled": true,
                            "max": 120,
                            "min": 2,
                            "type": "temperature"
                        }
                    ],
                    "ui_status": 0
                },
                {
                    "id": 9,
                    "name": "channel 9",
                    "sensors": [
                        {
                            "limits_enabled": true,
                            "max": 20,
                            "min": 1,
                            "type": "flow"
                        },
                        {
                            "limits_enabled": true,
                            "max": 120,
                            "min": 2,
                            "type": "temperature"
                        }
                    ],
                    "ui_status": 0
                },
                {
                    "id": 10,
                    "name": "channel 10",
                    "sensors": [
                        {
                            "limits_enabled": true,
                            "max": 20,
                            "min": 1,
                            "type": "flow"
                        },
                        {
                            "limits_enabled": true,
                            "max": 120,
                            "min": 2,
                            "type": "temperature"
                        }
                    ],
                    "ui_status": 0
                },
                {
                    "id": 11,
                    "name": "channel 11",
                    "sensors": [
                        {
                            "limits_enabled": true,
                            "max": 20,
                            "min": 1,
                            "type": "flow"
                        },
                        {
                            "limits_enabled": true,
                            "max": 120,
                            "min": 2,
                            "type": "temperature"
                        }
                    ],
                    "ui_status": 0
                },
                {
                    "id": 12,
                    "name": "channel 12",
                    "sensors": [
                        {
                            "limits_enabled": true,
                            "max": 20,
                            "min": 1,
                            "type": "flow"
                        },
                        {
                            "limits_enabled": true,
                            "max": 120,
                            "min": 2,
                            "type": "temperature"
                        }
                    ],
                    "ui_status": 0
                }
            ],
            "flowChannels": 8,
            "limits": {
                "pressure": {
                    "delta": {
                        "enabled": true,
                        "max": "7",
                        "min": "0.1"
                    },
                    "inlet": {
                        "enabled": true,
                        "max": "2.8",
                        "min": "0.1"
                    }
                },
                "temp": {
                    "enabled": true,
                    "max": 120,
                    "min": 10
                }
            },
            "mac": "60:bd:91:00:21:ee",
            "name": "Manifold-12",
            "pressureChannels": 2,
            "pressure_enabled": false,
            "sensor_types": 1,
            "ui_status": 0
        }
    ],
    "name": "Machine-1-1",
    "ui_status": 0
}

export let data = {
    "time": "2025-01-02 07:00:45+00:00",
    "inlet_temps": [
        61
    ],
    "channels": [
        {
            "id": 1,
            "values": {
                "temp": 62,
                "sensor_val": 7
            },
            "ui_status": 0
        },
        {
            "id": 2,
            "values": {
                "temp": 61,
                "sensor_val": 5
            },
            "ui_status": 0
        },
        {
            "id": 3,
            "values": {
                "temp": 64,
                "sensor_val": 6
            },
            "ui_status": 0
        },
        {
            "id": 4,
            "values": {
                "temp": 64,
                "sensor_val": 7
            },
            "ui_status": 0
        },
        {
            "id": 5,
            "values": {
                "temp": 61,
                "sensor_val": 6
            },
            "ui_status": 0
        },
        {
            "id": 6,
            "values": {
                "temp": 60,
                "sensor_val": 6
            },
            "ui_status": 0
        },
        {
            "id": 7,
            "values": {
                "temp": 63,
                "sensor_val": 5
            },
            "ui_status": 0
        },
        {
            "id": 8,
            "values": {
                "temp": 64,
                "sensor_val": 5
            },
            "ui_status": 0
        },
        {
            "id": 9,
            "values": {
                "temp": 64,
                "sensor_val": 6
            },
            "ui_status": 0
        },
        {
            "id": 10,
            "values": {
                "temp": 62,
                "sensor_val": 7
            },
            "ui_status": 0
        },
        {
            "id": 11,
            "values": {
                "temp": 65,
                "sensor_val": 5
            },
            "ui_status": 0
        },
        {
            "id": 12,
            "values": {
                "temp": 63,
                "sensor_val": 7
            },
            "ui_status": 0
        }
    ],
    "mac": "60:bd:91:00:18:01",
    "pressure_in": 2.3,
    "pressure_out": 1.6,
    "ui_machine_mac": "60:bd:91:00:0e:a1",
    "ui_manifold_mac": "60:bd:91:00:18:01"
}


