import { Component} from '@angular/core';
import { Subscription, first } from 'rxjs';
import { round } from '../../utils/calculate';
import { CommonModule } from '@angular/common';
import { CurrentData } from '../../models/channel.model';
import { MouldService } from '../../services/mould.service';
import { MachineService } from '../../services/machine.service';
import { ChannelCardComponent } from './channel-card/channel-card.component';
import { TranslatePipe } from '../../translate';

@Component({
  selector: 'app-controls',
  standalone: true,
  imports: [ChannelCardComponent, CommonModule, TranslatePipe],
  templateUrl: './controls.component.html',
  styleUrl: './controls.component.scss'
})
export class ControlsComponent {
  skeletonArray = [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12]
  channel: CurrentData; 
  mouldCofigSchema: any = {}
  public pressure = {
    inletPressure : 0,
    deltaPressure : 0,
    filterPressure: 0
  }
  public dataInMould: string = 'processing'  // To manage data in overview screen processing/data/noData
  public inletTemps : number[];
  private machineDataSubscription!: Subscription;
  public selectedMould: String
  constructor(private readonly machineService: MachineService, private readonly mouldService: MouldService) {
    this.anySelecetdMould()
    this.addDataInChannels()
    this.updateSchemaForControls()
    this.machineService.globalShare.subscribe((data)=>{
      if(data['filterPressure']){
        this.pressure.filterPressure = data['filterPressure']
      }
      if(data['dataApi']){
        this.dataInMould = data['dataApi'] == true? 'data': 'noData' 
      }
    })
  }

  // get dataInMould(): boolean{
  //   return this.machineService.dataInMould
  // }

  /**
   * Retrieves the selected mould from local storage and fetches its settings.
   * Ensures safe data handling and prevents memory leaks.
   */
  anySelecetdMould(){
    const storedMould = localStorage.getItem('selectedMould');
    this.selectedMould = storedMould ? JSON.parse(storedMould) : null;

    if (this.selectedMould) {
      this.mouldService.fetchMouldSettings(this.selectedMould).subscribe(configurations => {
        if (configurations) {
          this.mouldCofigSchema = { ...configurations, channel_no: configurations?.channels?.length ?? 0 };
        }
      });
    }
  }

 

  /**
   *  Add data in channels from the service and API
   */
  private transformedDataSubscription: Subscription
  public addDataInChannels(){
    /**
     * Checks if `pressure_in` is already available in `mouldService`.
     * If not, fetches the inlet pressure from `readPressure()`, rounds it, and stores it.
     */
    // console.log(this.mouldService.pressure_in)
    if (this.mouldService.pressure_in) {
      this.pressure.inletPressure = round(this.mouldService.pressure_in);
    } else {
      this.mouldService.readPressure().pipe(first()).subscribe({
        next: (response) => {
          if (response?.pressure !== undefined) {
            this.pressure.inletPressure = round(response.pressure);
            this.mouldService.pressure_in = response.pressure;
          }
        },
        error: (err) => console.error(err),
      });
    }

    /**
     * Subscribes to `transformedData` from `machineService`.
     * If data exists, assigns it to `this.channel` and updates `delta_pressure`.
     */
    this.transformedDataSubscription = this.machineService.transformedData.subscribe((channel) => {
      if (!channel) return;
      this.channel = channel;
      this.pressure.inletPressure = round(channel?.pressure_in);
      this.pressure.deltaPressure = channel?.delta_pressure;
      this.inletTemps = channel?.inlet_temps
      // console.log('channel',channel)

      /**
       * If `mouldCofigSchema` is empty, initialize it with default channel data.
       */
      if (!this.mouldCofigSchema || Object.keys(this.mouldCofigSchema).length === 0) {
        const channelCount = channel?.channels?.length ?? 0;
        this.mouldCofigSchema = {
          channel_no: channelCount,
          channels: Array.from({ length: channelCount }, (_, index) => ({
            channel_diameter: 'circle',
            channel_id: index + 1,
            channel_name: `Channel ${index + 1}`,
            channel_lower_limit: this.flowDefaultLimit(index + 1, 'min'),
            channel_upper_limit: this.flowDefaultLimit(index + 1, 'max'),
            channel_enabled: 0,
          })),
        };
      }
    });
  }
  
  public get manifold() {
    return this.mouldCofigSchema
  }

  public trackConfig(index: number, config: any) {
    return config ? this.manifold?.mac && config.id : null;
  }

  // public isHidenCard(config: ManifoldChannel, index: number): boolean {
  // public isHidenCard(config: ManifoldChannel, index: number): boolean {
  //   const isPressure = config.sensors.some(s => s.type === 'pressure');
  //   const isOutSide = this.manifold?.channel_no ? index + 1 > this.manifold?.channel_no : false;
  //   return isPressure || isOutSide;
  // }

  /**
   * Unsubscribe from the machine data subscription
   */
  ngOnDestroy(): void {
    if (this.machineDataSubscription) {
      this.machineDataSubscription.unsubscribe();
    }
    this.transformedDataSubscription.unsubscribe()
  }

  updateSchemaForControls(){
    // dummyManifold.channel_no = 

  }

  flowDefaultLimit(channel: number, boundry: string): number{
    if(channel < 3){
      return boundry == 'min'? 1:20
    }
    else if(channel < 11){
      return boundry == 'min'? 2:40
    }
    else{
      return boundry == 'min'? 20:400
    }
  }

  /**
   * To check that test is in running state or not
   */
  public get testStatus(): boolean {
    return this.mouldService.mouldTestStatus?.state.includes('idle') ? false : true
  }

}
