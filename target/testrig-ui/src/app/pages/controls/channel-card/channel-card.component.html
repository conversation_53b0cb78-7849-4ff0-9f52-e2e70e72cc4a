<div class="channel-card">
  <div class="channel-card-header size-2 position-relative" >
    <div class="header-border" [ngClass]="'b-status-' + status">
      
        <!-- <span class="badge blink badge-pill badge-danger position-absolute m-1" 
          style="right: 10px; background: red;"
          [hidden]="!overflow['alert-FOL-'+channel.id]"
        >{{"Overflow"|translate}}</span> -->
    
      </div>
    <!-- <div class="header-border b-status-3" ></div> -->
    <div class="number hover_effect" title="Go to setting" (click)="redirectToSettings('temp')">
      <!-- <span class="font-weight-bold">{{ config.id < 10 ? '0' + config.id : config.id }}</span> -->
      {{ config.channel_name }}<span class="float-end">{{channelNo}}</span>
    </div>
  </div>
  <div class="channel-card-body">
    <div>
      <div class="d-flex flex-column">
        <span class="label">{{"Reynolds"|translate}}</span> 
        <span class="value"> {{ reynolds || ' - ' }} </span>
        <img  src="assets/images/air.svg" width="22px">
        <div *ngIf="reynolds > 4000">
          <i class="fas value fa-wind"></i>
        </div>
        <div *ngIf="reynolds < 4000">
          <i class="fas value fa-water"></i>
        </div>
      </div>
      <!-- <div class="mt-28 d-flex flex-column">
        <span class="label"><i class="far fa-triangle"></i>T</span>
        <span class="value"> {{ deltaT }} {{ getTempUnit() }} </span>
      </div> -->
      <!-- <div class="mt-28 d-flex flex-column">
        <span><img src="assets/images/energysaved.svg" alt="energy saved" width="23px" height="25px" title="Energy saved"/></span>
        <div class="value">{{energyQ}} {{getEnergySavedUnit()}}</div>
      </div>
      <div class="mt-28 d-flex flex-column">
        <span><img src="assets/images/reshotsave.svg" alt="energy saved" width="23px" height="25px" title="Total energy saved"/></span>
        <div class="value">{{energyQ}} {{getEnergySavedUnit()}}</div>
      </div> -->
    </div> 
    <div class="d-flex ml-auto" style="column-gap: 15px;">
      <app-chart-verical title="Go to Flow setting" (click)="redirectToSettings('flow')"
      [channel]="channel"
      [config]="config"
      [type]="'flow'"
    ></app-chart-verical>
    <app-chart-verical title="Go to Temp setting" (click)="redirectToSettings('temp')"
      [channel]="channel"
      [config]="config"
      [type]="'temperature'"
    ></app-chart-verical>
    </div>
  </div>
</div>