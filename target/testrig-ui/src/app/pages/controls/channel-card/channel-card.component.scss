@import '../../../../styles/variables';


.label {
  color: #000;
  font-size: 14px;
  font-weight: 400;
  letter-spacing: 1px;
  margin-bottom: 12px;
  display: inline-flex;
  align-items: center;
  i {
    font-size: 10px;
  }
}

.value {
  color: #002d63;
  font-size: 14px;
  line-height: 16px;
  font-weight: bold;
}

.mt-28 {
  margin-top: 28px;
}



.channel-card-header.size-2 .header-border.b-status-1 + .number , .channel-card-header.size-2 .header-border.b-status-0 + .number  , .channel-card-header.size-2 .header-border.b-status-2 + .number,
.channel-card-header.size-2 .header-border.b-status--1 + .number  {
  position: absolute;
  top: 0;
}
.channel-card-header.size-2 .header-border.b-status-1 + .number , .channel-card-header.size-2 .header-border.b-status-0 + .number  , .channel-card-header.size-2 .header-border.b-status-3 + .number,
.channel-card-header.size-2 .header-border.b-status--1 + .number  {
  position: absolute;
  top: 0;
}