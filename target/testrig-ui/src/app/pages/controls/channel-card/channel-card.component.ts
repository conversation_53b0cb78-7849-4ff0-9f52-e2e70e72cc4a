import { Component, Input, OnChanges, OnDestroy, OnInit, SimpleChanges } from '@angular/core';
import { insertData } from './dummyData';
import { Channel } from '../../../models/channel.model';
import { ActivatedRoute, Router } from '@angular/router';
import { float } from '@zxing/library/esm/customTypings';
import { ChannelEnergy, ChannelsEnergyData, ManifoldChannel } from '../../../models/machine.model';
import { calcDeltaT, calcReynolds, round } from '../../../utils/calculate';
import { Status } from '../../../utils/enums';
import { getImperialTempValue } from '../../../utils/convert';
import { getStatus } from '../../../utils/status';
import { CommonModule } from '@angular/common';
import { ChartVericalComponent } from '../chart-verical/chart-verical.component';
import { MachineService } from '../../../services/machine.service';
import { TranslatePipe } from '../../../translate';

@Component({
  selector: 'app-channel-card',
  templateUrl: './channel-card.component.html',
  standalone: true,
  imports: [CommonModule, ChartVericalComponent, TranslatePipe],
  styleUrls: ['./channel-card.component.scss']
})
export class ChannelCardComponent implements OnInit, OnChanges, OnDestroy{
  @Input()
  config: ManifoldChannel;
  @Input()
  channel: Channel;
  @Input()
  inletTemp: number;
  @Input()
  channelNo: number;

  overflow: any = {}
  public SelectedUnit = 'Metric';
  public manifold: any
  public mainMachine: any
  public flowValue: float;
  constructor(private router: Router, private activatedRoute:ActivatedRoute, private machineService: MachineService){
  }
  
  ngOnInit() {
    this.loadLocalStorage();
    this.machineService.globalShare.subscribe((data)=>{
      const key = 'alert-FOL-' + this.channel.id
      if(data[key] || data[key] == false){
        this.overflow[key] = data[key]
      }
    })
  }

  ngOnChanges(changes: SimpleChanges): void {
    let flowVal = this.flowValue = this.channel?.values.sensor_val
    
    // this method will called for each channel in manifold
    if (this.channel) {
      const channelData: ChannelEnergy = {
        "id": this.channel.id,
        "flowValue": this.channel.values.sensor_val,
        "deltaT": this.deltaT,
        "totalEnergy": 0
      };
      this.insertData(channelData);
    }
  }

  ngOnDestroy(): void {
  }
  
  public totalEnergyOfChannel: number = 0;
  public insertData(channelData: ChannelEnergy) {
    // this.machineService.energyService.pipe(take(1)).subscribe((data) => {
      let data = insertData
      let updatedData: ChannelsEnergyData;
      if (data) {
        const existingIndex = data.channels.findIndex((item) => item.id === channelData.id);
        if (existingIndex !== -1) {
          data.channels[existingIndex] = channelData;
        } else {
          data.channels.push(channelData);
        }
        updatedData = { ...data };
      } else {
        updatedData = { channels: [channelData] };
      }
      // this.machineService.energyService.next(updatedData);
    // });
    this.machineService.globalShare.subscribe((data)=>{
      if(data['FOL-channel']){
        this.overflow = data['FOL-channel']
      }
    })
  }
  

  public get deltaT(): number {
    if (this.SelectedUnit === 'Metric') {
      return round(calcDeltaT(this.channel?.values?.temp, this.inletTemp));
    } else {
      return round(calcDeltaT(getImperialTempValue(this.channel?.values?.temp), getImperialTempValue(this.inletTemp))); 
    }
  }

  public getTempUnit() {
    if(this.SelectedUnit === 'Metric') {
      return "°C";
    } else {
    return "°F"; 
    }
  }

  public get reynolds(): number {
    const diameter:number = this.config.channel_diameter.split(',')[1] == undefined
      ? 0
      : this.config.channel_diameter.split(',')[1]
        return calcReynolds(
          this.channel?.values?.sensor_val,
          diameter,
          this.channel?.values?.temp
      );
  }

  public get status(): Status {
    return getStatus(this.config, this.channel);
  }

  public loadLocalStorage() {
    if (localStorage.length > 0) {
      const unit = localStorage.getItem('SelectedUnit')
      if(unit) {
        this.SelectedUnit = JSON.parse(unit);
      }
    }
  }

  // Redirect to settings "Temperature" and "Flow".  
  public redirectToSettings(val : string){
    this.activatedRoute.params.subscribe(data =>{
      this.mainMachine = data['machineMac']
      this.manifold = data['manifoldMac']
      this.router.navigate([`/settings/${this.mainMachine}/${this.manifold}`])
    })
  }

}
