import configs from '../../utils/configs';

export const tabs = [
  {
    label: 'Temperature',
    type: 'temperature',
    configs: [
      {
        label: 'No.',
        key: 'title',
        type: 'title'
      },
      {
        label: 'Status',
        key: 'status',
        type: 'status'
      },
      {
        label: 'Name',
        key: 'name',
        inputType: 'input',
        type: 'input',
        maxlength: 100,
        level: 'channel'
      },
      {
        label: 'Low',
        key: 'min',
        type: 'input',
        inputType: 'number',
        unit: '&#8451;',
        width: 80,
        maxlength: 4
      },
      {
        label: 'Current',
        key: 'value',
        unit: '&#8451;',
        type: 'text'
      },
      {
        label: 'High',
        key: 'max',
        type: 'input',
        inputType: 'number',
        unit: '&#8451;',
        width: 80,
        maxlength: 4
      },
      {
        label: 'Diameter',
        key: 'diameter',
        type: 'input',
        inputType: 'number',
        unit: 'mm',
        width: 80,
        maxlength: 4,
        level: 'channel'
      },
      {
        label: 'Checkbox',
        key: 'limits_enabled',
        type: 'checkbox'
      }
    ]
  },
  {
    label: 'Flow',
    type: 'flow',
    configs: [
      {
        label: 'No.',
        key: 'title',
        type: 'title'
      },
      {
        label: 'Status',
        key: 'status',
        type: 'status'
      },
      {
        label: 'Name',
        key: 'name',
        type: 'input',
        maxlength: 100,
        level: 'channel'
      },
      {
        label: 'Low',
        key: 'min',
        type: 'input',
        inputType: 'number',
        unit: 'l/min',
        width: 80,
        maxlength: 4
      },
      {
        label: 'Current',
        key: 'value',
        unit: 'l/min',
        type: 'text'
      },
      {
        label: 'High',
        key: 'max',
        type: 'input',
        inputType: 'number',
        unit: 'l/min',
        width: 80,
        maxlength: 4
      },
      {
        label: 'Diameter',
        key: 'diameter',
        type: 'input',
        inputType: 'number',
        unit: 'mm',
        width: 80,
        maxlength: 4,
        level: 'channel'
      },
      // {
      //   label: 'Reynolds',
      //   key: 'reynolds',
      //   type: 'text'
      // },
      {
        label: 'Checkbox',
        key: 'limits_enabled',
        type: 'checkbox'
      }
    ]
  },
  {
    label: 'General',
    type: 'general',
    configs: [
      {
        label: 'Status',
        key: 'status',
        type: 'status'
      },
      {
        label: 'Name',
        key: 'title',
        type: 'title'
      },
      {
        label: 'Low',
        key: 'min',
        type: 'input',
        inputType: 'number',
        unit: 'Bar',
        width: 80,
        maxlength: 4,
        level: 'manifold'
      },
      {
        label: 'Current',
        key: 'value',
        unit: 'Bar',
        type: 'text',
        level: 'manifold'
      },
      {
        label: 'High',
        key: 'max',
        type: 'input',
        inputType: 'number',
        unit: 'Bar',
        width: 80,
        maxlength: 4,
        level: 'manifold'
      },
      {
        label: 'Checkbox',
        key: 'limits_enabled',
        type: 'checkbox',
        level: 'manifold'
      }
    ]
  }
];

export const numChannels = [2, 4, 8, 12];

export const limitsFlow =
[{id: 1, name: 'Custom'}, {id: 2, name: '-/+ 1 litre'}, {id: 3, name: '-/+ 2 litres'}, {id: 4, name: '-/+ 5 litres'}];

export const limitsTemp =
[{id: 1, name: 'Custom'}, {id: 2, name: '-/+ 2 °C'}, {id: 3, name: '-/+ 5 °C'}, {id: 4, name: '-/+ 10 °C'}];

export const sensorTypes = [
  // { value: null, label: '' },  Commented to  remove the blank space from dropdown
  ...configs.flows.map((item, index) => ({
    value: index,
    label: `${item.min}-${item.max} l/min`
  }))
];
