import { CommonModule } from '@angular/common';
import { Component } from '@angular/core';
import { FormsModule } from '@angular/forms';
import { Router } from '@angular/router';
import { MouldService } from '../../services/mould.service';
import { Subscription, catchError, interval, of, switchMap } from 'rxjs';
import { MachineService } from '../../services/machine.service';
import { TranslatePipe } from '../../translate';
import Notiflix from 'notiflix';
@Component({
  selector: 'app-settings',
  standalone: true,
  imports: [CommonModule, FormsModule, TranslatePipe],
  templateUrl: './config-settings.component.html',
  styleUrl: './config-settings.component.scss'
})
export class ConfigSettingsComponent {
  emergencyButtonPressed:boolean = false
  UiUpdated: boolean = false
  waterLevel: number = 0;
  waterTankStatus: any = {
    tanklabel: 'No Water',
    tanklabelClass: 'no-water',
    pumpStatus: false
  }
  upperSensor: boolean = false;
  lowerSensor: boolean = false;
  fillRunning:boolean = false
  emptyRunning:boolean = false
  private waterFlowSubscription!: Subscription;

  constructor( private router:Router, private mouldService: MouldService, private machineService: MachineService){  
    const superUser = this.machineService.getUserRole()
    if(superUser != true){
      this.router.navigate([`/mould`]);    
    }
    this.getWaterFlowStatus()
    this.getRecycleStatus()
    // this.waterTankAnimation('fill')
  }

  getRecycleStatus(){
    
    this.mouldService.getRecycleStatus().subscribe({
      next: (data) => {
        if(data){
          console.log('✌️data --->', data);
          if(data.state.includes('water-fill') && data.state.includes('completed')){
            this.fillRunning = false;
          } else if(data.state.includes('water-fill') && data.state.includes('running')){
            this.fillRunning = true;
          } 

          if(data.state.includes('water-empty') && data.state.includes('completed')){
            this.emptyRunning = false
          }else if(data.state.includes('water-empty') && data.state.includes('running')){
            this.emptyRunning = true;
          }

          if(data.state.includes('idle')){
            this.fillRunning= false
            this.emptyRunning= false
          }
          // this.fill = data.fill
          // this.empty = data.empty
          // if(this.fill)
          //   this.waterTankAnimation('fill')
          // if(this.empty)
          //   this.waterTankAnimation('empty')
          setTimeout(() => {
            if(!data.state.includes('completed') && !data.state.includes('idle')){
              this.getRecycleStatus()
            }
          }, 3000);
        }
      },
      error: (err) => {
        Notiflix.Notify.failure('Error getting recycle status');
        console.error(err);
      },
    });
    
  }

  /**
   * Function is used to get the status of the water flow for the sensors upper/lower
   * if water tank is full the active upper scensor 
   * if water tang is empty then active lower sensor
   */
  getWaterFlowStatus(){
    // Extract the last three values from the inputs string
    this.waterFlowSubscription = interval(1000) // Emit every 1 second
    .pipe(
      switchMap(() => this.mouldService.waterFlowStatus().pipe(
        catchError((error) => {
          console.error("API Error:", error);
          return of(null); // Return a fallback value so polling continues
        })
      ))
    )
    .subscribe((data) => {
      if (data) {
        let inputsArray = data.inputs.split(" "); // Convert string to an array
        let lastThree = inputsArray.slice(-3).join(""); // Get last 3 values as a string
        
        switch (lastThree.slice(0, -1)) {
        case "10":
          // if(!this.lowerSensor)
          //   this.emptyFillWater('4', false, 'fill')
          this.lowerSensor = false;
          this.upperSensor = false;
          this.waterLevel = 0
          break;
        case "01":
          // if(!this.upperSensor)
          //   this.emptyFillWater('5', false, 'empty')
          this.lowerSensor = true;
          this.upperSensor = true;
          this.waterLevel = 100
          break;
        case "11":
          // if(this.upperSensor)
          //   this.emptyFillWater('5', false, 'empty')
          this.lowerSensor = true;
          this.upperSensor = false;
          this.waterLevel = 50
          break;
        default:
          this.lowerSensor = false;
          this.upperSensor = false;
          this.waterLevel = 0
          break;
        }
        this.getWaterTankStatus(); // Set Tank label and Pump status
      }
    });
  }

  redirectSelectedCard(card: String){
    return card=='pump'? this.router.navigate(['/settings/pump-config']):this.router.navigate(['/settings/setting-config'])
  } 

  /**
   * Function is used to fill or empty the water tank by updating relay main values
   * @param relay_number 
   * @param sensor 
   * @param buttonType 
   */
  emptyFillWater(event: string){
    // const status = sensor? 'ON' : 'OFF';
    this.mouldService.waterTankFillEmpty(event).subscribe({
      next: (data) => {
        this.getRecycleStatus();
      },
      error: (err) => {
        Notiflix.Notify.failure('Error updating water tank status');
        console.error(err);
      },
    })
    // this.mouldService.relaysStatus(relay_number, status, 'main').subscribe(result =>{
    //   if((buttonType == 'empty') && (sensor)){
    //     this.waterTankAnimation('empty')
    //     this.upperSensor = false
    //   }
    //   if((buttonType == 'fill')&& (sensor)){
    //     this.waterTankAnimation('fill')
    //     this.lowerSensor = false
    //   }
    // }, err => {
    //   console.error(err);
    // });
  }    

  /**
   * Function is used to get the status of the water tank & pump status
   */
  getWaterTankStatus(){
    if (!this.lowerSensor) {
      // Case 1: Lower sensor-> Red, Tank Label-> No Water, Pump Status-> Not Ready
      this.machineService.waterTankStatus.tanklabel = 'No Water';
      this.machineService.waterTankStatus.tanklabelClass = 'no-water'
      this.machineService.waterTankStatus.pumpStatus = false;
    } else if (this.lowerSensor && !this.upperSensor) {
      // Case 2: Lower sensor-> Green, Tank label-> Water OK, Pump status-> Ready
      this.machineService.waterTankStatus.tanklabel = 'Water OK';
      this.machineService.waterTankStatus.tanklabelClass = 'water-ok'
      this.machineService.waterTankStatus.pumpStatus = true;
    } else if (this.upperSensor) {
      // Case 3: Upper sensor-> Green, Tank label-> Water Full, Pump Status-> Ready
      this.machineService.waterTankStatus.tanklabel = 'Tank Full';
      this.machineService.waterTankStatus.tanklabelClass = 'water-full'
      this.machineService.waterTankStatus.pumpStatus = true;
    }
    this.waterTankStatus = this.machineService.waterTankStatus;
  }


  waterTankAnimation(animationType:string){
    if(animationType == 'fill'){
      setTimeout(() => {
        if(this.waterLevel > 90)
          this.waterLevel = this.lowerSensor == true? 40: 0
        this.waterLevel = (this.waterLevel + 10) % 110;
        if(this.fillRunning)
          this.waterTankAnimation('fill')
        else
          this.waterLevel = 100
      }, 500);
    }
    if(animationType == 'empty'){
      setTimeout(() => {
        if(this.waterLevel < 10)
          this.waterLevel = 110
        this.waterLevel = (this.waterLevel - 10) % 110;
        if(this.emptyRunning)
          this.waterTankAnimation('empty')
        else
          this.waterLevel = 0
      }, 500);
    }
  }

  
  ngOnDestroy(): void {
    if (this.waterFlowSubscription) {
      this.waterFlowSubscription.unsubscribe();
    }
  }
}
