@import '../../../styles/variables';


.divider {
    border-left: 2px solid #ddd;
    height: 24px;
    margin: 0 15px;
}

.status-value {
    font-weight: bold;
}

.icon-container img {
    width: 40px;
    height: 40px;
    margin-right: 10px;
}

.system-status {
    display: flex;
    background: #fff;
    flex-wrap: wrap;
    border-radius: 8px;
    padding: 20px;
    align-items: center;
    margin-bottom: 20px;

    h4 
    {
        margin: 0;
        font-size: 20px;
        color: $header-blue;
        padding-right: 20px;
        font-weight: 400;
        border-right: 1px solid $border-color;
        margin-right: 20px;
        padding-top: 4px;
        padding-bottom: 4px;
    }
    ul 
    {
        padding: 0;
        list-style-type: none;
        display: inline-flex;
        gap: 85px;
        align-items: center;
        margin: 0;
        li 
        {
            font-size: 16px;
            color: #4E4E4E;
            strong 
            {
                font-weight: 500;
                color: #000;
            }
        }
    }
}

.water-tank {
    width: 200px;
    height: 92px;
    border: 3px solid #2196F3;
    border-radius: 10px;
    position: relative;
    overflow: hidden;
}
.water-level {
    position: absolute;
    bottom: 0;
    width: 100%;
    background-color: #63b2f173;
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 24px;
    font-weight: bold;
}

.water-sensors{
    right: 10px;
    position: absolute;
    &.upper{
        top: 10px;
    }
    &.lower{
        bottom: 10px;
    }

}
.btn-empty-fill .btn{
    height: 40px;
}

.tank-style {
    font-size: 1.2rem;
    font-weight: bold;
    padding: 4px 0px;
    border-radius: 5px;
    display: inline-block;
}
.no-water {
    padding: 5px;
    color: #d9534f; /* Bootstrap danger color (red) */
}
.water-ok {
    padding: 5px;
    color: #ffc107; /* Bootstrap danger color (red) */
}
.water-full {
    padding: 5px;
    color: $green; /* Bootstrap danger color (red) */
}

