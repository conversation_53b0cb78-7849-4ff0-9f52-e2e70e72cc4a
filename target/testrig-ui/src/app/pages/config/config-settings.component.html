<section class="app-wrapper">
    <!-- SYSTEM STATUS -->
    
    <div class="system-status justify-content-between" >
        <div class="d-flex">
            <h4>{{ "System Status" | translate }} :</h4>

            <ul>
                <li>{{"MouldFlo Interface"|translate}} : <strong>{{"OK"|translate}}</strong></li>
                <li>{{"Firmware Rev"|translate}} : <strong>1152 Trunk</strong></li>
                <li>{{"Pump Status"|translate}} : 
                    <strong [ngClass]="waterTankStatus.pumpStatus? ' text-success': ' text-danger'">
                        <span *ngIf="waterTankStatus.pumpStatus">{{"Ready"|translate}}</span>
                        <span *ngIf="!waterTankStatus.pumpStatus">{{"Not Ready"|translate}}</span>
                    </strong>
                </li>
                <li> {{"Water filtration"|translate}} : <strong class="text-success">
                    <span *ngIf="waterTankStatus.pumpStatus"> {{ "In operation" | translate}} </span>
                    <span *ngIf="!waterTankStatus.pumpStatus">{{ "Operational" | translate}}</span>
                </strong></li>
            </ul>

        </div>
    </div>

    <div class="row" >
        <div class="col-md-12">
            <div class="bg-white rounded-8 p20 water_reserve">
                <div class="d-flex justify-content-between gap-2 ">
                    <div class="">
                        <h4 class="">{{"Water Reservoir Control"|translate}} </h4>
                        <label for="tankInput" class="tank-style"> {{"Tank"|translate}} : <span [ngClass]="waterTankStatus.tanklabelClass">{{ waterTankStatus.tanklabel | translate   }}</span></label>
                    </div>
                    <div class="d-flex gap-2">
                        <div class="d-flex flex-column btn-empty-fill">
                            
                            <button  type="button" class="btn btn-outline-success m-1" [disabled]="upperSensor" (click)="(fillRunning=!fillRunning); emptyFillWater('waterfillstart')" *ngIf="!fillRunning" >{{"Fill"|translate}}</button>
                            <button  type="button" class="btn btn-danger-outline m-1" (click)="(fillRunning=!fillRunning); emptyFillWater('stop')" *ngIf="fillRunning" >{{"Fill Stop"|translate}}</button>
                            
                            <button type="button" class="btn btn-danger-outline m-1" [disabled]="!lowerSensor" (click)="emptyRunning = !emptyRunning; emptyFillWater('wateremptystart')" *ngIf="!emptyRunning">{{"Empty"|translate}}</button>
                            <button type="button" class="btn btn-danger-outline m-1"  (click)="emptyRunning = !emptyRunning; emptyFillWater('stop')" *ngIf="emptyRunning">{{"Empty Stop"|translate}}</button>
                        </div>
                       
                        <div class="position-relative">
                            <div class="water-tank">
                                <div class="">
                                    <div class="water-level" [style.height.%]="waterLevel"></div>
                                </div>
                            </div>
                            <span class="gap-1 d-flex align-items-center water-sensors upper">
                                {{"Upper Sensor"|translate}} <div class="water-status" [ngClass]="upperSensor? ' active': ' '"></div>
                            </span>
                            <span class="gap-1 d-flex align-items-center water-sensors lower">
                                {{"Lower Sensor"|translate}}  <div class="water-status" [ngClass]="lowerSensor? ' active': ' '"></div>
                            </span>
    
                        </div>
                    </div>
                </div>
                <!-- <div class="inputbox d-flex align-items-center"> -->
                    
                    <!-- <div class="d-flex flex-fill align-items-center gap-2">
                        <label for="tankInput" > Tank:</label>
                        <input type="text" class="form-control" readonly id="tankInput" aria-describedby="emailHelp" [value]="waterTankStatus.tanklabel">
                    </div> -->
                    <!-- <button type="button" class="btn btn-danger-outline" [disabled]="!lowerSensor" (click)="empty = !empty; emptyFillWater('5', upperSensor, 'empty')" *ngIf="!empty">Empty</button>
                    <button type="button" class="btn btn-danger-outline" [disabled]="!lowerSensor" (click)="empty = !empty; emptyFillWater('5', upperSensor, 'empty')" *ngIf="empty">Empty Stop</button>

                    <button  type="button" class="btn btn-outline-success" [disabled]="!upperSensor" (click)="(fill=!fill); emptyFillWater('4', lowerSensor, 'fill')" *ngIf="!fill" >Fill</button>
                    <button  type="button" class="btn btn-danger-outline" [disabled]="!upperSensor" (click)="(fill=!fill); emptyFillWater('4', lowerSensor, 'fill')" *ngIf="fill" >Fill Stop</button> -->
                <!-- </div> -->
            </div> 
        </div>
        <!-- <div class="col-md-6">
            <div class="bg-white h-100 rounded-8 p20 water_reserve">
                <h4>Test Rig Flow Reference: </h4>
                <div class="inputbox d-flex align-items-center ">
                    <div class="d-flex flex-fill align-items-center gap-2">
                        <label for="tankInput" > Tank:</label>
                        <input type="text" readonly class="form-control" id="lastDateInput">
                    </div>
                    <button type="button" class="btn btn-primary" >Take Flow Ref</button>
                </div>
            </div>
        </div> -->
    </div>

    <div class="row">
        <div class="col-md-6">
            <div class="bg-white rounded-8 p20 p2p" (click)="redirectSelectedCard('pump')">
                <div class="iconbox">
                    <img src="assets/images/pump1.svg" alt="pump"/>   
                </div>
                <h3>{{"Pump and Valves"|translate}}</h3>

                <button class="arrow-icon">
                    <img src="assets/images/arrow_right_alt.svg" alt="arrow right"/>   
                </button>
            </div>
        </div>
        <div class="col-md-6" >
            <div class="bg-white rounded-8 p20 p2p" (click)="redirectSelectedCard('configuration')">
                <div class="iconbox">
                    <img src="assets/images/setting.svg" alt="setting"/>   
                </div>
                <h3>{{"Settings Configurations"|translate}}</h3>

                <button class="arrow-icon">
                    <img src="assets/images/arrow_right_alt.svg" alt="arrow right"/>   
                </button>
            </div>
        </div>
    </div>

</section>