@import '../../../../styles/variables';

.status-section {
    border: 1px solid #ddd;
    border-radius: 8px;
    padding: 20px;
    background-color: #f9f9f9;
}

.button-group button {
    width: 100%;
    margin-bottom: 10px;
}

.btn-status {
    border-radius: 15px;
}

.valve-container {
    display: flex;
    min-width: 90px;
    justify-content: center;
    align-items: center;
    // flex-direction: column;
    align-items: center;
    // margin-top: 50px;
}

.valve-toggle {
    display: none;
}

.valve-button {
    position: relative;
    width: 33px;
    height: 33px;
    border-radius: 50%;
    background-color: $btn-red; /* red for OFF state */
    cursor: pointer;
    transition: background-color 0.3s ease;
}

.valve-button::before {
    content: "";
    position: absolute;
    top: 50%;
    left: 50%;
    width: 3.5px;
    height: 14px;
    background-color: white;
    transform: translate(-50%, -50%) rotate(0deg);
    transform-origin: center;
    transition: transform 0.3s ease;
}
  
.valve-toggle:checked + .valve-button {
    background-color: $green; /* green for ON state */
}
  
.valve-toggle:checked + .valve-button::before {
    transform: translate(-50%, -50%) rotate(90deg);
}
  
.status {
    width: 100px;
    font-family: Arial, sans-serif;
    color: gray;
}

.status::after {
    content: "DÉSACTIVÉ";
}
  
.valve-toggle:checked ~ .status::after {
    content: "SUR";
}


.status-open-close {
    width: 100px;
    font-family: Arial, sans-serif;
    color: gray;
}
.status-open-close::after {
    content: "FERMER";
}
  
.valve-toggle:checked ~ .status-open-close::after {
    content: "OUVRIR";
}

.status-start-stop {
    width: 100px;
    font-family: Arial, sans-serif;
    color: gray;
}

.status-start-stop::after {
    content: "ARRÊT";
}
  
.valve-toggle:checked ~ .status-start-stop::after {
    content: "COMMENCER";
}






.number-input {
    display: flex;
    align-items: center;
    gap: 10px;
}

.button-group button { 
    margin: 0;
    border: none;
    background: $button-blue;
    border-radius: 4%;
    color: #fff;
    width: 90px;
    height: 48px;
}

.border-box-1{
    // border: 1px solid $border-color;
    border-radius: 6px;
    padding: 2px 0px;
}

.border-box-1 .form-control{
    padding: 10px;
    max-width: 100%;
    outline: none !important;
    box-shadow: none;
}

.border-box-1 .form-control::-webkit-outer-spin-button,
.border-box-1 .form-control::-webkit-inner-spin-button {
  -webkit-appearance: none;
  margin: 0;
}

/* Firefox */
.border-box-1 .form-control[type=number] {
  -moz-appearance: textfield;
}


.rotate-valve{
    position: relative;
    width: 33px;
    height: 33px;
    border-radius: 50%;
    cursor: pointer;
    &.clock-rotaion{
        animation: rotateClockwise 1.5s linear forwards;
    };
    &.anti-closk-rotation{
        animation: rotateAnticlockwise 1.5s linear forwards;
    }
}
@keyframes rotateClockwise {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(180deg); }
}

@keyframes rotateAnticlockwise {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(-180deg); }  
}

.seeting-right  button{
    background: transparent;
    border: 0px solid;
}

.seeting-right .active{
    border: 2px solid $green;
    border-radius: 4px;
}