<section class="app-wrapper">
    <div class="bg-white rounded-8">
            <div class="card-heading d-flex align-items-center gap20">
                <span class="cursor-pointer backArrow" (click)="backToMould()"> 
                    <img src="assets/images/arrow_right_alt1.svg" alt="arrow right"/>   
                </span>
                <h3 class="mainHeading">{{"Pump and Valves"|translate}}</h3>
            </div>
    </div>
    <div class="bg-white row m-0">
        <div class="col-md-6 borderRight">
            <div class="outerBox overflow-auto">
            <h4>{{"Pump Status"|translate}} :</h4>
            <div class="pump-input">
                <label for="inletPressure">{{"Inlet Pressure"|translate}}</label>
                <input type="number" step="0.1" class="form-control" id="exampleInputEmail1" placeholder="Inlet Pressure" readonly [value]="inletPressure || inletPressure == 0? inletPressure: null">
            </div>
            <div class="pump-input">
                <label for="outletPressure">{{"Outlet Pressure"|translate}}</label>
                <input type="text" readonly class="form-control" id="outletPressure" placeholder="Outlet Pressure"
                [value]="outletPressure">
            </div>

            <div class="pump-input">
                <label for="pressure-set-point">{{"Pressure Set Points"|translate}}</label>
                <div class="d-flex align-items-center w-100 gap-2 border-box-1" style="max-width: 50%;">
                    <div class="button-group" >
                        <button (click)="startIncreasing('decrease')"><h3 class="m-0">-</h3></button>
                    </div>
                    <span class="d-block f-16 fw-bolder text-center w-100" >{{pressureSetPoint }}</span>
                    <!-- <input *ngIf="pumpStatus !== 'Stop'" type="number" class="form-control w-100" id="pressure-set-point" [(ngModel)]="pressureSetPoint" 
                    step="0.1" min="0" max="10"
                    placeholder="Pressure Set Point"> -->
                    <div class="button-group d-flex align-items-center" >
                        <button (click)="startIncreasing('increase')">
                            <h3 class="m-0">+</h3>
                        </button>
                    </div>
                </div>
            </div>
            <div class="pump-input mt-5">
                <label class="text-muted">{{"Pump"|translate}} :
                    <span *ngIf="status.pumpStatus == 'Start'"><img src="assets/images/pump-gray.svg" alt="Pump Icon"></span> 
                    <span *ngIf="status.pumpStatus == 'Stop'"><img src="assets/images/pump-green.svg" alt="Pump Icon"></span> 
                </label> 
                <button class="btn btn-primary" id="pumpStopButton" [disabled]="status.pumpStatus == 'Processing'" (click)="pumpStartStop(status.pumpStatus)" > {{ status.pumpStatus |translate}}</button>
            </div>
        </div>
    </div>

        <!-- valve Status -->
        <div class="col-md-6">
            <div class="outerBox overflow-auto" >
                <h4>{{"Valve Status"|translate}} :</h4>
                <!-- Relay Main -->
                <div style="display: flex; align-items: center;">
                    <h6 style="margin-right: 10px; margin-bottom: 0.2rem;">{{"Main"|translate}}</h6>
                    <hr style="flex-grow: 1; border: -1px solid #000;">
                </div>
                <div class="pump-input">
                    <label for="water_fill">{{"Water Fill"|translate}} :</label> 
                    <div class="valve-container gap-2">
                        <input type="checkbox" id="water_fill" class="valve-toggle" 
                        (change)="getRelayStatus('4', $event, relayType.MAIN)"
                        [checked]="relayMailStatus.water_fill">
                        <label for="water_fill" class="valve-button"></label>
                        <div class="status"></div>
                    </div>
                </div>
                
                <div class="pump-input">
                    <label for="water_purge">{{"Water Purge"|translate}} :</label> 
                    <div class="valve-container gap-2">
                        <input type="checkbox" id="water_purge" class="valve-toggle" 
                        (change)="getRelayStatus('5', $event, relayType.MAIN)"
                        [checked]="relayMailStatus.water_purge">
                        <label for="water_purge" class="valve-button"></label>
                        <div class="status"></div>
                    </div>
                </div>
                <div class="pump-input">
                    <label for="compressed_air_fill_inlet">{{"Compressed Air High Pressure"|translate}} :</label> 
                    <div class="valve-container gap-2">
                        <input type="checkbox" id="compressed_air_fill_inlet" 
                        class="valve-toggle" (change)="getRelayStatus('7', $event, relayType.MAIN)"
                        [checked]="relayMailStatus.compressed_air_fill_inlet">
                        <label for="compressed_air_fill_inlet" class="valve-button"></label>
                        <div class="status"></div>
                    </div>
                </div>
                <div class="pump-input">
                    <label for="compressed_air_fill_outlet">{{"Compressed Air Regulated"|translate}} :</label> 
                    <div class="valve-container gap-2">
                        <input type="checkbox" id="compressed_air_fill_outlet" class="valve-toggle" 
                        (change)="getRelayStatus('6', $event, relayType.MAIN)"
                        [checked]="relayMailStatus.compressed_air_fill_outlet">
                        <label for="compressed_air_fill_outlet" class="valve-button"></label>
                        <div class="status"></div>
                    </div>
                </div>
                <!-- <div class="pump-input">
                    <label for="compressed-air-high-pressure">Compressed Air High Pressure:</label> 
                    <div class="valve-container gap-2">
                        <input type="checkbox" id="compressed-air-high-pressure" class="valve-toggle" (change)="getRelayStatus('1', $event, relayType.MAIN)">
                        <label for="compressed-air-high-pressure" class="valve-button"></label>
                        <div class="status"></div>
                    </div>
                </div> -->


                <!-- Relay Addon -->
                <div style="display: flex; align-items: center;">
                    <h6 style="margin-right: 10px; margin-bottom: 0.2rem;">{{"Addon"|translate}}</h6>
                    <hr style="flex-grow: 1; border: -1px solid #000;">
                </div>
                
                
                <div class="pump-input">
                    <label for="main-water-valve-power">{{"Main Water Valve"|translate}} :</label>
                    <div class="valve-container gap-2">
                        <input type="checkbox" id="main-water-valve-power" class="valve-toggle" [checked]="relayAddonStatus.r1" (change)="getRelayStatus('r1', $event, relayType.ADDON)">
                        <label for="main-water-valve-power" class="valve-button"></label>
                        <div class="status"></div>
                    </div>
                </div>
                <div class="pump-input">
                    
                    <label for="main-water-valve">{{"Main Water Valve Position"|translate}} :</label>
                    <div class="valve-container gap-2">
                        <input type="checkbox" id="main-water-valve" [checked]="!relayAddonStatus.r2" class="valve-toggle" (change)="rotateValveOpenClose('r2', $event, relayType.ADDON)">
                        <label for="main-water-valve" >
                            <img for="main-water-valve" [src]="!relayAddonStatus.r2 ? 'assets/images/green-valve.svg': 'assets/images/red-valve.svg'" [ngClass]="(!relayAddonStatus.r2? 'clock-rotaion': 'anti-closk-rotation')" class="rotate-valve" alt="arrow right"/>
                        </label>
                        <div for="main-water-valve" class="status-open-close"></div>
                    </div>
                </div>
                <div class="pump-input">
                    <label for="airvalveInlet-1" for="TCU-valve">{{"Filter Valve"|translate}} :</label>
                    <div class="valve-container gap-2">
                        <input type="checkbox" id="TCU-valve" class="valve-toggle" [checked]="relayAddonStatus.r4" (change)="getRelayStatus('r4', $event, relayType.ADDON)">
                        <label for="TCU-valve" class="valve-button"></label>
                        <div class="status-open-close"></div>
                    </div>
                </div>
                <div class="pump-input">
                    <label for="airvalveOutlet" for="empty">{{"TCU Valve"|translate}} :</label> 
                    <div class="valve-container gap-2">
                        <input type="checkbox" id="empty" class="valve-toggle" [checked]="relayAddonStatus.r3" (change)="getRelayStatus('r3', $event, relayType.ADDON)">
                        <label for="empty" class="valve-button"></label>
                        <div class="status-open-close"></div>
                    </div>
                </div>
                <div class="pump-input">
                    <label for="tcp-on-off">{{"TCU"|translate}} :</label> 
                    <div class="valve-container gap-2">
                        <input type="checkbox" id="tcp-on-off" class="valve-toggle" (change)="getRelayStatus('1', $event, relayType.MAIN)">
                        <label for="tcp-on-off" class="valve-button"></label>
                        <div class="status-start-stop"></div>
                    </div>
                </div>
                
                <div class="pump-input">
                    <label for="airvalveOutlet-1" for="inlet-air-purge-valve">{{"Inlet Air Purge Valve"|translate}} :</label> 
                    <div class="valve-container gap-2">
                        <input type="checkbox" id="inlet-air-purge-valve" class="valve-toggle" [checked]="relayAddonStatus.r5" (change)="getRelayStatus('r5', $event, relayType.ADDON)">
                        <label for="inlet-air-purge-valve" class="valve-button"></label>
                        <div class="status-open-close"></div>
                    </div>
                </div>
                <div class="pump-input">
                    <label for="airvalveInlet-2" for="inlet-water-purge-valve">{{"Inlet Water Purge Valve"|translate}} :</label>  
                    <div class="valve-container gap-2">
                        <input type="checkbox" id="inlet-water-purge-valve" class="valve-toggle"  [checked]="relayAddonStatus.r6"(change)="getRelayStatus('r6', $event, relayType.ADDON)">
                        <label for="inlet-water-purge-valve" class="valve-button"></label>
                        <div class="status-open-close"></div>
                    </div>
                </div>
                <div class="pump-input">
                    <label for="airvalveInlet-2" for="outlet-air-purge">{{"Outlet Air Purge Valve"|translate}} :</label>  
                    <div class="valve-container gap-2">
                        <input type="checkbox" id="outlet-air-purge" class="valve-toggle" [checked]="relayAddonStatus.r7" (change)="getRelayStatus('r7', $event, relayType.ADDON)">
                        <label for="outlet-air-purge" class="valve-button"></label>
                        <div class="status-open-close"></div>
                    </div>
                </div>
                <div class="pump-input">
                    <label for="airvalveInlet-2" for="outlet-water-valve">{{"Outlet Water Valve"|translate}} :</label>  
                    <div class="valve-container gap-2">
                        <input type="checkbox" id="outlet-water-valve" class="valve-toggle" [checked]="relayAddonStatus.r8" (change)="getRelayStatus('r8', $event, relayType.ADDON)">
                        <label for="outlet-water-valve" class="valve-button"></label>
                        <div class="status-open-close"></div>
                    </div>
                </div>
            </div>
        
        </div>  
    </div>
</section>
