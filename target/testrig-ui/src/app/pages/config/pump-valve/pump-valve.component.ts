import Notiflix from 'notiflix';
import { Subscription } from 'rxjs';
import { Router } from '@angular/router';
import { Component } from '@angular/core';
import { FormsModule } from '@angular/forms';
import { CommonModule } from '@angular/common';
import { TranslatePipe, TranslateService } from '../../../translate';
import { AuthService } from '../../../services/auth.service';
import { MouldService } from '../../../services/mould.service';
import { MachineService } from '../../../services/machine.service';

enum relayType {
  MAIN = 'main',
  ADDON = 'addon',
}

@Component({
  selector: 'app-pump-valve',
  standalone: true,
  imports: [FormsModule, CommonModule, TranslatePipe],
  templateUrl: './pump-valve.component.html',
  styleUrl: './pump-valve.component.scss'
})
export class PumpValveComponent {
 
  rotateState: string = '' // to show green or red valve based on valve opend or closed
  relayAddonCount: number = 8
  pumpStatusSubscription!: Subscription;   // To subscribe and unsubscribe API during component load and leave
  inletPressure: number = 0;
  outletPressure:number = 0;
  pressureSetPoint: number = 2
  private intervalId: any;
  public relayAddonStatus: any = {}
  relayType = relayType
  status:any = {
    pumpStatus: 'Start'
  }
  
  constructor(private router: Router, private mouldService: MouldService, private machineService: MachineService,
    private authService: AuthService, private translate: TranslateService) {
    const superUser = this.machineService.getUserRole()
    if(superUser != true){
      this.router.navigate([`/mould`]);    
    }
    const pressureSetPoint = localStorage.getItem('pressure-set-point');
   
    if(pressureSetPoint){
      this.pressureSetPoint = JSON.parse(pressureSetPoint)
      
    }
    // Read pressure settings of pump
    this.intervalId = setInterval(() => {
      this.mouldService.outletPressure().subscribe({
        next: (response) => {
          if(response.data_14_0){
            // console.log('response.data_14_0',parseFloat(response.data_14_0) < 0.1)
            if(parseFloat(response.data_14_0) == undefined){
              response.data_14_0 = null

            }
            // console.log('response.data_14_0',response.data_14_0)
            this.outletPressure = parseFloat(response.data_14_0) < 0.1 ? 0 : response.data_14_0.toFixed(1)
          }
        },
        error: (err) => {
          console.error(err);
        }
      });
      this.mouldService.readPressure().subscribe({
        next: (response) => {
          if(response.pressure || response.pressure == 0){
            this.inletPressure = this.mouldService.presusureRoundOf(response.pressure)
            this.mouldService.pressure_in = response.pressure
          }
        },
        error: (err) => {
          console.error(err);
        }
      });
    }, 1500);
    this.relayAddOnStatus()
    this.verifyPumpStatus()
    this.verifyPumpButtonStatusAfterEmergencyRelese()
  }
  
  

  /**
   * Set the status of relay addon based on the status API 
   * on refresh or component load 
   */
  relayAddOnStatus(){
    for (let count = 1; count <= this.relayAddonCount; count++) {
      this.relayAddonStatus[`r${count}`] = false; // Set true for '1', false for '0'
    }
    this.rotateState = this.relayAddonStatus.r2? 'ON': 'OFF'
    // Relay Addon status
    this.mouldService.relayAddonStatus().subscribe( (relayStatus)=>{
      // let relayStatus = {"response" : "10"}
      if(relayStatus.response){
        for (let count = 0; count < relayStatus.response.length; count++) {
          this.relayAddonStatus[`r${count + 1}`] = parseInt(relayStatus.response[(relayStatus.response.length-1) - count]); // Set true for '1', false for '0'
          this.rotateState = this.relayAddonStatus.r2? 'ON': 'OFF'
        }
        // this.relayAddonStatus[`r2`] = 0
      }
    }, (err)=>{
    } )
  }

  backToMould(){
    this.router.navigate(['/settings'])
  }

  

  /**
   * Function is used to set the status of the relay to ON or OFF
   * @param relay_number  The relay number to be set
   * @param event  The event object from the checkbox input to verify the button is on or off
   */
  getRelayStatus(relay_number: string, event: Event, type: 'main' | 'addon'){
    const relayCheckbox = event.target as HTMLInputElement;
    const previousState = relayCheckbox.checked;
    const isChecked = (event.target as HTMLInputElement).checked;
    const state = isChecked ? 'ON' : 'OFF';   
    if(type == 'addon'){
      this.relayAddonStatus[relay_number] = isChecked
    }
    relayCheckbox.checked = previousState;
    this.mouldService.relaysStatus(relay_number, state, type).subscribe({
      next: (response) => {
        // if(type == 'addon'){
        //   this.relayAddonStatus[relay_number] = isChecked
        // }
        // relayCheckbox.checked = previousState;
      },
      error: (err) => {
        this.relayAddonStatus[relay_number] = !previousState
        relayCheckbox.checked = !previousState;
        Notiflix.Notify.failure(this.translate.instant('Failed to set relay status'));
      }
    });
  }

  public get relayMailStatus(){
    return this.machineService.relayMainStatus
  }

  /**
   * Function is used to start or stop the pump
   * @param status The status of the pump to be set
   */
  pumpStartStop(status: string){
    const newState = status === 'Start' ? 'Stop' : 'Start';
    if((status === 'Start') && (this.pressureSetPoint === undefined)){
      Notiflix.Notify.failure('Please set the pressure set point');
      return
    } else if (status === 'Start' && this.pressureSetPoint > 10) {
      Notiflix.Notify.failure('Pressure set point cannot be more than 10');
      return
    }
    else{
      this.status.pumpStatus = 'Processing'
      if(status === 'Start'){
        this.mouldService.startPump(this.pressureSetPoint).subscribe({
          next: (response) => {
            localStorage.setItem('pressure-set-point', JSON.stringify(this.pressureSetPoint))
            this.status.pumpStatus = newState;
          },
          error: (err) => {
            this.status.pumpStatus = status;
            const errorMessage = err?.error?.message || err?.detail || "An unknown error occurred.";
            Notiflix.Notify.failure(errorMessage);
          }
        });
      }
      else if(status === 'Stop'){
        this.mouldService.stopPump().subscribe({
          next: (response) => {
            this.status.pumpStatus = newState;
          },
          error: (err) => {
            this.status.pumpStatus = status;
            const errorMessage = err?.error?.message || err?.detail || "An unknown error occurred.";
            Notiflix.Notify.failure(errorMessage);
          }
        });
      }
    }
  }

  /**
   * Change pump pressure on chnage or pressure set point input using up-down arrow 
   */
  onPressureChange(){
    if(this.status.pumpStatus != 'Start'){ // Check if the pump is not already running
      this.pressureSetPoint

      // Call the service to start the pump with the new pressure set point
      this.mouldService.incrementDecrementPump(this.pressureSetPoint).subscribe({
        next: (result) => {
          localStorage.setItem('pressure-set-point', JSON.stringify(this.pressureSetPoint))
        },
        error: (err) => {
          console.log('✌️err --->', err);
        }
      });
    }
  }

  /**
   * Verify pump status 'start' | 'stop'
   */
  verifyPumpStatus(){
    this.pumpStatusSubscription = this.mouldService.pumpStatus().subscribe( (status)=>{
      this.status.pumpStatus = status.status == 1? 'Stop': 'Start'
      // if(this.pumpStatus == 'Stop'){
      //   this.getRunningPumpPressure()
      // }
    })
  }

  verifyPumpButtonStatusAfterEmergencyRelese(){
    if(this.machineService.emergencyButtonPressed){
      this.mouldService.pumpStatus().subscribe( (status)=>{
        this.status.pumpStatus = 'Stop'
      })
    }else{
      setTimeout(() => {
        this.verifyPumpButtonStatusAfterEmergencyRelese()
      }, 1000);
    }
  }

  getRunningPumpPressure(){
    this.mouldService.pumpPressure().subscribe( (pressure)=>{
      this.pressureSetPoint = +pressure.pump_pressure // Converting to number
    })
  }

  ngOnDestroy(): void {
    if (this.intervalId) {
      clearInterval(this.intervalId);
    }
    // if (this.pumpStatusSubscription) {
    //   this.pumpStatusSubscription.unsubscribe();
    // }
  }

  /**
   * Bind the DOWN arrow with number input filed to decrease pressure set point using DOWN arrow.
   */
  decreasePressure() {
    if (this.pressureSetPoint > 0) {  // Ensure the pressure set point does not go below the minimum limit
      this.pressureSetPoint = Number((this.pressureSetPoint - 0.1).toFixed(1));
      
      // Trigger the pressure change event
      this.onPressureChange();  
    }
  }

  rotateValveOpenClose(relay_number: string, event: Event, type: 'main' | 'addon'){
    const relayCheckbox = event.target as HTMLInputElement;
    const previousState = relayCheckbox.checked;
    const isChecked = (event.target as HTMLInputElement).checked;
    const state = !isChecked ? 'ON' : 'OFF';
    // const currentVal = this.relayAddonStatus[relay_number]
    // if(relay_number == 'r2'){
    //   this.relayAddonStatus.r2 = !this.relayAddonStatus.r2
    // }
    if(type == 'addon'){
      this.relayAddonStatus[relay_number] = !!isChecked
      // this.relayAddonStatus[relay_number] =  currentVal? 0:1
      this.relayAddonStatus[relay_number] =  this.relayAddonStatus[relay_number]? 0:1
      this.rotateState = state
    }
    relayCheckbox.checked = previousState;
    this.mouldService.relaysStatus(relay_number, state, type).subscribe({
      next: (response) => {
        // if(type == 'addon'){
        //   this.relayAddonStatus[relay_number] = !!isChecked
        //   // this.relayAddonStatus[relay_number] =  currentVal? 0:1
        //   this.relayAddonStatus[relay_number] =  this.relayAddonStatus[relay_number]? 0:1
        //   this.rotateState = state
        // }
        // relayCheckbox.checked = previousState;
      },
      error: (err) => {
        relayCheckbox.checked = !previousState;
        // setTimeout(()=>{
        //   relayCheckbox.checked = !previousState;
        //   this.relayAddonStatus[relay_number] = !currentVal? 0:1
        // }, 1000)
        Notiflix.Notify.failure(this.translate.instant('Failed to set relay status'));
      }
    });
  }

  interval: any;
  startIncreasing(action: 'increase' | 'decrease') {
    // if(this.pumpStatus != 'Start'){
      // this.interval = setInterval(() => {
        // this.pressureSetPoint++;
        if(action == 'increase'){
          if (this.pressureSetPoint < 10) { 
            this.pressureSetPoint = Number((this.pressureSetPoint + 0.1).toFixed(1));
            this.onPressureChange()
          }
        }
        if(action == 'decrease'){
          if (this.pressureSetPoint > 0) {
            this.pressureSetPoint = Number((this.pressureSetPoint - 0.1).toFixed(1));
            this.onPressureChange()
          }
        }
      // }, 100); // Adjust the speed as needed (100ms = 0.1 second)
    // }else{
    //   Notiflix.Notify.failure('Please start the pump before changing the pressure set point');
    // }
  }

}
