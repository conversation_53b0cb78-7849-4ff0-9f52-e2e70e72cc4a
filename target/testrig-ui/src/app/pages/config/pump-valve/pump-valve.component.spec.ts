import { ComponentFixture, TestBed } from '@angular/core/testing';

import { PumpValveComponent } from './pump-valve.component';

describe('PumpValveComponent', () => {
  let component: PumpValveComponent;
  let fixture: ComponentFixture<PumpValveComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [PumpValveComponent]
    })
    .compileComponents();

    fixture = TestBed.createComponent(PumpValveComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
