<section class="app-wrapper"> 
    <div class="bg-white rounded-8">
            <div class="card-heading d-flex align-items-center gap20">
                <span class="cursor-pointer backArrow"  (click)="backToMould()"> 
                    <img src="assets/images/arrow_right_alt1.svg" alt="arrow right"/>   
                </span>
                <h3 class="mainHeading">{{"Configurations"|translate}}</h3>
                <button class="btn btn-primary" [disabled]="buttonState == 'Processing...'" (click)="saveConfiguration()">{{ buttonState |translate }}</button>
            </div>
    </div>

    
    <div class="bg-white row m-0">
        <div class="col-md-6 borderRight">
            <div class="outerBox overflow-auto"> 
            <div class="pump-input">
                <label for="inletPressure">{{"Language"|translate}} : </label>
                <select class="form-select" aria-label="Default select example" (change)="updateConfiguration('language', $event)">
                    <option value='selectlanguage' selected> {{"Select Language"|translate}}</option>
                    <option [value]='language' [selected]="configuration.user_language == language" *ngFor="let language of configuration.language">{{ language }}</option>
                </select>
            </div>
            <div class="pump-input" hidden>
                <label for="tcuTemperature">{{"TCU temperature"|translate}} : </label>
                <select class="form-select" aria-label="Default select example" >
                    <option value="1">5-100l/min</option>
                </select>
            </div>

            <div class="pump-input" hidden>
                <label for="tcuTimeOut">{{"TCU Timeout"|translate}} : </label>
                <select class="form-select" aria-label="Default select example">
                    <option value="1">12</option>
                </select>
            </div>
            <div class="pump-input" hidden>
                <label for="password">{{"Password"|translate}} : </label>
                <input type="password" class="form-control" disabled   id="password" readonly placeholder="Password" value="Mouldflo100%">                
            </div>

            <div class="pump-input">
                <label for="location" class="text-muted">{{"Location"|translate}} : </label>
                <input type="text" class="form-control" id="location" 
                    placeholder="Location" (blur)="updateConfiguration('location', $event)" 
                    [value]='configuration.location? configuration.location: "Matissart"'>
            </div>
            <div class="pump-input">
                <label for="upload-logo">{{"Upload Logo"|translate}} : </label>
                <input type="file" class="form-control" id="upload-logo" (change)="onFileSelected($event)" accept=".jpg" >
            </div>
            <!-- <div class="pump-input justify-content-end">
                <button class="btn btn-primary" [disabled]="buttonState == 'Processing...'" (click)="saveConfiguration()">{{ buttonState |translate }}</button>
            </div> -->

            <div class="logo-image">
                <div class="align-items-center justify-content-center d-flex m-5 opacity-75">
                    <div *ngIf="logoLoading" class="loader-logo"> </div>
                    <span *ngIf="!logoLoading && !downloadedLogo">{{"No Logo Found!" | translate}}</span>
                </div>
                <img *ngIf="downloadedLogo && !logoLoading" [src]="downloadedLogo" alt="Downloaded Logo" width="200" />
            </div>
        </div>
    </div>

        <!-- valve Status -->
        <div class="col-md-6">
            <div class="outerBox overflow-auto"> 
                <div class="pump-input">
                    <label for="max-water-pressure">{{"Max Water Pressure"|translate}} : </label> 
                    <div class="d-flex align-items-center w-50 max-w-100">
                        <input type="number" placeholder="Max Water Pressure" 
                            (blur)="updateConfiguration('max_water_pressure', $event)"
                            [value]='configuration.max_water_pressure? configuration.max_water_pressure : 4'
                            min="0" max="9" class="form-control flex-fill" 
                            id="max-water-pressure"> 
                            <span class="me-3">bars</span> 
                    </div>
                </div>
                
                <div class="pump-input">
                    <label for="flow-stabilization-timeout">{{"Flow Stabilization Timeout"|translate}} : </label> 
                    <div class="d-flex align-items-center w-50 max-w-100">
                        <input type="number" placeholder="Flow Stabilization Timeout" 
                            class="form-control flex-fill" id="flow-stabilization-timeout" 
                            (blur)="updateConfiguration('flow_stabilization_timeout', $event)"
                            [value]='configuration.flow_stabilization_timeout? configuration.flow_stabilization_timeout : null'> 
                            <span class="me-3">{{"seconds"|translate}}</span> 

                    </div>
                </div>

                <div class="pump-input">
                    <label for="flow-stabilization-limit">{{"Flow Stabilization Limit"|translate}} : </label>
                    <input type="text" class="form-control" placeholder="Flow Stabilization Limit" 
                        id="flow-stabilization-limit" 
                        (blur)="updateConfiguration('flow_stabilization_limit', $event)"
                        [value]='configuration.flow_stabilization_limit? configuration.flow_stabilization_limit : null'> 
                </div>

                <div class="pump-input">
                    <label for="pressure-stabilization-timeout">{{"Pressure Stabilization Timeout"|translate}} : </label>
                    <div class="d-flex align-items-center w-50 max-w-100">
                        <input type="text" placeholder="Pressure Stabilization Timeout" 
                            class="form-control flex-fill" id="pressure-stabilization-timeout" 
                            (blur)="updateConfiguration('pressure_stabilization_timeout', $event)"
                            [value]='configuration.pressure_stabilization_timeout? configuration.pressure_stabilization_timeout: null'> 
                            <span class="me-3">{{"seconds"|translate}}</span> 

                    </div>
                </div>

                <div class="pump-input">
                    <label for="maximum-pressure-loss">{{"Maximum Pressure Loss"|translate}} : </label> 
                    <div class="d-flex align-items-center w-50 max-w-100 ">
                        <input type="text" placeholder="Maximum Pressure Loss" 
                            class="form-control flex-fill" id="maximum-pressure-loss" 
                            (blur)="updateConfiguration('max_pressure_loss', $event)"
                            [value]='configuration.max_pressure_loss? configuration.max_pressure_loss: null'> 
                            <span class="me-3">bar</span> 

                    </div>
                </div>

                <div class="pump-input">
                    <label for="pressure-measure-time">{{"Pressure Measure Time"|translate}} : </label>
                    <div class="d-flex align-items-center w-50 max-w-100 ">
                        <input type="text" placeholder="Pressure Measure Time" 
                            class="form-control flex-fill" id="pressure-measure-time" 
                            (blur)="updateConfiguration('pressure_measure_time', $event)"
                            [value]='configuration.pressure_measure_time? configuration.pressure_measure_time: null'> 
                            <span class="me-3">{{"seconds"|translate}}</span> 
                    </div>
                </div>
                
                <div class="pump-input">
                    <label for="tcu-temperature">{{"TCU temperature"|translate}} : </label>
                    <div class="d-flex align-items-center w-50 max-w-100 ">
                        <input type="text" placeholder="TCU temperature" 
                            class="form-control flex-fill" id="tcu-temperature" 
                            (blur)="updateConfiguration('tcu_temperature', $event)"
                            [value]='configuration.tcu_temperature? configuration.tcu_temperature: null'> 
                            <span class="me-3">{{"&deg;C"|translate}}</span> 
                    </div>
                </div>

                <!-- <div class="pump-input">
                    <label for="cooling-cutoff-temperature">{{"Cooling Cutoff Temperature"|translate}} : </label>
                    <div class="d-flex align-items-center w-50 max-w-100 ">
                        <input type="text" placeholder="Cooling Cutoff Temperature" 
                            class="form-control flex-fill" id="cooling-cutoff-temperature" 
                            (blur)="updateConfiguration('cooling_cutoff_temperature', $event)"
                            [value]='configuration.cooling_cutoff_temperature? configuration.cooling_cutoff_temperature: null'> 
                            <span class="me-3">{{"&deg;C"|translate}}</span> 
                    </div>
                </div> -->
            </div>
        
        </div>  
    </div> 
</section>
