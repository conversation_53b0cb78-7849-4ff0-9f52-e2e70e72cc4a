import { Component } from '@angular/core';
import { Router } from '@angular/router';
import { MouldService } from '../../../services/mould.service';
import { Configuration } from '../../../models/mould.model';
import { CommonModule } from '@angular/common';
import { MachineService } from '../../../services/machine.service';
import Notiflix from 'notiflix';
import { TranslatePipe, TranslateService } from '../../../translate';
import { DomSanitizer } from '@angular/platform-browser';

@Component({
  selector: 'app-mould-configuration',
  standalone: true,
  imports: [CommonModule, TranslatePipe],
  templateUrl: './mould-configuration.component.html',
  styleUrl: './mould-configuration.component.scss'
})
export class MouldConfigurationComponent {
  updateConfigValues: any = {}
  buttonState : string = 'Save'
  configuration: Configuration = {} as Configuration; // Configuration is imported from mould.model.ts to store configurations
  maxFileSize = 1 * 1024 * 1024; // 1MB
  selectedFile: File | null = null; // to store the logo

  constructor(
    private machineService: MachineService, 
    private router: Router, 
    private mouldService: MouldService, 
    private sanitizer: DomSanitizer,
    private translate: TranslateService){
    const superUser = this.machineService.getUserRole()
    if(!superUser == true){
      this.router.navigate([`/mould`]);    
    }
    this.getCofiguration()
  }

  /**
   * Function to get configuration from API
   */
  getCofiguration(){
    this.mouldService.getConfiguration().subscribe((data) => {
      if(data){
        this.configuration.flow_stabilization_timeout = data.flow_stabilization_timeout
        this.configuration.max_water_pressure = data.max_water_pressure
        this.configuration.flow_stabilization_limit = data.flow_stabilization_limit 
        this.configuration.max_pressure_loss = data.max_pressure_loss 
        this.configuration.pressure_stabilization_timeout = data.pressure_stabilization_timeout 
        this.configuration.pressure_measure_time = data.pressure_measure_time 
        this.configuration.tcu_temperature = data.tcu_temperature
        // this.configuration.cooling_cutoff_temperature = data.cooling_cutoff_temperature
        this.configuration.location = data.location? data.location : 'Matissart'
        this.configuration.user_language = data.user_language 
        this.configuration.language = data.language
      }
    }, (err) =>{
    })
    this.downloadLogo()
  }

  backToMould(){
    this.router.navigate(['/settings'])
  }

  /**
   * Function to update configuration based on key 
   * @param key   // Key to update configuration
   * @param event   // Event to get input value
   */
  updateConfiguration(key: string, event: Event){
    const inputElement = event.target as HTMLInputElement;  // Extract input element
    const value = (key !== 'language' && key !== 'location') 
    ? Number(inputElement.value) 
    : inputElement.value;
    this.updateConfigValues[key] = value
  }

  /**
   * Set the seleted file value in a variable afetr verifying some conditions 
   */
  onFileSelected(event: Event) {
    const fileInput = event.target as HTMLInputElement;
    if (fileInput.files && fileInput.files.length > 0) {
      const file = fileInput.files[0];
      const allowedTypes = ['image/jpeg'];

      if (!allowedTypes.includes(file.type)) {
        Notiflix.Notify.failure('Only JPG images are allowed!')
        return;
      }
      
      if (file.size > this.maxFileSize) {
        Notiflix.Notify.failure('File size exceeds 1MB! Please upload a smaller logo.')
        return;
      }

      const newFileName = 'logo-mould' + file.name.substring(file.name.lastIndexOf('.'));
      const renamedFile = new File([file], newFileName, { type: file.type });
      this.selectedFile = renamedFile;
    }
  }

  /**
   * Download the logo from the server with filename 
   */
  downloadedLogo:any
  logoLoading:boolean = false
  downloadLogo(){
    this.logoLoading = true
     
    this.mouldService.downloadUplodedLogo('logo-mould.jpg').subscribe({
      next: (response) => {
        const blob = new Blob([response], { type: 'image/jpg' });
        const logoURL = URL.createObjectURL(blob);
        if(this.downloadedLogo)
          URL.revokeObjectURL(this.downloadedLogo.changingThisBreaksApplicationSecurity);
        this.logoLoading = false
        this.downloadedLogo = this.sanitizer.bypassSecurityTrustResourceUrl(logoURL);
      },
      error: (err) => {
        this.logoLoading = false
        console.error('❌ Error in downloadUplodedLogo:', err);
      }
    })
  }

  /**
   * Update configuration on click on save button
   */
  saveConfiguration(){
    this.buttonState = 'Processing...'
    // setTimeout(() => {
      if(this.selectedFile){
        this.mouldService.uploadLogo(this.selectedFile).subscribe({
          next: (response) =>{
            this.downloadLogo()
          },
          error: (error)=>{
            console.log('✌️error --->', error);
          }
        })
      }
      this.mouldService.updateConfigurations(this.updateConfigValues).subscribe({
        next: (response) => {
          Notiflix.Notify.success(this.translate.instant('Settings updated successfully!'))
          this.buttonState = 'Save' 
        },
        error: (err) => {
          this.buttonState = 'Save' 
        }
      });
    // }, 2000);

  }

}
