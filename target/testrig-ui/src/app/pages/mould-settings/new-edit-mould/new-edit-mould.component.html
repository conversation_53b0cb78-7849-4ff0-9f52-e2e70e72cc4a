    <section class="mouldflow-setting mt-5">
        <div class="card-box flex-fill">
            <!-- Header -->
            <form [formGroup]="mouldForm" (ngSubmit)="onSubmit()">
                <div class="mb-5">
                    <div class="card-heading d-flex align-items-center gap20">
                        <a class="cursor-pointer backArrow" [href]="['/mould']">
                            <img src="assets/images/arrow_right_alt1.svg" alt="arrow right" />
                        </a>
                        <h3 class="mainHeading" *ngIf="!mould.edit">{{"Add New Excel"|translate}}</h3>
                        <h3 class="mainHeading" *ngIf="mould.edit">{{"Update Excel"|translate}}</h3>
                        <button class="btn btn-primary" type="submit" [disabled]="button == 'Processing...'"> {{ button |translate }}</button>
                    </div>
                </div>
                <!-- Body -->
                <div>
                    <div class="row">
                        <div class="col-lg-8">
                            <div class="row">
                                <div class="col-lg-6 mb-5">
                                    <div class="">
                                        <label>{{"Sector"|translate}}</label>
                                        <div class="d-flex gap-3">
                                            <div class="form-check form-check-inline w-50 m-0 ps-0">
                                                <input type="radio" class="btn-check" formControlName="sector" id="new-mould" autocomplete="off" value="new-mould">
                                                <label class="btn btn-outline-secondary" for="new-mould">
                                                    {{"New Mould"|translate}}
                                                </label>
                                            </div>
                                            <div class="form-check form-check-inline w-50 m-0 ps-0">
                                                <input type="radio" class="btn-check" formControlName="sector" id="maintenance" autocomplete="off" value="maintenance">
                                                <label class="btn btn-outline-secondary" for="maintenance">
                                                    {{"Maintenance"|translate}}
                                                </label>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-lg-6 mb-5">
                                    <label>{{"Name"|translate}}</label>
                                    <input type="text" class="form-control flex-fill" formControlName="name" [placeholder]="mould.placeholders.name"
                                    [ngClass]="{'is-invalid': mouldForm.get('name')?.invalid && mouldForm.get('name')?.touched}">
                                </div>
                                <div class="col-lg-6 mb-5">
                                    <div class="">
                                        <label>{{"Order Number"|translate}}</label>
                                        <div class="d-flex justify-content-center align-items-center">
                                            <input *ngIf="this.mouldForm.value.sector !== 'maintenance'" type="number" class=" form-control flex-fill"  
                                            formControlName="orderNumber" [placeholder]="mould.placeholders.orderNumber"
                                            [ngClass]="{
                                            'is-invalid': mouldForm.get('orderNumber')?.invalid && mouldForm.get('orderNumber')?.touched
                                            }">
                                            <div *ngIf="mouldForm.get('orderNumber')?.touched && mouldForm.get('orderNumber')?.invalid" class="invalid-feedback d-block">
                                            <span *ngIf="mouldForm.get('orderNumber')?.errors?.['required']">Order number is required.</span>
                                            <span *ngIf="mouldForm.get('orderNumber')?.errors?.['pattern']">Order number must be 5 to 7 digits.</span>
                                            </div>
                                            <input *ngIf="this.mouldForm.value.sector == 'maintenance'" type="text" class="form-control flex-fill" readonly 
                                            [placeholder]="mould.placeholders.maintenance" formControlName="orderNumber">
                                        </div>
                                    </div>
                                    
                                </div>
                                <div class="col-lg-6 mb-5">
                                    <div class="">
                                        <label>{{"Year"|translate}}</label>
                                        <mat-form-field appearance="fill" class="w-100">
                                            <input matInput [matDatepicker]="picker" formControlName="year" placeholder="Select date"
                                                (focus)="picker.open()" (click)="picker.open()"
                                                [ngClass]="{'is-invalid': mouldForm.get('year')?.touched && mouldForm.get('year')?.invalid}">
                                            <mat-datepicker-toggle matSuffix [for]="picker"></mat-datepicker-toggle>
                                            <mat-datepicker #picker></mat-datepicker>
                                            <div *ngIf="mouldForm.get('year')?.touched && mouldForm.get('year')?.invalid" class="invalid-feedback d-block">
                                                <span *ngIf="mouldForm.get('year')?.errors?.['required']">Year is required.</span>
                                            </div>
                                        </mat-form-field>
                                    </div>
                                </div>  
                                <div class="col-lg-6 mb-5">
                                    <label>{{"Client"|translate}}</label>
                                    <div class="position-relative">
                                        <div *ngIf="this.addClient" class="d-flex">
                                            <input type="text" class="form-control flex-fill me-3" formControlName="clientName" placeholder="Enter Client"
                                            [ngClass]="{'is-invalid': mouldForm.get('clientName')?.invalid && mouldForm.get('clientName')?.touched}">
                                            <button class="cross-icon" type="button" (click)="this.addClient = !this.addClient">
                                                <img src="assets/images/close_small.svg" alt="close" />
                                            </button>
                                        </div>
                                        <div *ngIf="!this.addClient">
                                            <button class="dropdown-toggle" type="button" id="dropdownProfile" data-bs-toggle="dropdown" aria-expanded="false" >
                                                <span *ngIf="mouldForm.value.client">
                                                    {{mouldForm.value.client}}
                                                </span>
                                                <span *ngIf="!mouldForm.value.client">
                                                    {{"Select Client"|translate }}
                                                </span>
                                            </button>
                                            <ul class="dropdown-menu cursor-pointer" aria-labelledby="dropdownProfile">
                                                <li *ngFor="let mould of this.mould['allMoulds']" (click)="setClientValue(mould.Client)">
                                                    {{ mould.Client }}
                                                </li>
                                                <li>
                                                    <button type="button" (click)="this.addClient = !this.addClient"> + {{"Add New"|translate}}</button>  
                                                </li>
                                            </ul>
                                        </div>
                                    </div> 
                                </div>
                                <div class="col-lg-6 mb-5">
                                    <div class="">
                                        <label>{{"Number Of Mould"|translate}}</label>
                                        <div class="d-flex justify-content-center align-items-center new-mould-range-btn">
                                            <div class="button-group ">
                                                <button class="range-btn-inner left-btn" type="button" (click)="incrementDecrement('decrement', this.mouldForm.value.numberMould, 'numberMould')">
                                                    <h3 class="m-0">-</h3>
                                                </button>   
                                            </div>
                                            <input type="number" class="mx-3 form-control flex-fill" formControlName="numberMould" placeholder=""
                                            [ngClass]="{'is-invalid': mouldForm.get('numberMould')?.touched && mouldForm.get('numberMould')?.invalid}">
                                            <div class="button-group">
                                                <button class="range-btn-inner right-btn" type="button" (click)="incrementDecrement('increment', this.mouldForm.value.numberMould, 'numberMould')">
                                                    <h3 class="m-0">+</h3>
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                    
                                    <!-- <select class="form-select w-100" formControlName="client" aria-label="select example">
                                        <option [value]="">
                                            channel_dropdown_name
                                        </option>
                                    </select> -->
                                </div>
                            </div>
                        </div>
                        <div class="col-lg-4">
                            <label>{{"Description"|translate}}</label>
                            <textarea class="form-control" formControlName="description" [placeholder]="mould.placeholders.description" id="floatingTextarea" style="height:80%"
                            [ngClass]="{
                                'is-invalid': mouldForm.get('description')?.invalid && mouldForm.get('description')?.touched
                              }"></textarea>
                        </div>
                    </div>
                    <div class="row mt-2">
                        <div class="col-lg-8">
                            <div class="col-lg-12 mb-5">
                                <div class="d-flex gap-3 justify-content-between mb-2 align-items-center">
                                    <label>{{"Number of Cavity/Mould:"|translate}}</label>
                                    <div class="cavity-value">
                                        <input class="form-control" type="" placeholder="" [value]="mouldForm.value.cavityValue" formControlName="cavityValue" />
                                    </div>
                                </div>
                                <div class="d-flex gap-3 custom-slider">
                                    <ngx-slider class="ngx-slider-setup" 
                                        formControlName="cavityValue"
                                        [(value)]="mouldForm.value.cavityValue" 
                                        [options]="cavityOptions">
                                    </ngx-slider>
                                </div>
                            </div>
                        </div>
                        <div class="col-lg-4">
                            <div class="col-lg-12">
                                <label>{{"Set Volume"|translate}} (ml)</label>
                                <input type="number" class="form-control flex-fill" placeholder="Enter Volume" formControlName="mouldVolume"
                                [ngClass]="{'is-invalid': mouldForm.get('mouldVolume')?.invalid && mouldForm.get('mouldVolume')?.touched}">
                            </div>
                        </div>
                    </div>
                </div>
            </form>
        </div>
    </section>