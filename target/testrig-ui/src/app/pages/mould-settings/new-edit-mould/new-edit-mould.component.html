    <section class="mouldflow-setting mt-5">
        <div class="card-box flex-fill">
            <!-- Header -->
            <form [formGroup]="mouldForm" (ngSubmit)="onSubmit()">
                <div class="mb-5">
                    <div class="card-heading d-flex align-items-center gap20">
                        <a class="cursor-pointer backArrow" [href]="['/mould']">
                            <img src="assets/images/arrow_right_alt1.svg" alt="arrow right" />
                        </a>
                        <h3 class="mainHeading" *ngIf="!mould.edit">{{"Add New Excel"|translate}}</h3>
                        <h3 class="mainHeading" *ngIf="mould.edit">{{"Update Excel"|translate}}</h3>
                        <button class="btn btn-primary" type="submit" [disabled]="button == 'Processing...'"> {{ button |translate }}</button>
                    </div>
                </div>
                <!-- Body -->
                <div>
                    <div class="row">
                        <div class="col-lg-8">
                            <div class="row">
                                <div class="col-lg-6 mb-5">
                                    <div class="">
                                        <label>{{"Sector"|translate}}</label>
                                        <div class="d-flex gap-3">
                                            <div class="form-check form-check-inline w-50 m-0 ps-0">
                                                <input type="radio" class="btn-check" formControlName="sector" id="new-mould" autocomplete="off" value="new-mould">
                                                <label class="btn btn-outline-secondary" for="new-mould">
                                                    {{"New Mould"|translate}}
                                                </label>
                                            </div>
                                            <div class="form-check form-check-inline w-50 m-0 ps-0">
                                                <input type="radio" class="btn-check" formControlName="sector" id="maintenance" autocomplete="off" value="maintenance">
                                                <label class="btn btn-outline-secondary" for="maintenance">
                                                    {{"Maintenance"|translate}}
                                                </label>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-lg-6 mb-5">
                                    <label>{{"Name"|translate}}</label>
                                    <input type="text" class="form-control flex-fill" formControlName="name" [placeholder]="mould.placeholders.name"
                                    [ngClass]="{'is-invalid': mouldForm.get('name')?.invalid && mouldForm.get('name')?.touched}">
                                </div>
                                <div class="col-lg-6 mb-5">
                                    <div class="">
                                        <label>{{"Order Number"|translate}}</label>
                                        <div class="d-flex justify-content-center align-items-center">
                                            <input *ngIf="this.mouldForm.value.sector !== 'maintenance'" type="number" class=" form-control flex-fill"
                                            formControlName="orderNumber" [placeholder]="mould.placeholders.orderNumber"
                                            [ngClass]="{
                                            'is-invalid': mouldForm.get('orderNumber')?.invalid && mouldForm.get('orderNumber')?.touched
                                            }">
                                            <div *ngIf="mouldForm.get('orderNumber')?.touched && mouldForm.get('orderNumber')?.invalid" class="invalid-feedback d-block">
                                            <span *ngIf="mouldForm.get('orderNumber')?.errors?.['required']">Order number is required.</span>
                                            <span *ngIf="mouldForm.get('orderNumber')?.errors?.['pattern']">Order number must be 5 to 7 digits.</span>
                                            </div>
                                            <input *ngIf="this.mouldForm.value.sector == 'maintenance'" type="text" class="form-control flex-fill" readonly
                                            [placeholder]="mould.placeholders.maintenance" formControlName="orderNumber">
                                        </div>
                                    </div>

                                </div>
                                <div class="col-lg-6 mb-5">
                                    <div class="position-relative">
                                        <label>{{"Year"|translate}}</label>
                                        <div class="custom-datepicker-wrapper">
                                            <input type="text" class="form-control" formControlName="year" placeholder="Select year"
                                                (click)="toggleDatePicker()" readonly
                                                [ngClass]="{'is-invalid': mouldForm.get('year')?.touched && mouldForm.get('year')?.invalid}">
                                            <div class="datepicker-icon" (click)="toggleDatePicker()">
                                                <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                    <path d="M19 3H18V1H16V3H8V1H6V3H5C3.89 3 3.01 3.9 3.01 5L3 19C3 20.1 3.89 21 5 21H19C20.1 21 21 20.1 21 19V5C21 3.9 20.1 3 19 3ZM19 19H5V8H19V19ZM7 10H12V15H7V10Z" fill="#666"/>
                                                </svg>
                                            </div>
                                            <!-- Custom Year Picker Dropdown -->
                                            <div *ngIf="showDatePicker" class="year-picker-dropdown">
                                                <div class="year-picker-header">
                                                    <span>{{"Select Year"|translate}}</span>
                                                    <button type="button" class="close-btn" (click)="closeDatePicker()">×</button>
                                                </div>
                                                <div class="year-picker-grid">
                                                    <div *ngFor="let year of getYearOptions()"
                                                         class="year-option"
                                                         [class.selected]="mouldForm.get('year')?.value == year"
                                                         (click)="selectYear(year)">
                                                        {{year}}
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <div *ngIf="mouldForm.get('year')?.touched && mouldForm.get('year')?.invalid" class="invalid-feedback d-block">
                                            <span *ngIf="mouldForm.get('year')?.errors?.['required']">Year is required.</span>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-lg-6 mb-5">
                                    <label>{{"Client"|translate}}</label>
                                    <div class="position-relative">
                                        <div *ngIf="this.addClient" class="d-flex">
                                            <input type="text" class="form-control flex-fill me-3" formControlName="clientName" placeholder="Enter Client"
                                            [ngClass]="{'is-invalid': mouldForm.get('clientName')?.invalid && mouldForm.get('clientName')?.touched}">
                                            <button class="cross-icon" type="button" (click)="this.addClient = !this.addClient">
                                                <img src="assets/images/close_small.svg" alt="close" />
                                            </button>
                                        </div>
                                        <div *ngIf="!this.addClient">
                                            <button class="dropdown-toggle" type="button" id="dropdownProfile" data-bs-toggle="dropdown" aria-expanded="false" >
                                                <span *ngIf="mouldForm.value.client">
                                                    {{mouldForm.value.client}}
                                                </span>
                                                <span *ngIf="!mouldForm.value.client">
                                                    {{"Select Client"|translate }}
                                                </span>
                                            </button>
                                            <ul class="dropdown-menu cursor-pointer" aria-labelledby="dropdownProfile">
                                                <li *ngFor="let mould of this.mould['allMoulds']" (click)="setClientValue(mould.Client)">
                                                    {{ mould.Client }}
                                                </li>
                                                <li>
                                                    <button type="button" (click)="this.addClient = !this.addClient"> + {{"Add New"|translate}}</button>
                                                </li>
                                            </ul>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-lg-6 mb-5">
                                    <div class="">
                                        <label>{{"Number Of Mould"|translate}} {{"Quantity"|translate}}</label>
                                        <div class="d-flex justify-content-center align-items-center new-mould-range-btn">
                                            <div class="button-group ">
                                                <button class="range-btn-inner left-btn" type="button" (click)="incrementDecrement('decrement', this.mouldForm.value.numberMould, 'numberMould')">
                                                    <h3 class="m-0">-</h3>
                                                </button>
                                            </div>
                                            <input type="number" class="mx-3 form-control flex-fill" formControlName="numberMould" placeholder=""
                                            [ngClass]="{'is-invalid': mouldForm.get('numberMould')?.touched && mouldForm.get('numberMould')?.invalid}">
                                            <div class="button-group">
                                                <button class="range-btn-inner right-btn" type="button" (click)="incrementDecrement('increment', this.mouldForm.value.numberMould, 'numberMould')">
                                                    <h3 class="m-0">+</h3>
                                                </button>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- <select class="form-select w-100" formControlName="client" aria-label="select example">
                                        <option [value]="">
                                            channel_dropdown_name
                                        </option>
                                    </select> -->
                                </div>
                            </div>
                        </div>
                        <div class="col-lg-4">
                            <label>{{"Description"|translate}}</label>
                            <textarea class="form-control" formControlName="description" [placeholder]="mould.placeholders.description" id="floatingTextarea" style="height:80%"
                            [ngClass]="{
                                'is-invalid': mouldForm.get('description')?.invalid && mouldForm.get('description')?.touched
                              }"></textarea>
                        </div>
                    </div>
                    <div class="row mt-2">
                        <div class="col-lg-8">
                            <div class="col-lg-12 mb-5">
                                <div class="d-flex gap-3 justify-content-between mb-2 align-items-center">
                                    <label>{{"Number of Cavity/Mould:"|translate}}</label>
                                    <div class="cavity-value">
                                        <input class="form-control" type="" placeholder="" [value]="mouldForm.value.cavityValue" formControlName="cavityValue" />
                                    </div>
                                </div>
                                <div class="d-flex gap-3 custom-slider">
                                    <ngx-slider class="ngx-slider-setup"
                                        formControlName="cavityValue"
                                        [(value)]="mouldForm.value.cavityValue"
                                        [options]="cavityOptions">
                                    </ngx-slider>
                                </div>
                            </div>
                        </div>
                        <div class="col-lg-4">
                            <div class="col-lg-12">
                                <label>{{"Set Volume"|translate}} (ml)</label>
                                <input type="number" class="form-control flex-fill" placeholder="Enter Volume" formControlName="mouldVolume"
                                [ngClass]="{'is-invalid': mouldForm.get('mouldVolume')?.invalid && mouldForm.get('mouldVolume')?.touched}">
                            </div>
                        </div>
                    </div>
                </div>
            </form>
        </div>
    </section>