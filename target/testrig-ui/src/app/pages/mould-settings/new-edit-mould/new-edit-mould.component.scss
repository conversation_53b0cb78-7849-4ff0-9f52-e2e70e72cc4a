@import '../../../../styles/variables';

.mouldflow-setting {
    height: calc(100vh - 125px);
    padding: 0px 30px 20px 30px;

}

.card-box 
{
    display: flex;
    flex-direction: column;
    box-shadow: 0px 12px 20px 0px #E0F3FD;
    padding: 18px;
    background-color: #fff;
    border-radius: 8px;
    .table 
    {
        margin: 0;
        th, td 
        {
            padding: 8px 10px;
            border-color: $border-color;
            vertical-align: middle;
        }
        th 
        {
            color: #4E4E4E;
            font-size: 16px;
            font-weight: 500;
            height: 45px;
            &:first-child 
            {
                text-align: center;
            }
        }
        td 
        {
            color: #000000;
            font-size: 13px;
            &:first-child 
            {
                text-align: center;
            }
            
        }
    }
    .form-select
    {
        border-color: $border-color;
        padding: 8px;
        background-color: #F8F8F8;
        font-size: 20px;
        width: 100%;
        max-width: 300px;
    }
}


.range-btn{
    margin: 0px !important;
    width: 80px !important;
}

.range-btn-inner h3{
    font-size: 30px;
}
.range-btn-inner{
    min-height: 50px;
    width: 50px!important;
    border-radius: 4px!important ;
}
.button-group button {
    margin: 0;
    border: none;
    background: $button-blue;
    border-radius: 4px;
    color: #fff;
    width: 32px;
    height: 45px;
}
.mouldflow-setting input{
    height: 50px;
    border-radius: 0;
    border: 1px solid #D1D1D1;
    border-radius: 4px;
}

.mouldflow-setting .button-group .range-btn-inner.left-btn{
    border-top-right-radius: 0;
    border-bottom-right-radius: 0;
}
.mouldflow-setting .button-group .range-btn-inner.right-btn{
    border-top-left-radius: 0px;
    border-bottom-left-radius: 0px;
}
.mouldflow-setting input::placeholder, .mouldflow-setting textarea::placeholder {
    color: #D1D1D1;
    
}
.new-mould-range-btn input{
    border-radius: 0px !important;
}
.mouldflow-setting select{
    width: 100%;
    max-width: 100% !important;
}

.custom-radio{
    padding: 14px 50px;
    border-radius: 4px;
    background: #1A936F;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
    height: 50px;
    color: #fff;
}
.custom-radio-disabled{
    padding: 14px 50px;
    border-radius: 4px;
    background: #F6F6F6;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
    color: #9B9B9B;
    border: 1px solid #D1D1D1;
    height: 50px;
}
.custom-radio-disabled input{
   display: none;
}
.custom-radio label{
    color: #fff !important;
}

.mouldflow-setting .custom-radio input, .mouldflow-setting .custom-radio-disabled input{
    height: 20px !important;
    width: 20px !important;
    border-radius: 50%;
}


.cavity-value input{
    background: #fff;
    border: 1px solid #D1D1D1;
    padding: 0;
    border-radius: 4px;
    max-width: 105px;
    width: 100%;
    text-align: center;
    color: #000;
    height: 40px;

}
// Radio Button CSS 
.btn-check + .btn {
    padding: 0.5rem 1.5rem;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    background-color: #f5f5f5; /* gray for unchecked */
    color: #333;
    border: 1px solid #ccc;
    // margin-right: 10px; /* space between buttons */
  }
.btn-check:checked + .btn {
    background-color: #009973;
    color: white;
    border-color: #009973;
}
.check-icon {
    display: none;
    font-size: 1rem;
}
.btn-check:checked + .btn .check-icon {
    display: inline;
}
// ------------------

.mouldflow-setting label{
color: #4E4E4E;


}
// .card-box .custom-radio input, .card-box .custom-radio-disabled input{
//     background-color: #fff !important;
//     border-color: transparent !important;
// }
// .custom-radio .form-check-input:checked[type=radio] {
//     --bs-form-check-bg-image: url("../../../../assets/images/check_green.png") !important;
//     background-size: auto;
//     background-repeat: no-repeat;
   

// }

// .custom-radio .form-check-input:checked {
//     background-color: #fff;
//     border-color: #fff;
// }

// .custom-radio  .form-check-input:focus, .custom-radio-disabled .form-check-input:focus {
//     box-shadow: none;
// }


.mouldflow-setting  input::-webkit-outer-spin-button,
input::-webkit-inner-spin-button {
  -webkit-appearance: none;
  margin: 0;
}

// input[type="radio"]:checked + .custom-radio {
//     background-color: green;
//     color: white; /* optional */
//     border-radius: 5px; /* optional */
//     padding: 5px 10px; /* optional */
// }

// Custom Slider CSS 
::ng-deep {
    .ngx-slider .ngx-slider-pointer:after {
        content: "";
       display: none;
    }
    .custom-slider .ngx-slider .ngx-slider-bar {
      height: 2px;
    }
    .custom-slider .ngx-slider .ngx-slider-pointer {
      width: 20px;
      height: 28px;
      top: auto; 
      bottom: -10px;
      background-color: $button-blue;
      border-top-left-radius: 3px;
      border-top-right-radius: 3px;
    }
    .custom-slider .ngx-slider .ngx-slider-bubble {
      bottom: 14px;
    }
    .custom-slider .ngx-slider .ngx-slider-bubble.ngx-slider-limit {
      font-weight: bold;
    }
    .custom-slider .ngx-slider .ngx-slider-tick {
      width: 1px;
      height: 10px;
      margin-left: 4px;
      border-radius: 0;
      background: #ffe4d1;
      top: -1px;
    } 
}
// ------------------
.dropdown-toggle::after{
    position: absolute;
    right: 15px;
    top: 20px;
}
.cross-icon{
    margin: 0;
    border: none;
    background: $button-blue;
    border-radius: 4px;
    color: #fff;
    width: 50px;
    height: 50px;
    background: $btn-red;
    img{
        filter: brightness(0) invert(1);
        height: 15px;
    }
}
.dropdown-menu li button{
    background-color: #1A936F14;
    border: 1px solid #1A936F14;
    width: 100%;
    text-align: center;
    font-size: 16px;
    color: #1A936F;
    padding: 7px;
}
.dropdown-menu li:last-child{
    padding: 0px;
}
.dropdown-menu li{
    padding: 7px 12px;
}
.dropdown-menu{
    padding: 0px;
    width: 100%;
}
.dropdown-toggle{
    position: relative;
    border: 1px solid #D1D1D1;
    background: transparent;
    border-radius: 4px;
    padding: 6px 12px;
    height: 50px;
    width: 100%;
    text-align: left;
}
.is-invalid{
    border: 1px solid red!important;
}