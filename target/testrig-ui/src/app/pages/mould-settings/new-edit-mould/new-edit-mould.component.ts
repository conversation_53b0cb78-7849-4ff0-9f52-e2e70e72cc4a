import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { NgxSliderModule, Options } from '@angular-slider/ngx-slider';
import { FormGroup, ReactiveFormsModule } from '@angular/forms';
import { FormBuilder, Validators } from '@angular/forms';
import Notiflix from 'notiflix';
import { ActivatedRoute, Router } from '@angular/router';
import { MouldService } from '../../../services/mould.service';
import { TranslatePipe, TranslateService } from '../../../translate';

@Component({
  selector: 'app-new-edit-mould',
  standalone: true,
  imports: [CommonModule, NgxSliderModule, ReactiveFormsModule, TranslatePipe],
  templateUrl: './new-edit-mould.component.html',
  styleUrl: './new-edit-mould.component.scss'
})
export class NewEditMouldComponent {
  mould:any = {
    'edit': false,    
    'placeholders': {},
  }
  button:string = 'Add Excel';
  cavityOptions: Options = {
    floor: 0,
    ceil: 30,
    showSelectionBar: true,
    showTicks: true
  };

  mouldForm: FormGroup;

  constructor(
    private fb: FormBuilder, 
    private router: Router, 
    private mouldService: MouldService,
    private route: ActivatedRoute,
    private translate: TranslateService
    ) {
      this.mould.placeholders['name'] = this.translate.instant('Enter Name')
      this.mould.placeholders['orderNumber'] = this.translate.instant('5 digit number')
      this.mould.placeholders['maintenance'] = this.translate.instant('maintenance')
      this.mould.placeholders['description'] = this.translate.instant('Enter Description')
    this.mouldForm = this.fb.group({
      name: ['', Validators.required],
      sector: ['', Validators.required],
      description: ['', Validators.required],
      year: ['', [Validators.required, this.yearOrDateValidator]],
      orderNumber: ['', [
        Validators.required,
        Validators.pattern(/^\d{5,7}$/)
      ]],
      client: ['', Validators.required],
      clientName: ['', Validators.required],
      numberMould: [1, [Validators.required, Validators.pattern('^[0-9]+$')]],
      cavityValue: [15, [Validators.required, Validators.min(0), Validators.max(30)]],
      mouldVolume: [2, [Validators.required, Validators.pattern('^[0-9]+$')]],
    });
    this.mouldForm.patchValue({ sector: 'new-mould' }); 
    this.mouldForm.patchValue({ year: Math.min(new Date().getFullYear()) }); 

    // get all moulds
    this.mouldService.listMouldWithData().subscribe({
      next: (moulds) => {
        if(moulds && moulds.data){
          this.mould['allMoulds'] = moulds.data
          if(window.location.href.includes('edit')){
            this.mould.edit = true
            this.button = 'Update Excel'
            const mouldId = this.route.snapshot.paramMap.get('mould-id');
            let currentMould = this.mould['allMoulds'].filter((mould:any) => mould.Name == mouldId);
            this.mould['currentMould'] = currentMould[0]
            this.setCurrentMouldInFormData(currentMould[0]);
          } 
        }
      },
      error: (err) => {
        console.log('✌️err --->', err);
      }
    })

    // Auto-set orderNumber based on sector value
    this.mouldForm.get('sector')?.valueChanges.subscribe(sectorValue => {
      const isEdit = this.mould.edit;
      const currentOrderNumber = this.mould['currentMould']?.['Order Number'] || '';

      let value = '';

      if (sectorValue === 'maintenance') {
        value = 'Maintenance';
        if (isEdit && currentOrderNumber.toLowerCase().startsWith('maintenance')) {
          value = currentOrderNumber;
        }
      } else {
        value = isEdit ? currentOrderNumber : '';
      }

      this.mouldForm.get('orderNumber')?.setValue(value);
    });

  }

  /**
   * Custom validator: Accepts either a 4-digit year or a date in dd/mm/yyyy format
   */
  yearOrDateValidator(control: any) {
    const value = control.value;
    if (!value) return { required: true };
    // Accept 4-digit year
    if (/^\d{4}$/.test(value)) return null;
    // Accept dd/mm/yyyy
    if (/^\d{2}\/\d{2}\/\d{4}$/.test(value)) {
      const [d, m, y] = value.split('/').map(Number);
      if (d >= 1 && d <= 31 && m >= 1 && m <= 12 && y >= 1900 && y <= 2100) return null;
    }
    return { invalidDate: true };
  }

  /**
   * Set the value of current mould in form data
   * @param mould 
   */
  setCurrentMouldInFormData(mould:any){
    let yearValue = mould['Year'];
    // If yearValue is a number or 4-digit year, convert to string
    if (typeof yearValue === 'number' || /^\d{4}$/.test(yearValue)) {
      yearValue = yearValue.toString();
    }
    this.mouldForm.patchValue({ name: mould['Name'] }); 
    this.mouldForm.patchValue({ year: yearValue }); 
    this.mouldForm.patchValue({ clientName: mould['Client'] }); 
    this.mouldForm.patchValue({ client: mould['Client'] }); 
    this.mouldForm.patchValue({ orderNumber: mould['Order Number'] }); 
    this.mouldForm.patchValue({ description: mould['Description'] }); 
    this.mouldForm.patchValue({ numberMould: mould['No. of Moulds'] }); 
    this.mouldForm.patchValue({ cavityValue: mould['No. of Cavity'] }); 
    this.mouldForm.patchValue({ mouldVolume: mould['Volume'] }); 
    if(mould['Order Number'].includes('maintenance') || mould['Order Number'].includes('Maintenance')){
      this.mouldForm.patchValue({ sector: 'maintenance' });
    }
  }

  /**
   * Edit existing or add new mould as per button condition
   */
  onSubmit() {
    this.button = 'Processing...'
    if(this.addClient)
      this.mouldForm.patchValue({ client: this.mouldForm.value.clientName }); 
    else
      this.mouldForm.patchValue({ clientName: this.mouldForm.value.client }); 
    if(this.mouldForm.invalid)  {
      this.mouldForm.markAllAsTouched();
      this.button = this.mould.edit? 'Update Excel':'Add Excel'
      Notiflix.Notify.failure(this.translate.instant('Please set incorrect fields!'));
      return
    }
    // Handle year/date field
    let yearInput = this.mouldForm.value.year;
    if (/^\d{4}$/.test(yearInput)) {
      yearInput = `01/01/${yearInput}`;
    }
    // If date is in dd/mm/yyyy, keep as is
    let mouldData ={}
    if (this.mouldForm.valid) {
      mouldData = {
        "Name": this.mouldForm.value.name,
        "Year": yearInput,
        "Order Number": this.mouldForm.value.orderNumber,
        "Client": this.mouldForm.value.clientName,
        "Description": this.mouldForm.value.description,
        "No. of Moulds": this.mouldForm.value.numberMould,
        "No. of Cavity": this.mouldForm.value.cavityValue,
        "Volume": this.mouldForm.value.mouldVolume,
      }
    }
    if(this.mould.edit){
      this.mouldForm.value
      this.mouldService.editCurrentMould(this.mould['currentMould']['Order Number'], mouldData).subscribe({
        next: (result) =>{
          Notiflix.Notify.success(this.translate.instant('Mould updated successfully!'));
          this.button = 'Update Excel'
        },
        error: (err) => {
          Notiflix.Notify.failure(this.translate.instant('Unable to update Excel!'));
          this.button = 'Update Excel'
        }
      })
      this.mould['currentMould']['Order Number']
    }else{
      if (this.mouldForm.valid) {
        mouldData = JSON.parse(JSON.stringify(mouldData))
        this.mouldService.addNewMould({...mouldData,...{}}).subscribe({
          next: (result) =>{
            Notiflix.Notify.success(this.translate.instant('Mould added successfully!'));
            this.button = 'Add Excel'
            window.location.href = '/mould';
          },
          error: ()=>{
            console.log('✌️Unable to add new mould');
            this.button = 'Add Excel'
          }
        })
      } 
    }
  }

  /**
   * Increment or decrement the year of number of moulds
   * @param type (Increment/Decrement)
   * @param value (In which need to increment/decrement)
   */
  incrementDecrement(type: string, value: number, key: string){
    const control = this.mouldForm.get(key);
    if (!control) return;

    let updatedValue = value;
    if(type == 'increment'){
      if(key == 'year')
        // updatedValue = Math.min(updatedValue + 1, new Date().getFullYear());
        updatedValue = updatedValue + 1;
      else
        updatedValue += 1
    } else if(type == 'decrement' && updatedValue > 0){ // Ensure value is not negative
      updatedValue -= 1;
    }
    control.setValue(updatedValue);
  }

  addClient:boolean = false;
  setClientValue(client: string) {
    // if(client)
    this.mouldForm.patchValue({ client: client });
    // this.mouldForm.patchValue({ client: client });
    // else{

    // }
  }

}
