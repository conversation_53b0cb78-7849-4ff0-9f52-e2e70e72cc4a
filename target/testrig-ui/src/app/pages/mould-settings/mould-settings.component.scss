@import '../../../styles/variables';

.mouldflow-setting {
    height: calc(100vh - 80px);
    padding: 0px 30px 20px 30px;

    .setting-heading {
        display: flex;
        // justify-content: space-between;
        gap: 10px;
        align-items: center;
        flex-wrap: wrap;
        // margin-bottom: 20px;

        .mould-id {
            display: flex;
            gap: 10px;
            align-items: center;

            label {
                font-size: 16px;
                min-width: max-content; 
                color: #4E4E4E;
            }
            input {
                height: 38px;
                border: 1px solid $border-color;
                border-radius: 4px;
            }
            select{
                font-size: 20px;
                min-width: 200px;
                padding:8px;
            } 
        }

        .limit {
            display: flex;
            gap: 8px;
            color: #4E4E4E;
            font-size: 16px;
            align-items: center;

            .limit-text {
                background: #E1E1E1;
                padding: 4px 12px 2px;
                border-radius: 8px;
                color: #4E4E4E;
                text-transform: uppercase;
                display: inline-flex;
                align-items: center;
            }
        }
        .form-check {
            background: transparent;
            border: 1px solid #dee2e6;
            border-radius: 4px;
        }
        .form-check img{
            max-height: 40px;
        }
    }
}
.is-disabled{
    pointer-events: none;
    opacity: 0.4;
}
.is-active{
    border: 2px solid $green!important;
    border-radius: 4px!important;
}

.card-box 
{
    display: flex;
    flex-direction: column;
    box-shadow: 0px 12px 20px 0px #E0F3FD;
    padding: 18px;
    background-color: #fff;
    border-radius: 8px;
    .table 
    {
        margin: 0;
        th, td 
        {
            padding: 8px 10px;
            border-color: $border-color;
            vertical-align: middle;
        }
        th 
        {
            color: #4E4E4E;
            font-size: 16px;
            font-weight: 500;
            height: 45px;
            &:first-child 
            {
                text-align: center;
            }
        }
        td 
        {
            color: #000000;
            font-size: 13px;
            &:first-child 
            {
                text-align: center;
            }
            
        }
    }

    .form-check-input {
        width: 24px;
        height: 24px;
        border-color: $border-color;
        background-color: #F3F3F3;
        cursor: pointer;
        &:checked
        {
            background-color: $button-blue;
            border-color: $button-blue;
        }
    }
    .form-select
    {
        border-color: $border-color;
        padding: 8px;
        background-color: #F8F8F8;
        font-size: 20px;
        width: 100%;
        max-width: 300px;
    }
}

.water-pump {
    padding: 5px 0;
    align-items: center;
    strong {
        font-size: 16px;
        color: #4E4E4E;
    }
    .water-pump-select {
        display: flex;
        align-items: center;
        gap: 20px;
        font-size: 16px;
        color: #4E4E4E;

        label 
        {
            white-space: nowrap;
        }
        
    }
}
.all-button  {
    padding-top: 15px;
    border-top: 1px solid $border-color;
}
.card-steps 
{
    display: flex;
    justify-content: center;
    .card-steps-inner
    { 
        margin-bottom: 25px;
        display: inline-flex;  
        width: 100%; 
        justify-content: space-between;
        align-items: center;
        .line-grow
        {
            flex: 1;
            background-color: $button-blue;
            height: 1px;
            &.light-line
            {
                background-color: rgba(9, 144, 210, 0.07);
            }
        }
        .btn-primary{
            background-color: rgba(9, 144, 210, 0.05);
            color: $button-blue;
            display: flex;
            align-items: center;
            font-weight: 500;
            font-size: 16px;
            justify-content: center;
            padding: 0;
            position: relative;
            z-index: 2;
        }
        .card-step-btn
        {
            background-color: rgba(9, 144, 210, 0.05);
            border: none;
            border-radius: 100%; 
            color: rgba(9, 144, 210, 0.40);
            width: 32px;
            height: 32px;
            display: flex;
            align-items: center;
            font-weight: 500;
            font-size: 16px;
            justify-content: center;
            padding: 0;
            position: relative; 
            z-index: 2;
            &.active
            {   
                background-color: $button-blue;
                color: #fff; 
            }
            .closeBtn {
                position: absolute;
                width: 15px;
                height: 16px;
                background: #fff;
                align-items: center;
                display: none;
                justify-content: center;
                background: red;
                border-radius: 100%;
                right: -7px;
                top: -4px;
                img 
                {
                    filter: brightness(0) invert(1);
                    height: 8px;
                }
            }

            &.lastBTN:hover 
            {
                .closeBtn
                {
                    display: flex;
                }
            }

           
        }
    }
}

.select-mould{
    height: auto;
}



.number-input {
    display: flex;
    align-items: center;
    gap: 10px;
}

.button-group button {
    margin: 0;
    border: none;
    background: $button-blue;
    border-radius: 4px;
    color: #fff;
    width: 32px;
    height: 45px;
}

.border-box-1{
    border: 1px solid $border-color;
    border-radius: 6px;
    padding: 2px 10px;
    width: 250px;
    height: 48px;
}

.border-box-1 .form-control{
    padding: 0;
    border: none;
    max-width: 100%;
    outline: none !important;
    box-shadow: none;
}

.border-box-1 .form-control::-webkit-outer-spin-button,
.border-box-1 .form-control::-webkit-inner-spin-button {
  -webkit-appearance: none;
  margin: 0;
}

/* Firefox */
.border-box-1 .form-control[type=number] {
  -moz-appearance: textfield;
}

.test-checkbox{
    margin-top: -1px;
    margin-right: 7px;
}
.form-check-label{
    color: #4E4E4E;
}



.range-input label{
    font-size: 16px;
    color: #4E4E4E;
}

.range-btn{
    margin: 0px !important;
    width: 80px !important;
}

.range-btn-water-pressure h3{
    font-size: 40px;
}
.range-btn-water-pressure{
    min-height: 60px;
    width: 80px!important;
}


.delete-channel{
    position: absolute;
    top: 20px;
    right: 5px;
    width: 25px;
    height: 25px;
    background: #ff0000c2;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 100%;
    img {
        filter: brightness(0) invert(1);
    }
}

// Channel number circle style
.channel-circle {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 32px;
    height: 32px;
    border: 2.5px solid #0990D2;
    border-radius: 50%;
    font-size: 16px;
    font-weight: bold;
    color: #0990D2;
    background: #fff;
    margin: 0 auto;
}

// Thicker vertical border for separation
.borderRight {
    border-right: 5px solid #0990D2 !important;
}

.settings-icon{
    filter: brightness(0) invert(1);
}

.a-disabled{
    opacity: 0.8;
    pointer-events: none;
    text-decoration: none;
}