// Combined schema with Murali's Schema
export const  defaultSchemaConfigurations = 
{
  "time": "",
  "mouldid": "",
  "pressure_set_point_min": 0.0,
  "pressure_set_point_max": 9.0,
  "comments": "",
  "max_water_pressure": 5.0,
  "enable_all": false,
  "channels": [
    
  ],
  "tests" : {
    "flow_test": false,
    "air_leak_test": false,
    "water_leak_test": false,
    "heat_test": false
  }
}

export const defaultSchemaForControls = 
{
  "channel_no": '',
  "channels": [{
    "id": 1,
    "name": "Channel 1",
    "diameter":  11,
  }
  ],
  "time": "",
  "mouldid": "mould-test",
  "pressure_set_point": 0.0,
  "temperature_set_point": 0.0,
  "comments": "",
  "enable_all": true,
}


export const APIResponce = 
{
  "time": "2025-02-14 13:09:00",
  "mouldid": "MouldTest",
  "pressure_set_point": 3.5,
  "temperature_set_point": 23.5,
  "comments": "Test Setup",
  "enable_all": true,
  "channels": [
    {
      "channel_id": 1,
      "channel_name": "Channel 1",
      "channel_enabled": true,
      "channel_diameter": "circle",
      "channel_lower_limit": 1,
      "channel_upper_limit": 20
    },
    {
      "channel_id": 2,
      "channel_name": "Channel 2",
      "channel_enabled": true,
      "channel_diameter": "circle",
      "channel_lower_limit": 1,
      "channel_upper_limit": 20
    },
    {
      "channel_id": 3,
      "channel_name": "Channel 2",
      "channel_enabled": true,
      "channel_diameter": "circle",
      "channel_lower_limit": 1,
      "channel_upper_limit": 20
    },
    {
      "channel_id": 4,
      "channel_name": "Channel 3",
      "channel_enabled": true,
      "channel_diameter": "circle",
      "channel_lower_limit": 1,
      "channel_upper_limit": 20
    },
    {
      "channel_id": 5,
      "channel_name": "Channel 4",
      "channel_enabled": true,
      "channel_diameter": "circle",
      "channel_lower_limit": 1,
      "channel_upper_limit": 20
    },
    {
      "channel_id": 6,
      "channel_name": "Channel 5",
      "channel_enabled": true,
      "channel_diameter": "circle",
      "channel_lower_limit": 1,
      "channel_upper_limit": 20
    },
    {
      "channel_id": 7,
      "channel_name": "Channel 6",
      "channel_enabled": true,
      "channel_diameter": "circle",
      "channel_lower_limit": 1,
      "channel_upper_limit": 20
    },
    {
      "channel_id": 8,
      "channel_name": "Channel 7",
      "channel_enabled": true,
      "channel_diameter": "circle",
      "channel_lower_limit": 1,
      "channel_upper_limit": 20
    },
    {
      "channel_id": 9,
      "channel_name": "Channel 8",
      "channel_enabled": true,
      "channel_diameter": "circle",
      "channel_lower_limit": 1,
      "channel_upper_limit": 20
    },
    {
      "channel_id": 10,
      "channel_name": "Channel 9",
      "channel_enabled": true,
      "channel_diameter": "circle",
      "channel_lower_limit": 1,
      "channel_upper_limit": 20
    },
    {
      "channel_id": 11,
      "channel_name": "Channel 10",
      "channel_enabled": true,
      "channel_diameter": "circle",
      "channel_lower_limit": 1,
      "channel_upper_limit": 20
    },
    {
      "channel_id": 12,
      "channel_name": "Channel 11",
      "channel_enabled": true,
      "channel_diameter": "circle",
      "channel_lower_limit": 1,
      "channel_upper_limit": 20
    }
  ]
}



