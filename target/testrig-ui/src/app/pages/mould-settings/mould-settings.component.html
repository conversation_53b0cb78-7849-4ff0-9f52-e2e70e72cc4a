<section class="mouldflow-setting" >
    <div class="container-fluid d-flex flex-column h-100" >
        <!-- <div class="container-fluid" *ngIf="false"> -->
        <!-- <div class="container-fluid"> -->
        <div class="setting-heading py-3">
            <div class="mould-id align-items-end">
                <div>
                    <label class="mould-label">{{"Mould Name"|translate}} :</label>  
                    <select class="form-select"  [(ngModel)]="mouldSelected" (change)="fetchSettingsByMouldId($event); clickedRun = 1" >
                        <option *ngFor="let mould of dynamicDropdowns.mouldNameDropdown" [selected]="mould.Name == mouldSelected">
                            {{ mould.Name }}
                        </option>
                    </select>
                </div>
                <ul class="dropdown-menu p-2" aria-labelledby="settings-mould">
                    <li class="mb-1">
                        <a class="btn btn-primary w-100" [href]="['/mould/new']" > 
                            {{ this.button.newMould | translate}} 
                        </a>
                    </li>
                    <li class="mb-1">  
                        <a class="btn btn-secondary w-100" [href]="'mould/edit/' + mouldSelected" [ngClass]="{'a-disabled': dynamicDropdowns.mouldNameDropdown.length == 0}">
                        {{ this.button.editMould | translate}} 
                        </a>
                        <!-- [disabled]="dynamicDropdowns.mouldNameDropdown.length == 0" -->
                    </li>
                    <li> 
                        <button class="btn btn-danger w-100 " (click)="deleteMould()" [ngClass]="{'a-disabled': dynamicDropdowns.mouldNameDropdown.length == 0}"> 
                            {{ this.button.deleteMould | translate}} 
                        </button>
                    </li>
                </ul>
                <button class="btn btn-primary btn-icon dropdown-toggle" id="settings-mould" data-bs-toggle="dropdown" aria-expanded="false">
                    <img src="assets/images/settingicon.svg" class="settings-icon" height="21px" alt="chat" />
                </button>
                <button class="btn btn-primary btn-icon" (click)="toggleCommentSideBar()">
                    <img src="assets/images/chat.svg" alt="chat" />
                </button>
                <div class="d-flex gap-4">
                    <!-- <button class="btn btn-bordered" [disabled]="!((buttonCount.length > 1) && (clickedRun > 1))" (click)="onTabChange('previous')">{{"Previous"|translate}}</button>
                    <button class="btn btn-primary" [disabled]="buttonCount.length == clickedRun" (click)="onTabChange('next')">{{"Next"|translate}}</button> -->
                    <button class="btn btn-primary"  
                        (click)="saveBtnUpdateSchema(button.mould == 'Save'? btuttonAction.SAVE: btuttonAction.UPDATE)"
                        [disabled]="button.mould == 'Processing'">{{ button.mould | translate }}</button>
                    
                </div>  
            </div>
            <div class="d-flex gap-3 justify-content-center w-50">
                <button 
                class="form-check tests" 
                [ngClass]="{'is-active': this.status?.hotClean, 'is-disabled': this.status?.coldClean || !this.lowerSensor}"
                (click)="cleaningStartStop('hotClean', this.status?.hotClean)"
                [disabled]="this.status?.coldClean || !this.lowerSensor">
                    <div class="test-inner">
                        <img src="assets/images/hot-cleaning.png" width="70px"/>
                        <span class="form-check-label"> {{"Hot Water Cleaning"|translate}}</span>
                    </div>
                </button>
                <button 
                class="form-check tests"
                [ngClass]="{ 'is-active': this.status?.coldClean, 'is-disabled': this.status?.hotClean || !this.lowerSensor }"
                [disabled]="this.status?.hotClean || !this.lowerSensor"
                (click)="cleaningStartStop('coldClean', this.status?.coldClean)">
                    <div class="test-inner">
                        <img src="assets/images/cold-water.png" />
                        <span class="form-check-label" > {{"Cold Water Cleaning"|translate}}</span>
                    </div>
                </button>
                <button 
                class="form-check tests is-active"
                [ngClass]="{'is-active': this.filter.statusRunning,'is-disabled': !this.lowerSensor}"
                [disabled]="!this.lowerSensor"
                (click)="autoFilterStop()">
                    <div class="test-inner">
                        <img src="assets/images/auto-filter.png" />
                        <span class="form-check-label" > {{"Auto Filter Cleaning"|translate}}</span>
                    </div>
                </button>
            </div>
        </div>
        <div class="comment-sidebar" [ngClass]="{ 'hidden': isCommentSidebarVisible }">
            <button (click)="toggleCommentSideBar()" class="crossBtn">
                <img src="assets/images/close_small.svg" alt="close" />
            </button>
            <h4>{{"Comments"|translate}}</h4>
            <div class="comment-box">
                <div class="comment-form">
                    <textarea placeholder="Add Comment" [value]="stepperData.comments"  (blur)="settingSaved = true"
                        [(ngModel)]="stepperData.comments"></textarea>
                </div>
            </div>
        </div>

        <div class="overlay-box" (click)="toggleCommentSideBar()" [ngClass]="{ 'active': isCommentSidebarVisible }">
        </div>
        <div class="card-box flex-fill">
            <div class="card-steps">
                <div class="card-steps-inner">
                    <span *ngFor="let run of buttonCount" class="flex-fill d-flex align-items-center">
                        <button class="card-step-btn"
                            [ngClass]="checkLastRun(run)"
                            (click)="clickedRun = run">
                            {{ run }}
                            <div class="closeBtn" >
                                <img src="assets/images/close_small.svg" alt="close" />
                            </div>
                        </button>
                        <div class="line-grow" *ngIf="!((buttonCount.length) == run)"></div>
                        <div class="line-grow light-line" *ngIf="((buttonCount.length) == run)"></div>
                    </span>
                    <span class="">
                        <!-- <div class="line-grow light-line"></div> -->
                        <!-- <button class="btn btn-primary"  title="Click to add new channel" 
                            (click)="manageChannel('add')">
                            {{"Channel"|translate}} <img src="assets/images/add.svg" alt="add" />
                        </button> -->
                    </span>
                </div>
            </div>
            <div class="card-data row">
                <div class="col-md-6 pe-0 borderRight">
                    <div>
                        <div class="table-repsonsive">
                            <table class="table">
                                <thead>
                                <tr>
                                <th> {{"Instructions"|translate}} </th>
                                <!-- <th> Channel Settings </th> -->
                                <th> {{"Hydraulic Diameter"|translate}} </th>
                                <th>
                                <div class="form-check">
                                </div>
                                </th>
                                <th> {{"ch"|translate}} </th>
                                </tr>
                                </thead>
                                <!-- clicked Run {{ clickedRun }} -->
                                
                                <tbody>
                                <tr  *ngFor="let channel of stepperData?.channels | slice: sliceStartEnd[clickedRun-1].firstStart: sliceStartEnd[clickedRun-1].firstEnd" 
                                class="selected-to-delete">
                                <td>
                                <!-- {{channel.channel_name}} {{channelNameDropdown[0]}}-{{channel.channel_name && channel.channel_name != '' ? 'SD': channelNameDropdown[0]}} -->
                                <select class="form-select" aria-label="select example"
                                [(ngModel)]="channel.channel_name">
                                <option *ngFor="let channel_dropdown_name of dynamicDropdowns.channelNameDropdown" 
                                [value]="channel_dropdown_name" 
                                [selected]="channel.channel_name && channel.channel_name != '' ? (channel_dropdown_name.toLowerCase() == channel.channel_name.toLowerCase()): dynamicDropdowns.channelNameDropdown[0]">
                                {{ channel_dropdown_name }}
                                </option>
                                </select>
                                </td>
                                <!-- <td>
                                <div style="position: relative; width: 100%;">
                                <ngx-slider [(value)]="channel.channel_lower_limit"
                                [(highValue)]="channel.channel_upper_limit"
                                (userChangeEnd)="updateRunElement('slider', channel)"
                                [options]="setFlowOptions(channel.channel_id)" ></ngx-slider>
                                <div [style.left.%]="midValuePosition(channelCurrentValues[channel.channel_id -1 ].values.sensor_val, options)" 
                                style="position: absolute; top: 35px;">{{ channelCurrentValues[channel.channel_id -1 ].values.sensor_val }} </div>
                                </div>
                                </td> -->
                                <td>
                                <select class="form-select" aria-label="select example" 
                                [(ngModel)]="channel.channel_diameter">
                                <option *ngFor="let diameter_dropdown_name of dynamicDropdowns.diameterDropdown" 
                                [value]="diameter_dropdown_name"
                                [selected]="channel.channel_diameter? (diameter_dropdown_name.toLowerCase() == channel.channel_diameter.toLowerCase()): dynamicDropdowns.diameterDropdown[0]">
                                {{ diameter_dropdown_name }}
                                </option>
                                </select>
                                </td>
                                <td class="position-relative">
                                <div class="form-check">
                                <input class="form-check-input" type="checkbox" value="" 
                                (change)="updateRunElement('channel-enable', $event, {'channel_id': channel.channel_id})"
                                id="flexCheckChecked" [checked]="channel.channel_enabled">
                                </div>
                                <div class="delete-channel" *ngIf="channel.channel_id > 12 && channel.channel_id == stepperData.channels.length"
                                (click)="manageChannel('remove')">
                                
                                <img src="assets/images/close_small.svg" />
                                </div>
                                </td>
                                <td><span class="channel-circle">{{ channel.channel_id }}</span></td>
                                </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>

                </div>
                <div class="col-md-6 ps-0">
                    <div>
                        <div class="table-repsonsive">
                            <table class="table">
                                <thead>
                                <tr>
                                <th> {{"Instructions"|translate}} </th>
                                <!-- <th> Channel Settings </th> -->
                                <th> {{"Hydraulic Diameter"|translate}} </th>
                                <th>
                                <div class="form-check">
                                <input class="form-check-input" type="checkbox" value=""
                                id="flexCheckChecked"
                                (change)="updateRunElement('enable-all', $event)"
                                [checked]="this.stepperData.enable_all">
                                </div>
                                </th>
                                <th> {{"ch"|translate}} </th>
                                </tr>
                                </thead>
                                <tbody>
                                <tr *ngFor="let channel of stepperData?.channels | slice: sliceStartEnd[clickedRun-1].secondStart: sliceStartEnd[clickedRun-1].secondEnd">
                                <td>
                                <select class="form-select" aria-label="select example"
                                [(ngModel)]="channel.channel_name">
                                <option *ngFor="let channel_dropdown_name of dynamicDropdowns.channelNameDropdown" 
                                [value]="channel_dropdown_name"
                                [selected]="channel.channel_name? (channel_dropdown_name.toLowerCase() == channel.channel_name.toLowerCase()): dynamicDropdowns.channelNameDropdown[0]">
                                {{ channel_dropdown_name }}
                                </option>
                                </select>
                                </td>
                                <!-- <td>
                                <div style="position: relative; width: 100%;">
                                <ngx-slider [(value)]="channel.channel_lower_limit"
                                [(highValue)]="channel.channel_upper_limit"
                                (userChangeEnd)="updateRunElement('slider', channel)"
                                [options]="setFlowOptions(channel.channel_id)" ></ngx-slider>
                                <div [style.left.%]="midValuePosition(channelCurrentValues[channel.channel_id - 1].values.sensor_val, options)" 
                                style="position: absolute; top: 35px;">{{ channelCurrentValues[channel.channel_id - 1].values.sensor_val }} </div>
                                </div>
                                </td> -->
                                <td>
                                <select class="form-select" aria-label="select example" 
                                [(ngModel)]="channel.channel_diameter">
                                <option *ngFor="let diameter_dropdown_name of dynamicDropdowns.diameterDropdown" 
                                [value]="diameter_dropdown_name"
                                [selected]="channel.channel_diameter? (diameter_dropdown_name.toLowerCase() == channel.channel_diameter.toLowerCase()): dynamicDropdowns.diameterDropdown[0]">
                                {{ diameter_dropdown_name }}
                                </option>
                                </select>
                                </td>
                                <td class="position-relative">
                                <div class="form-check">
                                <input class="form-check-input" type="checkbox" value="" 
                                (change)="updateRunElement('channel-enable', $event, {'channel_id': channel.channel_id})"
                                id="flexCheckChecked" [checked]="channel.channel_enabled">
                                </div>
                                
                                <div class="delete-channel" *ngIf="channel.channel_id > 12 && channel.channel_id == stepperData.channels.length"
                                (click)="manageChannel('remove')">
                                <img src="assets/images/close_small.svg" />
                                </div>
                                </td>
                                <td><span class="channel-circle">{{ channel.channel_id }}</span></td>
                                <!-- <td *ngIf="channel.channel_id == stepperData.channels.length">
                                <input class="form-check-input" type="checkbox" value="" 
                                (change)="updateRunElement('channel-enable', $event, {'channel_id': channel.channel_id})"
                                id="flexCheckChecked" [checked]="channel.channel_enabled">
                                </td> -->
                                </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>  
                </div>

            </div>
            <div class="water-pump d-flex justify-content-between  mt-auto">
                <div class="col-sm-5">
                    <div> 
                        <strong>{{"Available Tests"|translate}} : </strong>
                    </div>
                    <div class="d-flex gap-3">
                        <div class="d-flex gap-2 align-items-center" *ngIf="stepperData.tests">
                            <label class="form-check tests">
                                <input class="form-check-input test-checkbox" type="checkbox" id="allTestCheckbox" 
                                (change)="selectTest('all', $event)"  
                                [checked]="stepperData.tests['water_leak_test'] && 
                                            stepperData.tests['air_leak_test'] &&
                                            stepperData.tests['heat_test'] &&
                                            stepperData.tests['flow_test'] ">
                                <div class="test-inner">
                                    <img src="assets/images/mark.png" />
                                    <span class="form-check-label" for="allTestCheckbox">{{"All Tests"|translate}}</span>
                                </div>   
                            </label>
                            <label class="form-check tests">
                                <input class="form-check-input test-checkbox" type="checkbox" id="flowTestCheckbox"
                                [checked]="stepperData.tests['flow_test']" (change)="selectTest('flow_test', $event)">
                                <div class="test-inner">
                                    <img src="assets/images/flow-test.png" />
                                    <span class="form-check-label" for="flowTestCheckbox">{{"Flow Test"|translate}}</span>
                                </div>
                            </label>
                            <label class="form-check tests">
                                <input class="form-check-input test-checkbox" type="checkbox" id="airLeakTestCheckbox" 
                                [checked]="stepperData.tests['air_leak_test']" (change)="selectTest('air_leak_test', $event)">
                                <div class="test-inner">
                                    <img src="assets/images/air-leak-test.png" />
                                    <span class="form-check-label" for="airLeakTestCheckbox">{{"Air Leak Test"|translate}}</span>
                                </div>
                            </label>
                            <label class="form-check tests">
                                <input class="form-check-input test-checkbox" type="checkbox" id="waterLeakTestCheckbox"
                                [checked]="stepperData.tests['water_leak_test']" (change)="selectTest('water_leak_test', $event)">
                                <div class="test-inner">
                                    <img src="assets/images/water-leak-test.png" />
                                    <span class="form-check-label" for="waterLeakTestCheckbox">{{"Water Leak Test"|translate}}</span>
                                </div>
                            </label>                            
                            <label class="form-check tests" >
                                <input class="form-check-input test-checkbox" type="checkbox" id="heatTestCheckbox"
                                [checked]="stepperData.tests['heat_test']" (change)="selectTest('heat_test', $event)">
                                <div class="test-inner">
                                    <img src="assets/images/heat-test.png" />
                                    <span class="form-check-label" for="heatTestCheckbox">{{"Heat Test"}}</span>
                                </div>
                            </label>
                        </div>
                        <!-- <div class="water-pump-select">
                            <label for="inletPressure">Inlet Pressure:</label>
                            <div class="d-flex align-items-center">
                                <input type="number" step="0.1" class="form-control" id="inletPressure" placeholder="Inlet Pressure" readonly [value]="inletPressure || inletPressure == 0? inletPressure.toFixed(1): null">
                            </div>
                        </div> -->
                        <!-- <div class="water-pump-select">
                            <label for="outletPressure">Outlet Pressure</label>
                            <div class="d-flex align-items-center ">
                                <input type="text" readonly class="form-control" id="outletPressure" placeholder="Outlet Pressure"
                            [value]="outletPressure || outletPressure == 0? outletPressure.toFixed(1): null">
                            </div>
                        </div> -->
                    </div>
                </div>
                <div class="col-sm-2">
                    <img src="assets/images/channel-direction_1.png"  style="max-width: 100%;" />
                </div>
                <div class="col-sm-2 ms-4">
                    <div class="w-100 d-flex justify-content-center">
                        <label><strong>{{"Max Water Pressure"|translate}} </strong></label>
                    </div>
                    <div class="d-flex justify-content-center align-items-center">
                        <div>
                            <!-- MIN -->
                            <!-- <label><strong>{{"Min"|translate}} :</strong></label> -->
                            <div class="button-group range-btn">
                                <button class="range-btn-water-pressure" (click)="maxPressureChange('minValue')">
                                    <h3 class="m-0">-</h3>
                                </button>
                            </div>
                        </div>
                        <div class="m-3">
                            <!-- value -->
                            <b> {{ stepperData.max_water_pressure? stepperData.max_water_pressure : 5  }} bars</b>
                        </div>
                        <div>
                            <!-- Max -->    
                            <!-- <label> <strong>{{"Max"|translate}} :</strong></label> -->
                            <div class="button-group range-btn">
                                <button class="range-btn-water-pressure" (click)="maxPressureChange('maxValue')">
                                    <h3 class="m-0">+</h3>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-sm-3 pe-4">
                    <div class="d-flex gap-3 justify-content-end">
                        <div class="water-pump-select">
                            <!-- <img src="assets/images/pressure_gauge.png" width="50px" />
                            <label><strong>Pressure Range:</strong> </label> -->
                            <div class="d-flex gap-1 align-items-center">
                                <div class="range-input">
                                    <label><strong>{{"Min"|translate}} :</strong></label>
                                    <div>
                                        <div class="button-group range-btn">
                                            <button class="range-btn" (click)="incrementRange('minValue', stepperData.pressure_set_point_min)"><h3 class="m-0">+</h3></button>
                                        </div>
                                        <span class="d-block text-center font-weight-bold my-1">{{ stepperData.pressure_set_point_min }}  </span>
                                        <div class="button-group ">
                                            <button class="range-btn" (click)="decrementRange('minValue', stepperData.pressure_set_point_min)"><h3 class="m-0">-</h3></button>
                                        </div>
                                    </div>
                                </div>
                                <label class="tests">
                                    <div class="test-inner border-0">
                                        <img src="assets/images/pressure_gauge.png" />
                                        <span class="form-check-label" > <b>{{"Pressure Range"|translate}} :</b></span>
                                    </div>
                                </label>
                                <div class="range-input">
                                    <label> <strong>{{"Max"|translate}} :</strong></label>
                                    <div>
                                        <div class="button-group range-btn">
                                            <button class="range-btn" (click)="incrementRange('maxValue', stepperData.pressure_set_point_max)"><h3 class="m-0">+</h3></button>
                                        </div>
                                        <span class="d-block text-center font-weight-bold my-1">{{ stepperData.pressure_set_point_max}}</span>
                                        
                                        <div class="button-group ">
                                            <button class="range-btn" (click)="decrementRange('maxValue', stepperData.pressure_set_point_max)"><h3 class="m-0">-</h3></button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="all-button d-flex align-items-center justify-content-between gap-2">
                <!-- <button class="btn btn-green"  data-bs-target="#available-moulds" (click)="getAvailableMoulds()" data-bs-toggle="modal">
                    <img src="assets/images/cached.svg" alt="refresh" />
                    Load
                </button>
                
                <div class="modal fade" id="available-moulds" tabindex="-1" aria-labelledby="available-moulds-label" aria-hidden="true">
                    <div class="modal-dialog modal-dialog-centered">
                    <div class="modal-content">
                        <div class="modal-header">
                        <h1 class="modal-title fs-5" id="available-moulds-label">Available Moulds</h1>
                        <button type="button" id="close-available-moulds-label" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                        </div>
                        <div class="modal-body">
                            <div class="select-mould select-Module">
                                <ul class="list-group" *ngIf="mouldExists">
                                    <li *ngFor="let mould of availableMoulds" 
                                    class=""
                                    (click)="fetchSettingsByMouldId(mould)">
                                        <div>
                                            <span class="mx-2">{{ mould }}</span>
                                        </div>
                                    </li>
                                </ul>
                                <div *ngIf="!mouldExists"> No Mould Exists </div>
                            </div> 
                        </div>
                    </div>
                    </div>
                </div> -->
                
                <div ></div>
            </div>
        </div>
    </div>
</section>