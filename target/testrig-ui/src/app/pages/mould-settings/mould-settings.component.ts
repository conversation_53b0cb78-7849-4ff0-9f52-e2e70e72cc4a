import Notiflix from 'notiflix';
import { forkJoin } from 'rxjs';
import Stepper from 'bs-stepper';
import { FormsModule } from '@angular/forms';
import { round } from '../../utils/calculate';
import { processing } from '../../utils/enums';
import { CommonModule } from '@angular/common';
import { Mould } from '../../models/mould.model';
import { MouldService } from '../../services/mould.service';
import { float, int } from '@zxing/library/esm/customTypings';
import { TranslatePipe, TranslateService } from '../../translate';
import { NgxSliderModule, Options } from '@angular-slider/ngx-slider';
import { AfterViewInit, ChangeDetectorRef, Component, forwardRef } from '@angular/core';
import { defaultSchemaConfigurations as defaultSchema } from './dummyStepperdata';
import { Router } from '@angular/router';
import { MachineService } from '../../services/machine.service';

enum ButtonAction { 
  SAVE = 'save',
  UPDATE = 'update'
}

@Component({
  selector: 'app-config',
  standalone: true,
  imports: [CommonModule, NgxSliderModule, FormsModule, TranslatePipe],
  templateUrl: './mould-settings.component.html',
  styleUrl: './mould-settings.component.scss',
  providers: [{ provide: MouldSettingsComponent, useExisting: forwardRef(() => MouldSettingsComponent) }],
})
export class MouldSettingsComponent implements AfterViewInit{
  mouldSelected:any = null // To get the mould name if selected 
  filter = {
    'statusRunning': false,
    'pendingButton': true
  }
  status:any = {  }

  button = {
    mould: 'Save',
    newMould: 'New',
    editMould: 'Edit',
    deleteMould: 'Delete'
  }
  // dynamic options for dropdowns channel name, hydraulic diameter and mouold names 
  public dynamicDropdowns:any = {   
    channelNameDropdown: [],
    diameterDropdown: [],
    mouldNameDropdown: []
  };

  mouldExists: boolean = false 
  availableMoulds: string[]
  buttonCount: number[] = [1]
  sliceStartEnd: any[] = [
    {
        "firstStart": 0,
        "firstEnd": 6,
        "secondStart": 6,
        "secondEnd": 12
    }
  ]
  btuttonAction = ButtonAction // Enum for button actions save/update 
  settingSaved: boolean = false; // To store value for settings saved or not
  isCommentSidebarVisible: boolean = false; // To store value for side bar container
  testSaved: boolean = false; // To store value for test saved or not
  commentText: string = ''; // To store value for comment text
  clickedRun: number = 1
  pressureSetRange: number[] = Array.from({ length: 5001 }, (_, i) => Number((i * 0.01).toFixed(2)));
  selectedPressureValue: Number
  selectedTempValue: Number
  value: float = 40.0;
  highValue: float = 60.0;
  options: Options = {
    floor: 0,
    ceil: 0,
    step: 0.1,
  };
  stepperData: Mould  // To store the values for mould configs in form of json
  stepper: Stepper | undefined;
  channelsDropdown: any[] = []
  
  ngAfterViewInit(): void {
  }
  lowerSensor:boolean
  constructor(private readonly mouldService: MouldService,private machineService:MachineService, private translate: TranslateService,private ref:ChangeDetectorRef, private route:Router ) {
    this.newMouldSchema()    
    this.checkMouldStatus()
    this.getCleaningStatus()
    this.checkFilterCleaning()
    this.machineService.globalShare.subscribe((data) => {
      if (data['lowerSensor'] !== undefined) {
        this.lowerSensor = data['lowerSensor'];
      }
    })
  }
  range(start: number, end: number): number[] {
    return Array.from({ length: end - start + 1 }, (_, i) => start + i);
  }
  nextStep(): void {
    this.stepper?.next();
  }

  previousStep(): void {
    this.stepper?.previous();
  }

  /**
     * get the filtr cleaning status 
     */
  getCleaningStatus(){
    this.mouldService.getCleaningStatus().subscribe({
      next: (response) => {
        if(response){
          if(response.state == 'idle'){
            this.status['hotClean'] = false
            this.status['coldClean'] = false
          }
          else{
            let responseArray = response.state.split(':')
            if(responseArray.includes('idle') || responseArray.includes('completed')){
              this.status['hotClean'] = false
              this.status['coldClean'] = false
            }else{
              this.status['coldClean'] = responseArray[0] == 'coldclean'? true: false
              this.status['hotClean'] = responseArray[0] == 'coldclean'? false: true
            }
          }
        }
      },
      error: (err) => {
        console.log('✌️err --->', err);
      }
    })
   

  }
  checkFilterCleaning(){
    setTimeout(()=>{
      this.mouldService.getAutoFilterStatus().subscribe({
        next: (data) => {
          if(data && data.state){
            
            if(data.state.includes('running')){
              this.filter.statusRunning = true
            }else{
              this.filter.statusRunning = false
            }
            setTimeout(() => {
              this.checkFilterCleaning()
            }, 10000);
          }
        }})
    },2000)
  }
  
  /**
   * To start and stop hot and cold cleaning
   */
  cleaningStartStop(cleaningType: string, start:boolean){
    if(cleaningType == 'hotClean'){
      const hotClean = this.status['hotClean']? 'stop':'hotcleanstart'
      this.status['hotClean'] = !this.status['hotClean']
      this.mouldService.cleaningStartStop(hotClean).subscribe({
        next: (response) => {
          const alertMsg = this.status['hotClean']? 'Cleaning started successfully!': 'Cleaning stopped successfully!'
          Notiflix.Notify.success(this.translate.instant(alertMsg));
        },
        error: (err) => {
          this.status['hotClean'] = !this.status['hotClean']
          console.log('✌️err --->', err);
        }
      })
    }else if(cleaningType == 'coldClean'){
      const hotClean = this.status['coldClean']? 'stop':'coldcleanstart'
      this.status['coldClean'] = !this.status['coldClean']
      this.mouldService.cleaningStartStop(hotClean).subscribe({
        next: (response) => {
          const alertMsg = this.status['coldClean']? 'Cleaning started successfully!': 'Cleaning stopped successfully!'
          Notiflix.Notify.success(this.translate.instant(alertMsg));
        },
        error: (err) => {
          this.status['coldClean'] = !this.status['coldClean']
          console.log('✌️err --->', err);
        }
      })
    }
  }
 
  
  /**
   * To start and stop auto filter cleaning
   */
  autoFilterStop(){
    const filterStatsus = this.filter.statusRunning? 'stop': 'start'
    this.filter.statusRunning = !this.filter.statusRunning
    this.mouldService.autoFilterCleaningStartStop(filterStatsus).subscribe({
      next: (response) => {
        const alertMsg = this.filter.statusRunning? 'Auto Filter Cleaning Started!': 'Auto Filter Cleaning Stopped!'
        localStorage.setItem('filterRunningStatus', this.filter.statusRunning? 'true': 'false')
        Notiflix.Notify.success(this.translate.instant(alertMsg));
      },
      error: (error) => {
        this.filter.statusRunning = !this.filter.statusRunning
        const alertMsg = this.filter.statusRunning? 'Failed to start Auto Filter Cleaning!': 'Failed to stop Auto Filter Cleaning!'
        Notiflix.Notify.failure(this.translate.instant(alertMsg));
    }
    })
  }

  /**
   * Set the current value on slider
   * @param midValue 
   * @param options 
   * @returns 
   */
  public midValuePosition(midValue: any, options: Options): Number {
    if(midValue==`> ${options.ceil}`){
      return 99.5;
    }else{
      return ((midValue - 0) / (100 - 0)) * 100;
    }
  }

  /**
   * It set options for channels, diameter and mouldname dropdown
   */
  getDropDownValues(){
    forkJoin({
      mouldOptions: this.mouldService.listMouldWithData(),
      channelOptions: this.mouldService.listChannelOpitions(),
      diameterOptions: this.mouldService.listDiameterOpitions()
    }).subscribe({
      next: ({
        mouldOptions, channelOptions, diameterOptions
      }) =>{
        if(mouldOptions){
          if(!this.mouldSelected){
            const storedMould = localStorage.getItem('selectedMould');
            mouldOptions.data.filter((mould: any) => {
              if(mould['Name'] == storedMould){
                this.mouldSelected = mould['Name']
              }
            })
            // this.mouldSelected = storedMould ? JSON.parse(storedMould) : null;
          }
          this.dynamicDropdowns.mouldNameDropdown = mouldOptions['data']
          const mouldSelected = localStorage.getItem('selectedMould');
          if(mouldSelected){
            this.mouldSelected = JSON.parse(mouldSelected);
          }
          this.stepperData.mouldid = this.mouldSelected?this.mouldSelected: mouldOptions['data'][0].Name
          // this.ref.detectChanges()
          if(!this.mouldSelected){
            this.mouldSelected = mouldOptions['data'][0].Name
          }
          this.fetchSettingsByMouldId(this.mouldSelected)
        }
        if(channelOptions)
          this.dynamicDropdowns.channelNameDropdown = channelOptions["channel-names"]
        if(diameterOptions)
          this.dynamicDropdowns.diameterDropdown = diameterOptions["hydraulic-diameters"]

        this.stepperData?.channels.forEach((channel, index) => {
          if(channel.channel_name == ''){ 
            channel.channel_diameter = this.dynamicDropdowns.diameterDropdown[0]
            channel.channel_name = this.dynamicDropdowns.channelNameDropdown[0]
          }
        })
      },
      error: (err) => {
        console.error('Error fetching dropdown values:', err);
      }
    })
  }

  /**
   * Set value for enable all based on that specific run 
   * Set true if all channels are enabled
   */
  newMouldSchema(){
    this.getDropDownValues()
    // if(this.stepperData){
      // this.defaultSelected = false
      this.stepperData = defaultSchema

      this.stepperData.mouldid = ''
      if(this.mouldSelected){
        this.stepperData.mouldid = this.mouldSelected
      }
      this.stepperData.channels = []

      for(let index=0; index <12; index++)
      {
        let data = {
          "channel_id": index + 1,
          "channel_name": '',
          "channel_enabled": false,
          "channel_diameter": '',
          "channel_lower_limit": this.flowDefaultLimit(index +1, 'min'),
          "channel_upper_limit": this.flowDefaultLimit(index +1, 'max'),
        }
        this.stepperData?.channels.push(data)
      } 
    this.stepperButtonCount(this.stepperData.channels.length)
  }

  // Show and hide side bar for comment section
  toggleCommentSideBar(): void {
    this.isCommentSidebarVisible = !this.isCommentSidebarVisible;
  }

  /**
   * verify that current run is last run and number of runs is more then 1 
   * @param runId id of the current run
   * @param runLength total sidze of run
   * @returns class to show cross button  
   */
  checkLastRun(runId:int){
    return this.clickedRun == runId ? ' active' : ''  // Set color blue if tab is selected
  }

  /**
   * Change tabs of the runs based on the button action previous/next 
   * @param buttonAction  previous/next 
   */
  onTabChange(buttonAction: string){
    if (buttonAction === 'next'){
      this.clickedRun = this.clickedRun == this.buttonCount.length ? this.clickedRun :this.clickedRun + 1
    } else {
      this.clickedRun = this.clickedRun == 1 ? this.clickedRun :this.clickedRun - 1
    }
  }

  /**
   * Update the schema of new settings to the backend based on the button action
   * @param btnAction  save/cancel
   */
  saveBtnUpdateSchema(btnAction: string){
    const date = new Date();
    const formattedDate = date.toISOString().slice(0, 19).replace("T", " ");
    this.stepperData.time = formattedDate
    const that = this
    Notiflix.Confirm.show(
      that.translate.instant('Confirm'),
      that.translate.instant(`Do you want to ${btnAction} mould settings?`),
      that.translate.instant('Yes'),
      that.translate.instant('No'),
      function okCb() {
        that.button.mould = processing.processing
        if (btnAction === ButtonAction.SAVE){
          delete that.stepperData?.enable_all
          // Save the schema to the backend
          that.stepperData.mouldid = that.mouldSelected
          that.mouldService.insertMouldSettings(that.stepperData).subscribe((response) => { 
            that.settingSaved = true
            that.button.mould = 'Update'
            that.testSaved = true
            Notiflix.Notify.success(that.translate.instant('Mould settings saved successfully'));
          }, err =>{
            that.button.mould = 'Save'
            Notiflix.Notify.failure(that.translate.instant('Failed to save mould settings'));
          });
        }
        if(btnAction === ButtonAction.UPDATE){
          delete that.stepperData?.enable_all
          const mouldNameTemp = that.stepperData.mouldid
          let newStepperData: any = that.stepperData
          delete newStepperData.mouldid

          that.mouldService.updateMouldSettings(mouldNameTemp, newStepperData).subscribe((response) => {
            that.testSaved = true
            that.button.mould = 'Update'
            Notiflix.Notify.success(that.translate.instant('Mould settings updated successfully'));
            that.stepperData.mouldid = that.mouldSelected
          }, err =>{
            that.button.mould = 'Update'
            Notiflix.Notify.failure(that.translate.instant('Failed to update mould settings'));
          })
        }
      },
      function cancelCb() { }
    );
  }

  /**
   * Update the schema of the new settings based on the element and input
   * @param element element to update 
   * @param input input value to update
   * @param data   
   */
  updateRunElement(element:string, input: any, data?: any){
    this.testSaved = false
    if(element === 'slider'){
      this.stepperData.channels.forEach(run => {
        if(run.channel_id == input.channel_id){
          run.channel_lower_limit = input.channel_lower_limit
          run.channel_upper_limit = input.channel_upper_limit
        }
      })
    }
    else if(element === 'channel-enable'){
      const isChecked = (input.target as HTMLInputElement).checked;
      this.stepperData.channels.forEach(run => {
        if(run.channel_id === data.channel_id)
          run.channel_enabled = isChecked
      })
    }
    else if(element == 'enable-all'){
      const isChecked = (input.target as HTMLInputElement).checked;
      this.stepperData.enable_all = isChecked
      this.stepperData.channels.forEach(run => {
        run.channel_enabled = isChecked
      })
    }
  }

  /**
   * List of availabel moulds in stogae 
   */
  getAvailableMoulds(): void{
    this.mouldService.listMouldids().subscribe((data) => {
      this.settingSaved = true
      this.testSaved = true
      if(data.mouldids.length){
        this.mouldExists = true
        this.availableMoulds = data.mouldids
      }else
      this.mouldExists = false
    }, (err)=>{
      this.mouldExists = false
    })
  }

  /**
   * Getting saved configuration of mould based on mould-id
   * @param mouldId 
   */
  fetchSettingsByMouldId(event: Event | string): void{
    
    if (!event) {
      Notiflix.Notify.failure(this.translate.instant('Selected value is null'));
      return;
    }
    
    let mouldId: string;
    if (event instanceof Event) {
      const selectedOption = event.target as HTMLSelectElement;
      mouldId = selectedOption?.value;
      this.stepperData.mouldid = mouldId
    } else {
      mouldId = event;
    }

    this.mouldService.fetchMouldSettings(mouldId).subscribe({
      next: (record: any) => {
        this.button.mould = 'Update'
        // this.defaultSelected = false;
        localStorage.setItem('selectedMould', JSON.stringify(mouldId));
        
        this.stepperData = record;
        this.stepperButtonCount(this.stepperData.channels.length)
        // this.ref.detectChanges()

      },
      error: (err: any) => {
        if(err.error.detail == '404: Record not found.'){
          // this.mouldSelected = mouldId
          // Notiflix.Notify.failure(this.translate.instant('No settings found for') + ` ${mouldId}`);
          // this.stepperButtonCount()
          this.button.mould = 'Save'
          this.stepperData = defaultSchema
        } 
      }
    });
  }

  /**
   * Remove the mould settings to default if any selecetd or loaded
   */
  removeLoad(){
    // this.defaultSelected = true
    localStorage.removeItem('selectedMould')
    this.settingSaved = false
    this.button.mould = 'Save'
    this.newMouldSchema()
  }

  /**
   * clear API call on destroy if required
   */
  ngOnDestroy() { }

  /**
   * Set flow boundries/scales based on channel number
   * @param channel_id 
   * @returns 
   */
  setFlowOptions(channel_id: number): Options{
    if(channel_id < 3){
      this.options.floor = 1
      this.options.ceil = 20
    }
    else if(channel_id < 11){
      this.options.floor = 2
      this.options.ceil = 40
    }
    else{
      this.options.floor = 20
      this.options.ceil = 400
    }
    return this.options 
  }

  /**
   * To set the flow bouldry based on channel number
   * @param channel 
   * @returns 
   */
  flowDefaultLimit(channel: number, boundry: string): number{
    if(channel < 3){
      return boundry == 'min'? 1:20
    }
    else if(channel < 11){
      return boundry == 'min'? 2:40
    }
    else{
      return boundry == 'min'? 20:400
    }
  }

  /**
   * checked all and unchecked all tests which check all checkbox clicked 
   * @param checkbox 
   * @param input 
   * @returns 
   */
  selectTest(checkbox: string, input: Event){
    const isChecked = (input.target as HTMLInputElement).checked;
    if(checkbox == 'all'){
      Object.keys(this.stepperData.tests).forEach(key => {
        this.stepperData.tests[key as keyof typeof this.stepperData.tests] = isChecked;
      });
      return
    }
    this.stepperData.tests[checkbox as keyof typeof this.stepperData.tests] = isChecked
  }

  /**
   * Increment in range min and max values
   * @param key 
   * @param currentVal 
   * @returns 
   */
  incrementRange(key: string, currentVal: number) {
    // Prevent incrementing if the current value is out of the allowed range (0 to 8)
    if (currentVal >= 9) return;
  
    // Switch case to handle different keys efficiently
    switch (key) {
      case 'minValue':
        this.stepperData.pressure_set_point_min = round(this.stepperData.pressure_set_point_min + 0.1);
        break;
        
      case 'maxValue':
        this.stepperData.pressure_set_point_max = round(this.stepperData.pressure_set_point_max + 0.1);
        break;
  
    }
  }
  
  /**
   * Decrement in range min/max value
   * @param key 
   * @param currentVal 
   * @returns 
   */
  decrementRange(key: string, currentVal: number) {
    // Prevent decrementing if the current value is out of the allowed range (0 to 9)
    if (currentVal <= 0) return;
  
    // Switch case to handle different keys efficiently
    switch (key) {
      case 'minValue':
        this.stepperData.pressure_set_point_min = round(this.stepperData.pressure_set_point_min - 0.1);
        break;
        
      case 'maxValue':
        this.stepperData.pressure_set_point_max = round(this.stepperData.pressure_set_point_max - 0.1);
        break;
    }
  }

  /**
   * Set the size/count of steps based on number of channels
   */
  stepperButtonCount(totalChannels: number){
    let start = 0
    const totalChannelCount = totalChannels
    this.buttonCount = Array.from({ length:(Math.ceil( totalChannelCount / 12))}, (_, i) => i + 1);
    this.sliceStartEnd = []
    this.buttonCount.forEach((_, index) => {
      if(index == this.buttonCount.length - 1){
        this.sliceStartEnd.push({
          firstStart: start,
          firstEnd: Math.ceil(start + (( totalChannelCount - start)/2)),
          secondStart:  Math.ceil(start + (( totalChannelCount - start)/2)),
          secondEnd: totalChannelCount
        })
        return
      }
      this.sliceStartEnd.push({
        firstStart: start,
        firstEnd: Math.ceil(start + (((12 + start) - start)/2)),
        secondStart: Math.ceil(start + (((12 + start) - start)/2)),
        secondEnd: start + 12
      })
      start = start + 12
    });
  }

  /**
   * Add new channel in the settings as needed and manage/set button count or step count based on nuber o f channels
   */
  manageChannel(action: 'add' | 'remove' ){
    if(action == 'add'){
      this.stepperData.channels.push({
        "channel_id": this.stepperData.channels.length + 1,
        "channel_name": '',
        "channel_enabled": false,
        "channel_diameter": '',
        "channel_lower_limit": this.flowDefaultLimit(this.stepperData.channels.length + 1, 'min'),
        "channel_upper_limit": this.flowDefaultLimit(this.stepperData.channels.length + 1, 'max'),
      })
      this.stepperData.channels[this.stepperData.channels.length-1].channel_diameter = this.dynamicDropdowns.diameterDropdown[0]
      this.stepperData.channels[this.stepperData.channels.length-1].channel_name = this.dynamicDropdowns.channelNameDropdown[0]
      if (this.sliceStartEnd[this.clickedRun - 1].secondEnd % 12 === 0) {
        this.clickedRun++
      }
    } else if (action == 'remove') {
      if(this.sliceStartEnd[this.clickedRun - 1].secondStart == this.sliceStartEnd[this.clickedRun - 1].secondEnd){
        this.sliceStartEnd.pop()
        this.clickedRun--
      }
      this.stepperData.channels.pop();
    }

    this.stepperButtonCount(this.stepperData.channels.length)
    // Set default channel name and diameter
  }

  checkMouldStatus(){
    if(this.mouldService.mouldTestStatus?.state.includes('verifying')){
      // setTimeout(() => {
      //   console.log('✌️verifying --->');
      //   this.checkMouldStatus()
      // }, 1000);
    }
    if(this.mouldSelected ){
      this.mouldSelected = this.mouldService.mouldTestStatus?.state.split(':')[0]
      this.stepperData.mouldid = this.mouldSelected
      this.fetchSettingsByMouldId(this.mouldSelected)
    }
  }

  /**
   * increment and decrement in max water pressure range(0-9)
   * @param key 
   * @returns 
   */
  maxPressureChange(key: string){
    if(key == 'minValue'){
      if(this.stepperData.max_water_pressure <1) return
      this.stepperData.max_water_pressure = this.stepperData.max_water_pressure - 1
    }else{
      if(this.stepperData.max_water_pressure >8) return
      this.stepperData.max_water_pressure = this.stepperData.max_water_pressure + 1
    }
  }

  /**
   * To delete the selected mould using order number
   */
  deleteMould(){
    this.button.deleteMould = 'Processing...'
    let that = this
    Notiflix.Confirm.show(
      that.translate.instant('Confirm'),
      that.translate.instant(`Are you sure you want to delete mould "${that.mouldSelected}"?`),
      that.translate.instant('Yes'),
      that.translate.instant('No'),
      function okCb() {
        console.log('✌️this.dynamicDropdowns.mouldNameDropdown  --->', that.dynamicDropdowns.mouldNameDropdown );
        // Delete mould from Excel
        that.dynamicDropdowns.mouldNameDropdown.forEach((mould: any) => {
          if(mould['Name'] == that.mouldSelected){
            const orderNumber = mould['Order Number']
            that.mouldService.deleteCurrentMould(orderNumber).subscribe({
              next: (result)=>{
                console.log('✌️result --->', result);
                that.button.deleteMould = 'Delete'
                Notiflix.Notify.success(that.translate.instant('Mould deleted successfully!'));
                setTimeout(() => {
                  window.location.reload()
                }, 1000);
              },
              error: (err)=>{
                  Notiflix.Notify.failure(that.translate.instant('Failed to delete mould!'));
                console.log('✌️err --->', err);
                that.button.deleteMould = 'Delete'
              }
            })
          }
        })
        // Delete the mould from the database
        that.mouldService.deleteMouldFromDB(that.mouldSelected).subscribe({
          next: (result) => {},
          error: (err) => {
            console.log('✌️err --->', err);
            Notiflix.Notify.failure(that.translate.instant('Failed to delete mould!'));
          }
        })
      },
      function cancelCb() { 
        that.button.deleteMould = 'Delete'
      }
    );
  }
  
  /**
   * To check that test is in running state or not
   */
  // public get testStatus(): boolean {
  //   return this.mouldService.mouldTestStatus?.state.includes('idle') ? false : true
  // }

}
