<section class="app-wrapper">
    <div class="row mx-5" *ngIf="action == ''">
        <div class="col-md">
            <div class="bg-white rounded-8 p20 p2p" (click)="selectedAction('test')">
                <div class="iconbox">
                    <img src="assets/images/testMould.png" height="55px" alt="setting"/>   
                </div>
                <h3>{{"Mould Test"|translate}}</h3>
                <button class="arrow-icon">
                    <img src="assets/images/arrow_right_alt.svg" alt="arrow right"/>   
                </button>
            </div>
        </div>
        <div class="col-md mx-5">
            <div class="bg-white rounded-8 p20 p2p" (click)="selectedAction('report')">
                <div class="iconbox">
                    <img src="assets/images/report.png" height="55px" alt="pump"/>   
                </div>
                <h3>{{"Mould Report" | translate}}</h3>
                <button class="arrow-icon">
                    <img src="assets/images/arrow_right_alt.svg" alt="arrow right"/>   
                </button>
            </div>
        </div>
    </div>
    <div class="test-container-card" *ngIf="action != ''">
            <!-- Select Mould  -->
            <div class="card-heading d-flex align-items-center justify-content-between">
                <div class="d-flex d-flex align-items-center">
                    <span class="cursor-pointer backArrow" (click)="selectedAction('')"> 
                        <img src="assets/images/arrow_right_alt1.svg" alt="arrow right"/>   
                    </span>
                    <h4 class="mx-3"> {{"Select Mould"|translate}} 
                        <span *ngIf="action == 'test'">{{"for Test"|translate}}</span> 
                        <span *ngIf="action != 'test'">{{"for Report"|translate}}</span>
                    </h4>
                </div>
                <div class="" >
                    <button *ngIf="mouldAvailabel" class="btn btn-primary" [disabled]="btnStatus =='Processing'" (click)="testSelectedMould()">{{ btnStatus |translate }}</button>
                </div>
            </div>
            <!-- <div class="text-center opacity-75 processing w-100 d-flex align-items-center justify-content-center" *ngIf="mouldAvailabel == undefined">
                Processing...
            </div> -->
            <div class="select-Module ">

                <!-- Skeleton loader for Mould buttons -->
                <div class="" *ngIf="mouldAvailabel == undefined">
                    <div *ngFor="let skel of [1, 2]" class="mb-2">
                        <div class="placeholder-wave">
                            <div class="placeholder col-12 rounded py-3 px-4"></div>
                        </div>
                    </div>
                </div>

                <div class="justify-content-center opacity-75 d-flex text-center" *ngIf="mouldAvailabel == false">
                    No Mould to test.
                </div>
                <!-- <ul class="list-group" *ngIf="mouldAvailabel"> -->
                <ul class="list-group">
                    <li *ngFor="let mould of moulds" 
                    class=""
                    [class.selected]="selectedMould === mould"
                    (click)="selectMould(mould)">
                        <div>
                            <span class="mx-2">{{ mould }}</span>
                        </div>
                        <!-- <div>
                            <span>{{ mould }}</span>
                        </div>  -->
                    </li>
                </ul>
            </div> 
    </div>
</section>