
<section class="app-wrapper ">
    <!-- <div class="justify-content-center align-items-center d-flex text-center" *ngIf="!stepperData.mouldid">
        Processing...
    </div> -->
    <div class="row">
        <!-- <div class="row" *ngIf="stepperData.mouldid"> -->
        <!-- Mould Name -->
        <div class="col-md" >
            <div class="bg-white rounded-8 h-equal" style="position: relative;">
                <!-- Heading -->
                <div class="card-heading w-100 border-0 d-flex " > 
                    <!-- <div (click)="testScreen()">back</div>   -->
                    <div class="d-flex align-items-center">
                            <span class="cursor-pointer backArrow" *ngIf="btnStatus != 'Stop Test'" (click)="backToTest()" > 
                                <img src="assets/images/arrow_right_alt1.svg" alt="arrow right"/>   
                            </span>
                            <!-- Skeleton header -->
                            <div *ngIf="!stepperData.mouldid" class="skeleton-bg placeholder-wave placeholder ms-2" style="width: 150px; height: 40px;"></div>

                            <h4 class="mx-3 " *ngIf="!testStatus"> 
                                {{ stepperData.mouldid }} 
                                <span *ngIf="stepperData.mouldid" class="">|</span> 
                            </h4> 
                            {{ convertGMTtoFrenchTime(stepperData.time) }}  
                    </div>
                    <div class="mx-5" *ngIf="stepperData.mouldid">
                        <button class="btn btn-danger" *ngIf="btnStatus == 'Stop Test'" (click)="startStopTest('Stop Test'); stopTriggered = true; ">{{ btnStatus | translate}}</button>
                        <button class="btn btn-success" *ngIf="!stopTriggered && btnStatus == 'Start Test' && this.startBtnClick" (click)="viewReport()">{{ "View Report"| translate}}</button>
                    </div>
                </div>
                <!-- skeleton loader for table of enabled channels -->
                <table class="table  skeleton-table m-3" *ngIf="!stepperData.mouldid">
                    <thead>
                      <tr class="text-center">
                        <th>
                            <div class="placeholder-wave mb-1">
                                <span class="placeholder col-8 bg-secondary rounded"></span>
                            </div>
                        </th>
                      </tr>
                    </thead>
                    <tbody>
                      <tr *ngFor="let skel of [1,2,3,4,5,6,7,8,9,10]" class="text-center">
                        <td>
                            <div class="placeholder-wave mb-1">
                                <span class="placeholder col-8 bg-secondary rounded"></span>
                            </div>    
                        </td>
                      </tr>
                    </tbody>
                </table>

                <section *ngIf="stepperData.mouldid && !testStatus" class="selected-channels">
                <!-- <section> -->
                    <!-- Mould Tests Component -->
                    <div class="channel-lists">
                    <div class="row channel-details channel-details-head">
                        <div class="col">
                            <strong> {{"Channel Id"|translate}} </strong>
                        </div>
                        <div class="col">
                            <strong> {{"Channel Name"|translate}} </strong>
                        </div>
                        <div class="col">
                            <strong> {{"Hydraulic Diameter"|translate}} </strong>
                        </div>
                    </div>
                    <div *ngFor="let channel of this.stepperData.channels">
                        <div class="row channel-details" *ngIf="channel.channel_enabled">
                            <div class="col">
                                <div class="backArrow">
                                    <strong style="color: #0990D2;"> {{ channel.channel_id }} </strong>
                                </div>
                            </div>
                            <div class="col">
                                <span  [ngClass]="channel.channel_name != '' && channel.channel_name? '': ' text-danger' "> 
                                    {{ channel.channel_name != '' && channel.channel_name? channel.channel_name : 'None' }} 
                                </span>
                            </div>
                            <div class="col">
                                <span [ngClass]="channel.channel_diameter != '' && channel.channel_diameter? '': ' text-danger'" class="text-faded"> 
                                    {{ channel.channel_diameter != '' && channel.channel_diameter? channel.channel_diameter: 'None' }} 
                                </span>
                            </div>
                        </div>
                    </div>
                    <!-- <div class="border-2 m-3 d-block text-center"> -->
                    </div>
                
                    <!-- </div> -->
                    <div class="w-100 mt-auto d-flex justify-content-between test-module bottom-0" >
                        
                        <div>
                            <div>
                                <strong>{{"Available Tests"|translate}} : </strong>
                            </div>
                            <div class="gap-3 d-flex align-items-center">
                                <div class="d-flex gap-4">
                                    <label class="form-check tests">
                                        <input class="form-check-input test-checkbox" type="checkbox" id="allTestCheckbox" 
                                        (change)="selectTest('all', $event)"
                                        [checked]="stepperData.tests['water_leak_test'] && 
                                                    stepperData.tests['air_leak_test'] &&
                                                    stepperData.tests['flow_test'] &&
                                                    stepperData.tests['heat_test']">
                                        <div class="test-inner">
                                            <img src="assets/images/mark.png" />
                                            <span class="form-check-label" >{{"All Tests"|translate}}</span>
                                        </div>
                                    </label>
                                    <label class="form-check tests">
                                        <input class="form-check-input test-checkbox" type="checkbox" id="flowTestCheckbox"
                                        [checked]="stepperData.tests['flow_test']" (change)="stepperData.tests['flow_test'] = !stepperData.tests['flow_test']">
                                        <div class="test-inner">
                                            <img src="assets/images/flow-test.png" />
                                            <span class="form-check-label" >{{"Flow Test"|translate}}</span>
                                        </div>
                                    </label>
                                    <label class="form-check tests">
                                        <input class="form-check-input test-checkbox" type="checkbox" id="airLeakTestCheckbox"
                                        [checked]="stepperData.tests['air_leak_test']" (change)="stepperData.tests['air_leak_test'] = !stepperData.tests['air_leak_test']">
                                        <div class="test-inner">
                                            <img src="assets/images/air-leak-test.png" />
                                            <span class="form-check-label" >{{"Air Leak Test"|translate}}</span>
                                        </div>
                                    </label>
                                    <label class="form-check tests">
                                        <input class="form-check-input test-checkbox" type="checkbox" id="waterLeakTestCheckbox"
                                        [checked]="stepperData.tests['water_leak_test']" (change)="stepperData.tests['water_leak_test'] = !stepperData.tests['water_leak_test']">
                                        <div class="test-inner">
                                            <img src="assets/images/water-leak-test.png" />
                                            <span class="form-check-label" for="waterLeakTestCheckbox">{{"Water Leak Test"|translate}}</span>
                                        </div>
                                    </label>
                                    <label class="form-check tests">
                                        <input class="form-check-input test-checkbox" type="checkbox" id="heatTestCheckbox"
                                        [checked]="stepperData.tests['heat_test']" (change)="stepperData.tests['heat_test'] = !stepperData.tests['heat_test']"> 
                                        <div class="test-inner">
                                            <img src="assets/images/heat-test.png" />
                                            <span class="form-check-label" for="heatTestCheckbox">{{"Heat Test"|translate}}</span>
                                        </div>
                                    </label>
                                </div>
                            </div>
                        </div>
                        <button class="btn btn-primary my-auto" [disabled]="btnStatus == 'Processing'" (click)="startStopTest(btnStatus)">{{ btnStatus |translate}}</button>
                    </div>
                </section>   
                <div *ngIf="stepperData.mouldid">
                    <section *ngIf="testStatus">
                        <app-status-tests  
                        [tests]="stepperData.tests"
                        [stepperData]="stepperData" >
                        </app-status-tests>
                    </section>    
                </div>
                
                <!-- Report of selected Mould -->
                     
            </div>

    </div>

        <!-- Preview Details -->
        
    </div>
</section>