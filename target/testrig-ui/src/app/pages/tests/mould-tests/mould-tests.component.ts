import Notiflix from 'notiflix';
import { concatMap } from 'rxjs';
import { FormsModule } from '@angular/forms';
import { CommonModule } from '@angular/common';
import { Component, OnDestroy } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { ApiService } from '../../../services/api.service';
import { MouldService } from '../../../services/mould.service';
import { MachineService } from '../../../services/machine.service';
import { TranslatePipe, TranslateService } from '../../../translate';
import { convertGMTtoFrenchTime } from '../../../utils/convert-fr-datetime';
import { StatusTestsComponent } from '../status-tests/status-tests.component';

@Component({
  selector: 'app-mould-tests',
  standalone: true,
  imports: [CommonModule, FormsModule, StatusTestsComponent, TranslatePipe],
  templateUrl: './mould-tests.component.html',
  styleUrl: './mould-tests.component.scss'
})
export class MouldTestsComponent implements OnDestroy{
  btnStatus:string = 'Start Test'
  activeTab: string = 'previous';
  stepperData :any = {}
  currectMould :string = ''
  stopTriggered: boolean = false
  convertGMTtoFrenchTime = convertGMTtoFrenchTime
  constructor(private mouldService: MouldService,private machineService:MachineService, private router: Router, private route: ActivatedRoute, private apiService: ApiService, private translate: TranslateService){
    this.route.paramMap.subscribe(params => {
      const mouldId = params.get('mould-id');
      if (mouldId) {
        this.currectMould = mouldId
        this.selectedMouldDetails(mouldId);
        this.machineService.selectedAction = 'test'
      } else {
        Notiflix.Notify.failure(this.translate.instant("No mould selected"));
        this.router.navigate(['/tests']);
      }
    });
    this.machineService.globalShare.subscribe((data)=>{
      if(data.test == 'running'){
        this.startBtnClick = true
      }
    })
    this.verifyTestStatus()
  }

  // Back to test screen to change mould to test
  backToTest(){
    this.router.navigate(['/tests'])
  }

  // Switch tabs from previous and recents tests
  switchTab(tab: string) {
    this.activeTab = tab;
  }

  // Details of selected mould 
  selectedMouldDetails(mouldId: string){
    this.mouldService.fetchMouldSettings(mouldId).subscribe({
      next: (mould) => {
        this.stepperData = mould;
      },
      error: (err) => {
        if(this.apiService.baseUrl=='http://127.0.0.1:8000'){
          let mould = {
            "time": "2025-03-19 04:12:35",
            "mouldid": "Mould 1",
            "pressure_set_point_min": 2,
            "pressure_set_point_max": 4,
            "comments": "Mould 1 Configurations",
            "channels": [
              {
                "channel_id": 1,
                "channel_name": "3/4 inch pipe",
                "channel_enabled": true,
                "channel_diameter": "square, 10",
                "channel_lower_limit": 1,
                "channel_upper_limit": 20
              },
              {
                "channel_id": 2,
                "channel_name": "1/2 inch pipe",
                "channel_enabled": true,
                "channel_diameter": "rectangle, 5",
                "channel_lower_limit": 1,
                "channel_upper_limit": 20
              },
              {
                "channel_id": 3,
                "channel_name": "small pipe",
                "channel_enabled": true,
                "channel_diameter": "circle, 10",
                "channel_lower_limit": 2,
                "channel_upper_limit": 40
              },
              {
                "channel_id": 4,
                "channel_name": "big pip",
                "channel_enabled": true,
                "channel_diameter": "rhombus, 7",
                "channel_lower_limit": 2,
                "channel_upper_limit": 40
              },
              {
                "channel_id": 5,
                "channel_name": "mould special pipe",
                "channel_enabled": true,
                "channel_diameter": "circle, 10",
                "channel_lower_limit": 2,
                "channel_upper_limit": 40
              },
              {
                "channel_id": 6,
                "channel_name": "manifold special pipe",
                "channel_enabled": true,
                "channel_diameter": "rectangle, 5",
                "channel_lower_limit": 2,
                "channel_upper_limit": 40
              },
              {
                "channel_id": 7,
                "channel_name": "inlet pipe 1",
                "channel_enabled": true,
                "channel_diameter": "rectangle, 5",
                "channel_lower_limit": 2,
                "channel_upper_limit": 40
              },
              {
                "channel_id": 8,
                "channel_name": "inlet pipe 2",
                "channel_enabled": true,
                "channel_diameter": "rhombus, 7",
                "channel_lower_limit": 2,
                "channel_upper_limit": 40
              },
              {
                "channel_id": 9,
                "channel_name": "inlet pipe 3",
                "channel_enabled": true,
                "channel_diameter": "circle, 10",
                "channel_lower_limit": 2,
                "channel_upper_limit": 40
              },
              {
                "channel_id": 10,
                "channel_name": "inlet pipe 4",
                "channel_enabled": true,
                "channel_diameter": "rectangle, 5",
                "channel_lower_limit": 2,
                "channel_upper_limit": 40
              },
              {
                "channel_id": 11,
                "channel_name": "inlet pipe 5",
                "channel_enabled": true,
                "channel_diameter": "rectangle, 5",
                "channel_lower_limit": 20,
                "channel_upper_limit": 400
              },
              {
                "channel_id": 12,
                "channel_name": "inlet pipe 6",
                "channel_enabled": true,
                "channel_diameter": "rhombus, 7",
                "channel_lower_limit": 20,
                "channel_upper_limit": 400
              }
            ],
            "tests": {
              "flow_test": true,
              "heat_test": false,
              "air_leak_test": false,
              "water_leak_test": false
            }
          }
          this.stepperData = mould;
        }else{
          Notiflix.Notify.failure(this.translate.instant("No mould selected"));
          this.router.navigate(['/tests'])
        }

      }
    });
  }
  
  ngOnDestroy(): void {
    this.machineService.selectedAction = ''
  }

  selectTest(checkbox: string, input: Event){
    const isChecked = (input.target as HTMLInputElement).checked;
    if(checkbox == 'all'){
      Object.keys(this.stepperData.tests).forEach(key => {
        this.stepperData.tests[key as keyof typeof this.stepperData.tests] = isChecked;
      });
      return
    }
    this.stepperData.tests[checkbox as keyof typeof this.stepperData.tests] = isChecked
  }

  /**
   * 
   */
  startBtnClick:boolean = false;
  startStopTest(action: string){
    this.btnStatus = 'Processing'

     // Create a copy of stepperData to avoid mutating the original object
    const newStepperData = { ...this.stepperData }; 

    const mouldId = this.stepperData.mouldid
    delete newStepperData.mouldid
    if(action == 'Stop Test'){
      // const that = this
      // Notiflix.Confirm.show(
      //   that.translate.instant('Confirm'),
      //   that.translate.instant(`Are you sure you want to stop test?`),
      //   that.translate.instant('Yes'),
      //   that.translate.instant('No'),
      //   function okCb() {
      //     that.mouldService.stopTest(mouldId).subscribe({
      //       next: () => {
      //         // Remove an element from localStorage
      //         localStorage.removeItem('pressureTabs');
      //         that.stepperData.mouldid = mouldId;
      //         that.btnStatus = 'Start Test';
      //       },
      //       error: (err) => {
      //         that.btnStatus = 'Stop Test';
      //         Notiflix.Notify.failure(that.translate.instant('Failed to Stop Test'));
      //       }
      //     });
      //   },
      //   function cancelCb() { }
      // );
      this.mouldService.stopTest(mouldId).subscribe({
        next: () => {
          // Remove an element from localStorage
          localStorage.removeItem('pressureTabs');
          this.stepperData.mouldid = mouldId;
          this.btnStatus = 'Start Test';
        },
        error: (err) => {
          this.btnStatus = 'Stop Test';
          Notiflix.Notify.failure(this.translate.instant('Failed to Stop Test'));
        }
      });
      
    }
    if(action == 'Start Test'){
      // Update selected tests in Mould details 
      this.mouldService.updateMouldSettings(mouldId, newStepperData).pipe(
        concatMap(() => this.mouldService.startTest(mouldId)) // Wait for the first API to complete before calling the second
      ).subscribe({
        next: () => {
          this.startBtnClick = true
          this.btnStatus = 'Stop Test';
          // this.stepperData.mouldid = mouldId;
        },
        error: (err) => {
          this.startBtnClick = false
          this.btnStatus = 'Start Test';
          Notiflix.Notify.failure(this.translate.instant('Failed to Start test'));
        }
      });
    }

  }

  
  /**
   * To check the status of running tests that are in progress or not
   * @returns 
   */
  verifyTestStatus(){
    this.mouldService.verifyTestStatus().subscribe({ 
      next: (status) => {
        if(status.state == 'idle'){
          this.btnStatus = 'Start Test'
        }else{
        }
      },
      error: (err) => {
        this.btnStatus = 'Start Test'
      }
    })
  }

  /**
   * To verify that selected mould timestamp and currect time stamp is same or not 
   * @param selectedDate 
   * @param currentDate 
   * @returns 
   */
  verifySelectedTimeStamp(selectedDate:any, currentDate:any){
    if(!this.isEmptyObject(selectedDate)){
      currentDate.start_time = this.formatDateTimeInterval(currentDate.start_time)
      selectedDate.startTime = this.formatDateTimeInterval(selectedDate.startTime)
      currentDate.stop_time = this.formatDateTimeInterval(currentDate.stop_time)
      selectedDate.stopTime = this.formatDateTimeInterval(selectedDate.stopTime)
      if((selectedDate.startTime == currentDate.start_time) && (currentDate.stop_time == selectedDate.stopTime)){
        return true
      }
    }
    return false
  }

  /**
   * To check that test is in running state or not
   */
  public get testStatus(): boolean {
    // return true
    if(this.mouldService.mouldTestStatus?.state.includes('idle'))
      this.btnStatus = this.btnStatus != 'Processing'? 'Start Test' : 'Processing'
    else
      this.btnStatus = this.btnStatus != 'Processing'? 'Stop Test' : 'Processing'
    return this.mouldService.mouldTestStatus?.state.includes('idle') && !this.startBtnClick ? false : true
  }
  
  /**
   * Format the date time to mke it easily readable
   * @param timeStamp 
   * @returns 
   */
  formatDateTimeInterval(timeStamp: any): any{
    const date = new Date(timeStamp);
    const formattedDate = date.getFullYear() + "-" +
    String(date.getMonth() + 1).padStart(2, "0") + "-" +
    String(date.getDate()).padStart(2, "0") + " " +
    String(date.getHours()).padStart(2, "0") + ":" +
    String(date.getMinutes()).padStart(2, "0") + ":" +
    String(date.getSeconds()).padStart(2, "0");

    return formattedDate;
  }

  /**
   * 
   * @param startTime 
   * @param endTime 
   * @returns 
   */
  calculateDuration(startTime: string, endTime: string): string {
    const start = new Date(startTime);
    const end = new Date(endTime);
  
    let diffInSeconds = Math.floor((end.getTime() - start.getTime()) / 1000);
  
    const hours = Math.floor(diffInSeconds / 3600);
    diffInSeconds %= 3600;
    const minutes = Math.floor(diffInSeconds / 60);
    const seconds = diffInSeconds % 60;
  
    return `${String(hours).padStart(2, "0")}:${String(minutes).padStart(2, "0")}:${String(seconds).padStart(2, "0")}`;
  }
  
  isEmptyObject(obj: any): boolean {
    return obj && Object.keys(obj).length === 0 && obj.constructor === Object;
  }

  /**
   * Redirect to report section page 
   */
  viewReport(){
    this.router.navigate(
      ['/tests/report', this.currectMould], 
      { queryParams: { latest: true } }
    );
  }
} 