import Notiflix from 'notiflix';
import { Router } from '@angular/router';
import { FormBuilder } from '@angular/forms';
import { CommonModule } from '@angular/common';
import { Component, OnDestroy, OnInit } from '@angular/core';
import { MouldService } from '../../services/mould.service';
import { ApiService } from '../../services/api.service';
import { MachineService } from '../../services/machine.service';
import { TranslatePipe, TranslateService } from '../../translate';

@Component({
  selector: 'app-tests',
  standalone: true,
  imports: [CommonModule, TranslatePipe],
  templateUrl: './tests.component.html',
  styleUrl: './tests.component.scss'
})
export class TestsComponent implements OnInit, OnDestroy{
  btnStatus: string = 'Show Details'
  mouldAvailabel: boolean
  selectedMoulds: String;
  moulds: string[]; // Dummy data for testing

  selectedMould: any;
  
  constructor(private router: Router, private fb: FormBuilder, private mouldService: MouldService, private apiService: ApiService, private machineService: MachineService, private translate: TranslateService ){
    this.btnStatus = this.action == 'test'? 'Show Details': 'Show Reports'
  } 

  ngOnInit() {
    this.testStatus()
    
    this.mouldService.listMouldids().subscribe((moulds =>{
      if(moulds.mouldids.length){
        this.mouldAvailabel = true
        this.moulds = moulds.mouldids
      }else{
        this.mouldAvailabel = false
      }
    }), (err) =>{
      if(this.apiService.baseUrl=='http://127.0.0.1:8000'){
        let moulds = {
          "mouldids": [
            "Mould 2",
            "Mould 1"
          ]
        }
        if(moulds.mouldids.length){
          this.mouldAvailabel = true
          this.moulds = moulds.mouldids
        }else{
          this.mouldAvailabel = false
        }
      }else{
        this.mouldAvailabel = false
      }
      
    })
  }

  selectedAction(action: string){
    this.btnStatus = action == 'test'? 'Show Details': 'Show Reports'
    this.machineService.selectedAction = action
  }
  get action(){
    return this.machineService.selectedAction
  }

  // Select single element 
  selectMould(id: string): void {
    this.selectedMould = this.selectedMould === id ? null : id;
  }

  checkSelected(id:string){
    return this.selectedMould!=null && this.selectedMould== id ?'selected':''
  }
  
  /**
   * Select the mould and navigate to thpage with selected mould name  
   */
  testSelectedMould(): void{
    this.btnStatus = 'Processing'
    if(this.selectedMould){
      this.btnStatus = this.action == 'test'? 'Show Details': 'Show Reports'
      const navigationRoute = this.action == 'test' ? `/tests/${this.selectedMould}`: `/tests/report/${this.selectedMould}`
      window.location.href = navigationRoute;
    }
    else{
      this.btnStatus = this.action == 'test'? 'Show Details': 'Show Reports'
      Notiflix.Notify.failure(this.translate.instant('Please select a mould to test'));
    }
  }

  ngOnDestroy(): void {
    this.machineService.selectedAction = ''
  }

  /**
   * To check that test is in running state or not
   */
  public testStatus(){
    if(this.mouldService.mouldTestStatus?.state.includes('verifying')){
      setTimeout(() => {
        this.testStatus()
      }, 1000);
      return
    }
    
    if(!this.mouldService.mouldTestStatus?.state.includes('idle')){
      this.selectedMould = this.mouldService.mouldTestStatus?.state.split(':')[0]
      this.router.navigate([`/tests/${this.selectedMould}`]);
    }
  }
}
