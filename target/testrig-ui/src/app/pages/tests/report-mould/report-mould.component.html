<section class="app-wrapper ">
    <div class="row" *ngIf="true">
        <!-- Preview Details -->
        <div class="col-md-5">
            <!-- Preview details -->
            <div class="bg-white rounded-8 h-equal d-flex flex-column">
                <div class="card-heading d-flex justify-content-between border-0">
                    <div class="d-flex align-items-center">
                        <span class="cursor-pointer backArrow" (click)="backToTest()">
                            <img src="assets/images/arrow_right_alt1.svg" alt="arrow right" />
                        </span>
                        <h4 class="mx-3"> {{"Preview details"|translate}} <span class=""></span> </h4>
                    </div>
                    <!-- <button class="print-btn">
                        <img src="assets/images/print.svg" alt="print" />
                        Print All
                    </button> -->
                </div>
                <div class="detail-tabs">
                    <ul class="nav nav-tabs">
                        <li class="nav-item">
                            <a class="nav-link text-dark cursor-pointer" [class.active]="activeTab === 'previous'"
                                (click)="switchTab('previous')">
                                <img src="assets/images/article.svg" alt="close" />
                                {{"All Tests"|translate}}
                            </a>
                        </li>
                        <!-- <li class="nav-item">
                            <a class="nav-link cursor-pointer text-dark" [class.active]="activeTab === 'recent'"
                                (click)="switchTab('recent')">
                                <img src="assets/images/article.svg" alt="close" />
                                Recent Tests
                            </a>
                        </li> -->
                    </ul>
                    <div *ngIf="activeTab === 'recent'"
                        class="w-100 border border-top-0 p-4 overflow-auto delail-inner">
                        <div class="p-0 form-check d-flex align-items-center">
                            <label class="form-check-label hovered-element p-3 w-100 checkbox-style"
                                [for]="'flexCheckDefault'">
                            </label>
                        </div>
                    </div>
                    <div *ngIf="activeTab === 'previous'"
                        class="w-100 border border-top-0 p-4 overflow-auto delail-inner">
                        <div>
                            <h3 class="mb-3 text-center">
                                {{ this.currentMould }}
                            </h3>
                        </div>
                        <div *ngIf="testReport?.recent.length < 1" class=" opacity-75">
                            <div *ngIf="!testReport.data" class="text-center m-5 ">
                                {{ "No Previous test" |translate}}!
                            </div>
                            <!-- Skeleton loader for recent test cases -->
                            <div *ngIf="testReport.data">
                                <div *ngFor="let skel of [1,2,3,4,5,6]"
                                    class="py-1 ps-0 form-check d-flex align-items-center">
                                    <label class="form-check-label p-3 w-100 checkbox-style"
                                        style="background-color: #f0f0f0; border-radius: 8px;">
                                        <div class="d-flex w-100 justify-content-between align-items-center">
                                            <!-- Left (start & stop time) -->
                                            <div class="w-75">
                                                <div class="placeholder-wave mb-1">
                                                    <span class="placeholder col-8 bg-secondary rounded"></span>
                                                </div>
                                                <div class="placeholder-wave">
                                                    <span class="placeholder col-7 bg-secondary rounded"></span>
                                                </div>
                                            </div>
                                            <!-- Right (duration) -->
                                            <div class="w-25 text-end placeholder-wave">
                                                <span class="placeholder col-5 bg-secondary rounded"></span>
                                            </div>
                                        </div>
                                    </label>
                                </div>
                            </div>

                        </div>
                        <div *ngFor="let testCase of testReport.recent; let i = index"
                            class="p-0 form-check d-flex align-items-center">
                            <label class="form-check-label hovered-element p-3 w-100 checkbox-style"
                                (click)="reportOfSelectedMould(testCase.start_time, testCase.stop_time)"
                                [ngClass]="verifySelectedTimeStamp(testReport?.selectedMould, testCase)? ' selected': ''"
                                [for]="'flexCheckDefault' + i">
                                <div class="d-flex w-100 justify-content-between align-items-center">
                                    <div>
                                        <div>{{ convertGMTtoFrenchTime(testCase.start_time) }} </div>
                                        <div>{{ convertGMTtoFrenchTime(testCase.stop_time) }}</div>
                                    </div>
                                    <div class="">
                                        {{ calculateDuration(testCase.start_time, testCase.stop_time) }}
                                    </div>
                                </div>
                            </label>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Mould Name -->
        <div class="col-md-7">
            <div class="bg-white rounded-8 h-equal" style="position: relative;">
                <div class="text-center py-3" >
                    <button class="btn btn-lang mx-2" 
                        [disabled]="isEmptyObject(this.testReport?.selectedMould) || report.processing"
                        [ngClass]="{'btn-active': reportLang === 'fr'}"
                        (click)="reportOfSelectedMould(testReport.selectedMould.startTime,testReport.selectedMould.stopTime,'fr')">
                        French
                    </button>
                    <button class="btn btn-lang mx-2"
                        [disabled]="isEmptyObject(this.testReport?.selectedMould) || report.processing"
                        [ngClass]="{'btn-active': reportLang === 'en'}"
                        (click)="reportOfSelectedMould(testReport.selectedMould.startTime,testReport.selectedMould.stopTime,'en')">
                        English
                    </button>
                </div>
                <!-- Report of selected Mould -->
                <section class="h-100 pb-4 pt-1">
                    <div class=" position-relative logs ">
                        <label class="logs-label" *ngIf="!isEmptyObject(this.testReport.selectedMould)">
                            {{ convertGMTtoFrenchTime(this.testReport.selectedMould.startTime)}} - {{
                            convertGMTtoFrenchTime(this.testReport?.selectedMould.stopTime)}} (
                            {{ calculateDuration( this.testReport?.selectedMould.startTime,
                            this.testReport?.selectedMould.stopTime) }} )
                           
                            <button class="print-btn print-label" (click)="generatePDF()"
                                *ngIf="!isEmptyObject(this.testReport?.selectedMould) && !report.processing"
                                [disabled]="this.report?.printBtn == 'printing..'">
                                <img src="assets/images/print.svg" alt="print" />
                                <span *ngIf="this.report?.printBtn == 'printing..'">
                                    {{"Printing.." | translate:reportLang}}
                                </span>
                                <span *ngIf="this.report?.printBtn != 'printing..'">
                                    {{"Print" | translate:reportLang}}
                                </span>
                                <!-- {{ this.report?.printBtn == 'printing..'? 'Printing..': 'Print'}} -->
                            </button>
                        </label>
                        <label class="logs-label" *ngIf="isEmptyObject(this.testReport?.selectedMould)">
                            {{"Selected Time-Stamp"|translate:reportLang}}
                        </label>
                        <div *ngIf="isEmptyObject(this.testReport?.selectedMould)" class="text-center m-5 opacity-75">
                            {{"No Time-stamp Selected"|translate:reportLang}}!
                        </div>

                        <!-- Skeleton loader for report section-->
                        <div *ngIf="report.processing" class="scroll-logs">
                            <div class="d-flex justify-content-center p-3 placeholder-wave">
                                <div class="text-center m-2 p-5 placeholder-glow placeholder-wave w-25"
                                    style="background-color: #f0f0f0; border-radius: 8px;">
                                </div>
                            </div>
                            <hr class="mx-3 ">
                            <div class="m-4 p-5" style="background-color: #f0f0f0; border-radius: 8px;"></div>
                            <hr class="mx-3 ">
                            <table class="table  skeleton-table m-3">
                                <thead>
                                    <tr>
                                        <th>
                                            <div class="placeholder-wave mb-1">
                                                <span class="placeholder col-8 bg-secondary rounded"></span>
                                            </div>
                                        </th>
                                        <th>
                                            <div class="placeholder-wave mb-1">
                                                <span class="placeholder col-8 bg-secondary rounded"></span>
                                            </div>
                                        </th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <!-- Repeat these rows for loading effect -->
                                    <tr *ngFor="let skel of [1,2,3,4,5,6]">
                                        <td>
                                            <div class="placeholder-wave mb-1">
                                                <span class="placeholder col-8 bg-secondary rounded"></span>
                                            </div>
                                        </td>
                                        <td>
                                            <div class="placeholder-wave mb-1">
                                                <span class="placeholder col-8 bg-secondary rounded"></span>
                                            </div>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>

                        <div id="pdf-report" class="scroll-logs p-3"
                            *ngIf="!isEmptyObject(this.testReport?.selectedMould) && !report.processing && report.stepperData">
                            <div id="contentToConvert1">
                            <!--  SECTION 1 -->
                            <div class="pdf-sections"> <!-- Logo, current configuration-->
                                <div class="text-center mx-3">
                                    <img [src]="downloadedLogo  " height="100px" alt="" />
                                </div>
                                <hr class="my-3">
                                <div class="h3 my-3 text-center">
                                    {{"Test-Rig Measurement Log"|translate:reportLang}}
                                </div>
                                <hr class="my-3">
                                <div class="h5">
                                    {{"Mould ID"|translate:reportLang}} : <b> {{ this.currentMould }} </b>
                                </div>
                                <hr class="my-3">
                                <div class="d-flex justify-content-between">
                                    <div class="h5">
                                        {{"Current Mould Configuration"|translate:reportLang}}
                                    </div>
                                    <div>
                                        {{"Date"|translate:reportLang}} : {{
                                        convertGMTtoFrenchTime(this.report?.stepperData?.time) }}
                                    </div>
                                </div>
                            </div>

                            <div class="pdf-sections">
                                <table class="table table-bordered">
                                    <thead>
                                        <tr>
                                            <th scope="col">{{"Channel Id"|translate:reportLang}}</th>
                                            <th scope="col">{{"Channel Name"|translate:reportLang}}</th>
                                            <th scope="col">{{"Hydraulic Diameter"|translate:reportLang}}</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr *ngFor="let channel of report?.stepperData?.channels"
                                            [hidden]="!channel.channel_enabled">
                                            <th scope="row">{{ channel.channel_id }}</th>
                                            <td> {{ channel.channel_name }} </td>
                                            <td> {{ channel.channel_diameter }} </td>
                                        </tr>
                                    </tbody>
                                </table>
                                <hr class="my-3">
                            <div class="h6"
                                *ngIf="(!tests.flow_test && !tests.air_leak_test && !tests.water_leak_test && !tests.heat_test)">
                                {{"Report Comment"|translate:reportLang}} :
                                <hr class="my-3">
                                <div class="h6">
                                    {{"Signature"|translate:reportLang}} :
                                </div>
                            </div>
                        </div>

                            <!-- SECTION 2 -->
                            <div  *ngIf="tests.flow_test" class="pdf-sections"> <!-- flow Test-->
                                <div>
                                    <div class="h3">
                                        {{"Measurement History"|translate:reportLang}}
                                        <hr class="my-3">
                                    </div>
                                    <div class="h5 text-center">
                                        {{"Measurement"|translate:reportLang}} ({{"Flow Test"|translate:reportLang}}) -
                                        {{ convertGMTtoFrenchTime(report?.flow_test_start_time) }} <br>
                                    </div>
                                    <div>
                                        <span> {{"Test Person"|translate:reportLang}} : {{ report?.username }} </span>
                                        <br>
                                        <span> {{"Location"|translate:reportLang}} : {{ report?.config?.location}}
                                        </span>
                                    </div>
                                    <div>
                                        <div class="border" *ngIf="report?.flowChartImageUrl">
                                            <img [src]="report?.flowChartImageUrl" alt="Chart Image">
                                        </div>
                                    </div>
                                    <hr class="my-3">
                                    <div class="h6"
                                        *ngIf="!tests.water_leak_test && !tests.air_leak_test && !tests.heat_test">
                                        {{"Report Comment"|translate:reportLang}} :
                                        <hr class="my-3">
                                        <div class="h6">
                                            {{"Signature"|translate:reportLang}} :
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- SECTION 3 -->
                            <div *ngIf="tests.air_leak_test" class="pdf-sections"> <!-- Air Pressure, Water Pressure -->
                                <div >
                                    <div class="h5 text-center">
                                        {{"Measurement"|translate:reportLang}} ({{"Air-Pressure"|translate:reportLang}})
                                        - {{ convertGMTtoFrenchTime(report?.air_leak_test_start_time) }}
                                    </div>
                                    <div>
                                        {{"Test Person"|translate:reportLang}} : {{ report?.username }} <br>
                                        {{"Location"|translate:reportLang}} : {{ report?.config?.location }} <br>
                                        {{"Initial Pressure"|translate:reportLang}} ({{"inlet"|translate:reportLang}}) :
                                        1.00 bar<br>
                                        {{"Stabilize"|translate:reportLang}} : {{
                                        report?.config?.pressure_stabilization_timeout }}
                                        {{"seconds"|translate:reportLang}} <br>
                                        {{"Max Pressure Drop"|translate:reportLang}} : {{
                                        report?.config?.max_pressure_loss }} bar <br>
                                    </div>
                                    <div>
                                        {{"Measurement results"|translate:reportLang}} : <b>{{
                                            report?.air_leak_test_measurement_report }} bar</b><br>
                                        {{"Result"|translate:reportLang}} : <b
                                            *ngIf="report?.air_leak_test_report == true"
                                            class="text-success">{{"OK"|translate:reportLang}} </b>
                                        <b *ngIf="report?.air_leak_test_report == false" class="text-danger">NOT
                                            {{"OK"|translate:reportLang}} </b>
                                    </div>
                                    <hr class="my-3">
                                </div>
                                <div *ngIf="tests.water_leak_test">
                                    <div class="h5 text-center">
                                        {{"Measurement"|translate:reportLang}}
                                        ({{"Water-Pressure"|translate:reportLang}}) - {{
                                        convertGMTtoFrenchTime(report?.water_leak_test_start_time) }}
                                    </div>
                                    <div>
                                        {{"Test Person"|translate:reportLang}} : {{ report?.username }} <br>
                                        {{"Location"|translate:reportLang}} : {{ report?.config?.location }} <br>
                                        {{"Initial Pressure"|translate:reportLang}} ({{"inlet"|translate:reportLang}}) :
                                        {{ report.stepperData?.max_water_pressure || '4' }} bar<br>
                                        {{"Stabilize"|translate:reportLang}} : {{
                                        report?.config?.pressure_stabilization_timeout }}
                                        {{"seconds"|translate:reportLang}} <br>
                                        {{"Max Pressure Drop"|translate:reportLang}} : {{
                                        report?.config?.max_pressure_loss }} bar <br>
                                    </div>
                                    <div>
                                        {{"Measurement results"|translate:reportLang}} : <b> {{
                                            report?.water_leak_test_measurement_report }} bar</b> <br>
                                        {{"Result"|translate:reportLang}} : <b
                                            *ngIf="report?.water_leak_test_report == true"
                                            class="text-success">{{"OK"|translate:reportLang}} </b>
                                        <b *ngIf="report?.water_leak_test_report == false" class="text-danger">{{"NOT
                                            OK"|translate:reportLang}} </b>
                                    </div>
                                    <hr class="my-3">
                                </div>
                                <div class="h6"
                                    *ngIf="(tests.water_leak_test || tests.air_leak_test) && !tests.heat_test">
                                    {{"Report Comment"|translate:reportLang}} :
                                    <hr class="my-3">
                                    <div class="h6">
                                        {{"Signature"|translate:reportLang}} :
                                    </div>
                                </div>
                            </div>

                            <!-- SECTION 4 -->
                            <div class="pdf-sections" *ngIf="tests.heat_test"> <!-- flow Test-->
                                <div >
                                    <div class="h5 text-center">
                                        {{"Measurement"|translate:reportLang}} ({{"Heat Test"|translate:reportLang}}) -
                                        {{ convertGMTtoFrenchTime(report?.heat_test_start_time) }} <br>
                                    </div>
                                    <div>
                                        <span> {{"Test Person"|translate:reportLang}} : {{ report?.username }} </span>
                                        <br>
                                        <span> {{"Location"|translate:reportLang}} : {{ report?.config?.location}}
                                        </span>
                                    </div>
                                    <div>
                                        <div class="border" *ngIf="report?.heatChartImageUrl">
                                            <img width="80%" [src]="report?.heatChartImageUrl" alt="Chart Image">
                                        </div>
                                    </div>
                                    <hr class="my-3">
                                    <div class="h6">
                                        {{"Report Comment"|translate:reportLang}} :
                                        <hr class="my-3">
                                        <div class="h6">
                                            {{"Signature"|translate:reportLang}} :
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        </div>
                    </div>
                </section>
            </div>

        </div>
    </div>
</section>

//  Following content of report to print for pdf
<div class="a4-container d-none" id="contentToConvert">

    <div class="a4-content page-break" id="section1"> <!-- Logo, current configuration-->
        <div class="text-center mx-3">
            <img [src]="downloadedLogo  " height="100px" alt="" />
        </div>
        <hr class="my-3">
        <div class="h3 my-3 text-center">
            {{"Test-Rig Measurement Log"|translate:reportLang}}
        </div>
        <hr class="my-3">
        <div class="h5">
            {{"Mould ID"|translate:reportLang}} : <b> {{ this.currentMould }} </b>
        </div>
        <hr class="my-3">
        <div class="d-flex justify-content-between">
            <div class="h5">
                {{"Current Mould Configuration"|translate:reportLang}}
            </div>
            <div>
                {{"Date"|translate:reportLang}} : {{ convertGMTtoFrenchTime(this.report?.stepperData?.time) }}
            </div>
        </div>
        <div>
            <table class="table table-bordered">
                <thead>
                <tr>
                    <th scope="col">{{"Channel Id"|translate:reportLang}}</th>
                    <th scope="col">{{"Channel Name"|translate:reportLang}}</th>
                    <th scope="col">{{"Hydraulic Diameter"|translate:reportLang}}</th>
                </tr>
                </thead>
                <tbody>
                    <tr *ngFor="let channel of report?.stepperData?.channels" [hidden]="!channel.channel_enabled">
                        <th scope="row">{{ channel.channel_id }}</th>
                        <td> {{ channel.channel_name }} </td>
                        <td> {{ channel.channel_diameter }} </td>
                    </tr>
                </tbody>
            </table>
            <hr class="my-3">
        </div>
        <div class="h6" *ngIf="(!tests.flow_test && !tests.air_leak_test && !tests.water_leak_test && !tests.heat_test)">
            {{"Report Comment"|translate:reportLang}} :
            <hr class="my-3">
            <div class="h6">
                {{"Signature"|translate:reportLang}} : 
            </div>
        </div>
        <!-- <div class="mt-auto text-center">
            <label for=""><i><b>Confidential</b> – For your eyes only – cannot be share out of your company</i></label>
        </div> -->
    </div>


    <div class="a4-content page-break" id="section2" [hidden]="!tests.flow_test"> <!-- flow Test-->
        <div *ngIf="tests.flow_test">
            <div class="h3">
                {{"Measurement History"|translate:reportLang}}
                <hr class="my-3">
            </div>
            <div class="h5 text-center">
                {{"Measurement"|translate:reportLang}} ({{"Flow Test"|translate:reportLang}}) - {{ convertGMTtoFrenchTime(report?.flow_test_start_time) }} <br>
            </div>
            <div>
                <span> {{"Test Person"|translate:reportLang}} {{ report?.username }} </span> <br>
                <span> {{"Location"|translate:reportLang}} : {{ report?.config?.location}} </span>
            </div>
            <div>
                <div class="border" *ngIf="report?.flowChartImageUrl">
                    <img [src]="report?.flowChartImageUrl" alt="Chart Image">
                </div>
            </div>
            <hr class="my-3">
            <div class="h6" *ngIf="!tests.water_leak_test && !tests.air_leak_test && !tests.heat_test">
                {{"Report Comment"|translate:reportLang}} : 
                <hr class="my-3">
                <div class="h6">
                    {{"Signature"|translate:reportLang}} : 
                </div>
            </div>
        </div>
        <!-- <div class="mt-auto text-center">
            <label for=""><i><b>Confidential</b> – For your eyes only – cannot be share out of your company</i></label>
        </div> -->
    </div>


    <div class="a4-content page-break" id="section3" [hidden]="!tests.air_leak_test && !tests.water_leak_test">
        <!-- Air Pressure, Water Pressure -->
        <div *ngIf="tests.air_leak_test">
            <div class="h5 text-center">
                {{"Measurement"|translate:reportLang}} ({{"Air-Pressure"|translate:reportLang}}) - {{ convertGMTtoFrenchTime(report?.air_leak_test_start_time) }}
            </div>
            <div>
                {{"Test Person"|translate:reportLang}} : {{ report?.username }} <br>
                {{"Location"|translate:reportLang}} : {{ report?.config?.location }} <br>
                {{"Initial Pressure"|translate:reportLang}} ({{"inlet"|translate:reportLang}}) : 1.00 bar<br>
                {{"Stabilize"|translate:reportLang}} : {{ report?.config?.pressure_stabilization_timeout }} seconds <br>
                {{"Max Pressure Drop"|translate:reportLang}} : {{ report?.config?.max_pressure_loss }} bar <br>
            </div>
            <div>
                {{"Measurement results"|translate:reportLang}} : <b>{{ report?.air_leak_test_measurement_report }} bar</b><br>
                {{"Result"|translate:reportLang}} :  <b *ngIf="report?.air_leak_test_report == true" class="text-success">OK </b>
                    <b *ngIf="report?.air_leak_test_report == false" class="text-danger">{{"NOT OK"|translate:reportLang}} </b>
            </div>
            <hr class="my-3">
        </div>
        <div *ngIf="tests.water_leak_test">
            <div class="h5 text-center">
                {{"Measurement"|translate:reportLang}} ({{"Water-Pressure"|translate:reportLang}}) - {{ convertGMTtoFrenchTime(report?.water_leak_test_start_time) }}
            </div>
            <div>
                {{"Test Person"|translate:reportLang}} : {{ report?.username }} <br>
                {{"Location"|translate:reportLang}} : {{ report?.config?.location }} <br>
                {{"Initial Pressure"|translate:reportLang}} ({{"inlet"|translate:reportLang}}) : {{ report.stepperData?.max_water_pressure || '4' }} bar<br>
                {{"Stabilize"|translate:reportLang}} : {{ report?.config?.pressure_stabilization_timeout }} seconds <br>
                {{"Max Pressure Drop"|translate:reportLang}} : {{ report?.config?.max_pressure_loss }} bar <br>
            </div>
            <div>
                {{"Measurement results"|translate:reportLang}} : <b> {{ report?.water_leak_test_measurement_report }} bar</b> <br>
                {{"Result"|translate:reportLang}} : <b *ngIf="report?.water_leak_test_report == true" class="text-success">OK </b>
                <b *ngIf="report?.water_leak_test_report == false" class="text-danger">{{"NOT OK"|translate:reportLang}} </b>
            </div>
            <hr class="my-3">
        </div>
        <div class="h6" *ngIf="(tests.water_leak_test || tests.air_leak_test) && !tests.heat_test">
            {{"Report Comment"|translate:reportLang}} :
            <hr class="my-3">
            <div class="h6">
                {{"Signature"|translate:reportLang}} :
            </div>
        </div>
        <!-- <div class="mt-auto text-center">
            <label for=""><i><b>Confidential</b> – For your eyes only – cannot be share out of your company</i></label>
        </div> -->
    </div>

    <div class="a4-content page-break" id="section4" [hidden]="!tests.heat_test"> <!-- heat Test-->
        <div *ngIf="tests.heat_test">
            <div class="h5 text-center">
                {{"Measurement"|translate:reportLang}} ({{"Heat Test"|translate:reportLang}}) - {{ convertGMTtoFrenchTime(report?.heat_test_start_time) }} <br>
            </div>
            <div>
                <span> {{"Test Person"|translate:reportLang}} : {{ report?.username }} </span> <br>
                <span> {{"Location"|translate:reportLang}} : {{ report?.config?.location}} </span>
            </div>
            <div>
                <div class="border" *ngIf="report?.heatChartImageUrl">
                    <img width="80%" [src]="report?.heatChartImageUrl" alt="Chart Image">
                </div>
            </div>
            <hr class="my-3">
            <div class="h6">
                {{"Report Comment"|translate:reportLang}} : 
                <hr class="my-3">
                <div class="h6">
                    {{"Signature"|translate:reportLang}} : 
                </div>
            </div>
        </div>
    </div>
</div>