import Notiflix from 'notiflix';
import { Subscription } from 'rxjs';
import { CommonModule } from '@angular/common';
import { TranslatePipe, TranslateService } from '../../../translate';
import { Component, OnDestroy } from '@angular/core';
import { DomSanitizer } from '@angular/platform-browser';
import { ActivatedRoute, Router } from '@angular/router';
import { ApiService } from '../../../services/api.service';
import { MouldService } from '../../../services/mould.service';
import { MachineService } from '../../../services/machine.service';
import {convertGMTtoFrenchTime} from '../../../utils/convert-fr-datetime';
import html2canvas from 'html2canvas';
import jsPDF from 'jspdf';
import moment from 'moment';

@Component({
  selector: 'app-report-mould',
  standalone: true,
  imports: [CommonModule, TranslatePipe],
  templateUrl: './report-mould.component.html',
  styleUrl: './report-mould.component.scss'
})
export class ReportMouldComponent implements OnDestroy{
  report:any = {}
  windowRef:any = window
  testReport:any = {
    'data': true,
    'recent': [],
    'previous':{},
    'selectedResult':{},
    'selectedMould': {}
  }
  tests = {
    flow_test: false,
    air_leak_test: false,
    water_leak_test: false,
    heat_test: false
  }
  activeTab: string = 'previous'
  currentMould: string = ''
  convertGMTtoFrenchTime = convertGMTtoFrenchTime
  constructor(
    private apiService: ApiService, 
    private mouldService: MouldService, 
    private machineService: MachineService, 
    private route: ActivatedRoute, 
    private router: Router, 
    public translate: TranslateService,
    private sanitizer: DomSanitizer
    ){
    const mouldId = this.route.snapshot.paramMap.get('mould-id');
      if (mouldId) {
        this.currentMould = mouldId
        this.getCurrentTestTimeStamp()
        this.machineService.selectedAction = 'report'
      } else {
        Notiflix.Notify.failure(this.translate.instant("No mould selected"));
        this.router.navigate(['/tests']);
      }
  }
  
  // Back to test screen to change mould to test
  backToTest(){
    window.location.href = '/tests'
  }
  
  generatePDF() {
    this.report.printBtn = 'printing..';
    Notiflix.Notify.success(this.translate.instant("PDF creation started!"));
    const element = document.getElementById('contentToConvert1');
    if (!element) return;
  
    // const originalScrollTop = window.scrollY;
    // element.style.overflow = 'visible'; // Remove overflow auto
    // // Temporarily expand content to full height
    const sections = document.querySelectorAll('.pdf-sections'); // Assuming sections have a class 'section'
    const pdf = new jsPDF('p', 'mm', 'a4');
    // const pdfWidth = pdf.internal.pageSize.getWidth();
    // const pdfHeight = pdf.internal.pageSize.getHeight();

    let pagesAdded = 0;
    let canvasPromises:any = []
    sections.forEach((section, index) => {
      canvasPromises.push(html2canvas(section as HTMLElement, {
      scrollY: -window.scrollY, // Capture from top
      windowWidth: document.body.scrollWidth,
      windowHeight: (section as HTMLElement).scrollHeight, // Capture full scrollable height
      }));
   
      // window.scrollTo(0, originalScrollTop);  // Restore original scroll
    });
    Promise.all(canvasPromises).then((canvas) => {
      // console.log('canvas',canvas)
    //  canvas.pop()
      const pdf = new jsPDF('p', 'mm', 'a4');
      const margin = 10; // 10mm margin on all sides
      const pageWidth = 210;
      const pageHeight = 297;
      const usableWidth = pageWidth - margin * 2;
      const usableHeight = pageHeight - margin * 2;

      let currentY = margin;
      let pagesAdded = 0;

            
      canvas.forEach((singleCanvas: any, index) => {
        const imgData = singleCanvas.toDataURL('image/png');
        const aspectRatio = singleCanvas.height / singleCanvas.width;
        const imgHeight = usableWidth * aspectRatio;

        // Check if the image fits in the remaining space on the current page
        if (currentY + imgHeight > pageHeight - margin) {
          pdf.addPage();
          currentY = margin;
          pagesAdded++;
        }

        pdf.addImage(imgData, 'PNG', margin, currentY, usableWidth, imgHeight);
        currentY += imgHeight;
      });
      const pdfName = `${this.testReport.selectedMould.startTime}_${this.testReport.selectedMould.stopTime}_${this.currentMould}`;
      Notiflix.Notify.success(this.translate.instant("PDF generated now saving!"));

      this.mouldService.uploadPDF(new Blob([pdf.output('blob')], { type: 'application/pdf' }),pdfName+'.pdf').subscribe({
          next: (response) => {
            console.log('PDF successfully uploaded to backend:', response);
            Notiflix.Notify.success(this.translate.instant("PDF saved successfull!"));
          },
          error: (err) => {
            console.error('Error uploading PDF to backend:', err);
          }
        });
      pdf.save(`${pdfName}.pdf`);
      this.report.printBtn = 'print';
      // element.style.overflow = 'auto'; // Remove overflow auto

    })
  }

  // Switch tabs from previous and recents tests
  switchTab(tab: string) {
    this.activeTab = tab;
  }
  
  /**
   * 
   * @param startTime 
   * @param endTime 
   * @returns 
   */
  calculateDuration(startTime: string, endTime: string): string {
    const start = moment(startTime);
    const end = moment(endTime);
    const duration = moment.duration(end.diff(start));
  
    const hours = Math.floor(duration.asHours());
    const minutes = duration.minutes();
    const seconds = duration.seconds();
  
    return `${String(hours).padStart(2, '0')}:${String(minutes).padStart(2, '0')}:${String(seconds).padStart(2, '0')}`;
  }


  // /**
  //  * Format the date time to make it According to API call
  //  * @param timeStamp 
  //  * @returns 
  //  */
  formatDateTimeInterval(timeStamp: any): any{
    const date = new Date(timeStamp);
    const formattedDate = date.getFullYear() + "-" +
    String(date.getMonth() + 1).padStart(2, "0") + "-" +
    String(date.getDate()).padStart(2, "0") + " " +
    String(date.getHours()).padStart(2, "0") + ":" +
    String(date.getMinutes()).padStart(2, "0") + ":" +
    String(date.getSeconds()).padStart(2, "0");
    return formattedDate;
  }

  /**
   * To verify that selected mould timestamp and currect time stamp is same or not 
   * @param selectedDate 
   * @param currentDate 
   * @returns 
   */
  verifySelectedTimeStamp(selectedDate:any, currentDate:any){
    if(!this.isEmptyObject(selectedDate)){
      currentDate.start_time = this.formatDateTimeInterval(currentDate.start_time)
      selectedDate.startTime = this.formatDateTimeInterval(selectedDate.startTime)
      currentDate.stop_time = this.formatDateTimeInterval(currentDate.stop_time)
      selectedDate.stopTime = this.formatDateTimeInterval(selectedDate.stopTime)
      if((selectedDate.startTime == currentDate.start_time) && (currentDate.stop_time == selectedDate.stopTime)){
        return true
      }
    }
    return false
  }

  isEmptyObject(obj: any): boolean {
    return obj && Object.keys(obj).length === 0 && obj.constructor === Object;
  }


  /**
   * Get total time stamps of the current selected mould
   */
  getCurrentTestTimeStamp(){
    if(this.currentMould){
      this.mouldService.getMouldTestHistory(this.currentMould).subscribe({
        next: (timeStamps)=>{
          if(timeStamps){
            const formatted = timeStamps.mould_history.map((item:any) => ({
              start_time: moment(item.start_time).format('YYYY-MM-DD HH:mm:ss'),
              stop_time: moment(item.stop_time).format('YYYY-MM-DD HH:mm:ss')
            }));
            this.testReport.recent = formatted.reverse();
            this.testReport.data = this.testReport.recent.length < 1 ? false: true
            if(window.location.href.includes('latest')){
              const mouldReportTime = this.testReport.recent[0]
              this.reportOfSelectedMould(mouldReportTime.start_time, mouldReportTime.stop_time)
            }
          }
        },
        error: ()=>{
          if(this.apiService.baseUrl=='http://127.0.0.1:8000'){          
            this.testReport.recent = {  "mould_history": [    {      "start_time": "2025-03-18T12:42:38.135084",      "stop_time": "2025-03-18T12:56:35.929185"     },    {      "start_time": "2025-03-18T15:10:40.942731",      "stop_time": "2025-03-18T15:19:06.277700"     },    {      "start_time": "2025-03-18T15:36:14.109964",      "stop_time": "2025-03-18T15:42:00.743524"     },    {      "start_time": "2025-03-19T09:45:37.771914",      "stop_time": "2025-03-19T09:47:54.012995"     },    {      "start_time": "2025-03-19T10:28:07.489395",      "stop_time": "2025-03-19T10:35:47.013610"     },    {      "start_time": "2025-03-19T12:24:53.744964",      "stop_time": "2025-03-19T12:29:55.469909"     },    {      "start_time": "2025-03-19T12:30:07.673399",      "stop_time": "2025-03-19T12:35:18.967340"     },    {      "start_time": "2025-03-19T14:16:26.697283",      "stop_time": "2025-03-19T14:21:34.388655"     },    {      "start_time": "2025-03-19T14:22:12.101756",      "stop_time": "2025-03-19T14:27:14.527204"     },    {      "start_time": "2025-03-19T14:29:38.245198",      "stop_time": "2025-03-19T14:34:50.768994"     },    {      "start_time": "2025-03-19T15:40:38.523709",      "stop_time": "2025-03-19T15:45:38.600173"     },    {      "start_time": "2025-03-20T12:42:11.664173",      "stop_time": "2025-03-20T12:53:27.783609"     },    {      "start_time": "2025-03-20T14:23:30.020598",      "stop_time": "2025-03-20T14:34:42.215456"     },    {      "start_time": "2025-03-20T14:39:51.485927",      "stop_time": "2025-03-20T14:51:15.021529"     },    {      "start_time": "2025-03-20T14:54:33.441459",      "stop_time": "2025-03-20T15:05:39.032600"     },    {      "start_time": "2025-03-20T15:06:28.727852",      "stop_time": "2025-03-20T15:17:37.120878"     },    {      "start_time": "2025-03-20T15:23:05.465708",      "stop_time": "2025-03-20T15:29:45.439912"     },{ "start_time": "2025-03-20T15:54:09.302742",
              "stop_time": "2025-03-20T15:57:17.425862"     },
            {
              "start_time": "2025-03-20T16:01:42.321930",
              "stop_time": "2025-03-20T16:04:33.283793"     },
            {
              "start_time": "2025-03-20T16:05:51.320409",
              "stop_time": "2025-03-20T16:09:10.002090"     },
            {
              "start_time": "2025-03-20T16:10:01.618708",
              "stop_time": "2025-03-20T16:12:58.515223"     },
            {
              "start_time": "2025-03-20T16:17:04.784945",
              "stop_time": "2025-03-20T16:27:50.027588"     },
            {
              "start_time": "2025-03-20T16:30:20.439246",
              "stop_time": "2025-03-20T16:35:40.588349"     },
            {
              "start_time": "2025-03-20T16:38:19.739018",
              "stop_time": "2025-03-20T16:39:22.219433"     }
            ]
            };      
          }
        }
        
      })
    }
  }
  reportLang: string = 'fr'; // Default language for the report
  async reportOfSelectedMould(startTime: string, stopTime: string,lng='fr') {
    this.reportLang = lng;
    this.report.processing = true;
    const previousSelectedMould = this.testReport.selectedMould;
    this.testReport.selectedMould = {
      'startTime': startTime,
      'stopTime': stopTime
    };
    startTime = this.formatDateTimeInterval(startTime);
    stopTime = this.formatDateTimeInterval(stopTime);

    try {
      this.downloadLogo()
      const events = await this.mouldService.getMouldEventsSelectedTimeStamp(this.currentMould, startTime, stopTime).toPromise();
      
      if (events.mould_events_timestamp) {
        this.testReport.selectedResult = events.mould_events_timestamp.reverse();
        this.checkTests(this.testReport.selectedResult);
        await this.fetchMouldSettingsAndGraphReport();
      }
    } catch (error) {
      this.testReport.selectedMould = previousSelectedMould;
      // this.testReport.selectedMould = {};
    }

    try {
      // if(this.tests?.flow_test || this.tests?.air_leak_test || this.tests?.water_leak_test || this.tests?.heat_test){
        const config = await this.mouldService.getConfiguration().toPromise();
        // this.report.processing = false;
        if (config) {
          this.report['config'] = config;
          this.report.username = JSON.parse(localStorage.getItem('username') || '');
          this.checkReportOK(this.testReport.selectedResult);
        }
      // }
    } catch (error) {
      // Handle error
    }
  }

  /**
   * Download the logo from the server with filename 
   */
  downloadedLogo:any
  downloadLogo(){
    this.mouldService.downloadUplodedLogo('logo-mould.jpg').subscribe({
      next: (response) => {
        const blob = new Blob([response], { type: 'image/jpg' });
        const logoURL = URL.createObjectURL(blob);
        if(this.downloadedLogo)
          URL.revokeObjectURL(this.downloadedLogo.changingThisBreaksApplicationSecurity);
        this.downloadedLogo = this.sanitizer.bypassSecurityTrustResourceUrl(logoURL);
      },
      error: (err) => {
        console.error('❌ Error in downloadUplodedLogo:', err);
      }
    })
  }

  async fetchMouldSettingsAndGraphReport() {
    try {
      const mould = await this.mouldService.fetchMouldSettings(this.currentMould).toPromise();
      this.report.stepperData = mould;
      const enabledChannelIds: string[] = this.report.stepperData?.channels
        .filter((channel: any) => channel.channel_enabled)
        .map((channel: any) => channel.channel_id)
        .join(',');
      this.report.enabledChannels = enabledChannelIds;
      await this.getGraphReport(enabledChannelIds);
    } catch (error) {
      console.log(error)
      // Handle error
    }
  }

  private getFlowGraphReportSubscription?: Subscription;
  private getHeatGraphReportSubscription?: Subscription;
  async getGraphReport(enabledChannels: any) {
    if(this.tests.flow_test == true){
      // this.getFlowGraphReportSubscription?.unsubscribe(); // Cancel previous request
      const currentLang = this.reportLang
      await this.mouldService.reportInFormOfGraph(this.testReport.selectedMould?.flowTest?.startLog, this.testReport.selectedMould?.flowTest?.stopLog, enabledChannels, currentLang).toPromise().then(blob => {
        this.report.processing = false;
        const objectURL = URL.createObjectURL(blob);
        if (this.report.flowChartImageUrl) {
          URL.revokeObjectURL(this.report.flowChartImageUrl.changingThisBreaksApplicationSecurity); // bypassed URLs
        }
        this.report.flowChartImageUrl = this.sanitizer.bypassSecurityTrustResourceUrl(objectURL);
      }).catch(error => {
        console.error('Error fetching flow graph report:', error);
      });
    }
    if(this.tests.heat_test == true){
      this.getHeatGraphReportSubscription?.unsubscribe(); // Cancel previous request
      const currentLang = this.reportLang
      this.getHeatGraphReportSubscription = this.mouldService.heatReportInFormOfGraph(this.testReport.selectedMould?.heatTest?.startLog, this.testReport.selectedMould?.heatTest?.stopLog, enabledChannels, currentLang).subscribe(blob => {
        this.report.processing = false;
        const objectURL = URL.createObjectURL(blob);
        if (this.report.heatChartImageUrl) {
          URL.revokeObjectURL(this.report.heatChartImageUrl.changingThisBreaksApplicationSecurity); // bypassed URLs
        }
        this.report.heatChartImageUrl = this.sanitizer.bypassSecurityTrustResourceUrl(objectURL);
      });
    }
    if(!this.tests.flow_test && !this.tests.heat_test)
      this.report.processing = false;
  }

  ngOnDestroy(): void {
    this.machineService.selectedAction = ''
  }
  
  /**
   * Logs should indicate which test was executed for the selected timestamp.
   * @param logs 
   */
  checkTests(logs: { msg: string; time: string }[]): void {
    Object.keys(this.tests).forEach((key) => (this.tests[key as keyof typeof this.tests] = false)); // Reset values
  
    logs.forEach((log) => {
      const msg = log.msg;
      Object.keys(this.tests).forEach((key) => {
        if (msg.includes(key)) {
          this.tests[key as keyof typeof this.tests] = true;
        }
      });
    });
    if(this.tests.flow_test){
      this.testReport.selectedMould['flowTest'] = this.testReport.selectedMould['flowTest'] || {};
      this.testReport.selectedMould['flowTest'].startLog = logs.find((log: any) => log.msg === "flow_test_start");
      this.testReport.selectedMould['flowTest'].stopLog = logs.find((log: any) => log.msg === "flow_test_complete");

      this.testReport.selectedMould['flowTest'].startLog = this.formatDateTimeInterval(this.testReport.selectedMould['flowTest'].startLog?.time);
      this.testReport.selectedMould['flowTest'].stopLog = this.formatDateTimeInterval(this.testReport.selectedMould['flowTest'].stopLog?.time);
    }
    if(this.tests.heat_test){
      this.testReport.selectedMould['heatTest'] = this.testReport.selectedMould['heatTest'] || {};
      this.testReport.selectedMould['heatTest'].startLog = logs.find((log: any) => log.msg === "heat_test_heating_start");
      this.testReport.selectedMould['heatTest'].stopLog = logs.find((log: any) => log.msg === "heat_test_complete");
      
      this.testReport.selectedMould['heatTest'].startLog = this.formatDateTimeInterval(this.testReport.selectedMould['heatTest'].startLog?.time);
      this.testReport.selectedMould['heatTest'].stopLog = this.formatDateTimeInterval(this.testReport.selectedMould['heatTest'].stopLog?.time);
    }
  }

  checkReportOK(logs: { msg: string; time: string }[]): void {
    if(this.tests.flow_test){
      const startTime = logs.find((log: any) => {
        if (log.msg == "flow_test_start") {
          return log;
        }
      });
      
      if(startTime)
        this.report['flow_test_start_time'] = this.formatDateTimeInterval(startTime.time)

    }
    if(this.tests.water_leak_test){
      const startTime = logs.find((log: any) => {
        if (log.msg == "water_leak_test_measure_start") {
          return log;
        }
      });
      const stopTime = logs.find((log:any)=>{
        if(log.msg == "water_leak_test_measure_complete") 
        return log 
      })
      const waterLeakStartTime = logs.find((log: any) => {
        if (log.msg == "water_leak_test_start") {
          return log;
        }
      });
      if(waterLeakStartTime)
        this.report['water_leak_test_start_time'] = this.formatDateTimeInterval(waterLeakStartTime.time)
      if(startTime && stopTime){
        this.mouldService.pumpPressureInTimeStmp(this.formatDateTimeInterval(startTime.time), this.formatDateTimeInterval(stopTime.time)).subscribe({
          next: (pumpPressure)=>{
            if(pumpPressure.pump_pressure_timestamped_data.length > 0 ){
              const startPressureVal= pumpPressure.pump_pressure_timestamped_data[pumpPressure.pump_pressure_timestamped_data.length -1].pump_pressure
              const endPressureVal  = pumpPressure.pump_pressure_timestamped_data[0].pump_pressure
              const pressureLoss = startPressureVal - endPressureVal
              if(pressureLoss == 0){
                this.report['water_leak_test_measurement_report'] = 0  
              }else{
                this.report['water_leak_test_measurement_report'] = pressureLoss? pressureLoss.toFixed(1): 'NA';
              }
              this.report['water_leak_test_report'] = pressureLoss <= this.report?.config?.max_pressure_loss? true : false;
            }else{
              this.report['water_leak_test_report'] = false
              this.report['water_leak_test_measurement_report'] = 'NA';
            }
          }
        })
      }
    }
    if(this.tests.air_leak_test){
      const startTime = logs.find((log: any) => {
        if (log.msg == "air_leak_test_set_measure_start") {
          return log;
        }
      });
      const stopTime = logs.find((log:any)=>{
        if(log.msg == "air_leak_test_set_measure_complete") 
        return log 
      })
      const airLeakStartTime = logs.find((log: any) => {
        if (log.msg == "air_leak_test_start") {
          return log;
        }
      });
      if(airLeakStartTime)
        this.report['air_leak_test_start_time'] = this.formatDateTimeInterval(airLeakStartTime.time)
      if(startTime && stopTime){
        this.mouldService.pumpPressureInTimeStmp(this.formatDateTimeInterval(startTime.time), this.formatDateTimeInterval(stopTime.time)).subscribe({
          next: (pumpPressure)=>{
            if(pumpPressure.pump_pressure_timestamped_data.length > 0 ){
              const startPressureVal= pumpPressure.pump_pressure_timestamped_data[pumpPressure.pump_pressure_timestamped_data.length -1].pump_pressure
              const endPressureVal  = pumpPressure.pump_pressure_timestamped_data[0].pump_pressure
              const pressureLoss = startPressureVal - endPressureVal
              if(pressureLoss == 0){
                this.report['air_leak_test_measurement_report'] = 0  
              }else{
                this.report['air_leak_test_measurement_report'] = pressureLoss? pressureLoss.toFixed(1): 'NA';
              }
              this.report['air_leak_test_report'] = pressureLoss <= this.report?.config?.max_pressure_loss? true : false;
            }else{
              this.report['air_leak_test_report'] = false
              this.report['air_leak_test_measurement_report'] = 'NA';
            }
          }
        })
      }
    }
    if(this.tests.heat_test){
      const heatStartTime = logs.find((log: any) => {
        if (log.msg == "heat_test_start") {
          return log;
        }
      });
      if(heatStartTime)
        this.report['heat_test_start_time'] = this.formatDateTimeInterval(heatStartTime.time)
    }
  }

  /** 
   * Download PDF of selected report
   */
  downloadMouldResunt() {
    this.windowRef.printDiv('contentToConvert');
    return;
    // this.report.printBtn = 'printing..';
    // const reportDiv = document.getElementById('contentToConvert');
    // if (reportDiv) reportDiv.classList.remove('d-none');

    // const sections = ['section1', 'section2', 'section3', 'section4']; // IDs of content sections
    // const pdf = new jsPDF.jsPDF('p', 'mm', 'a4'); // Single PDF instance
    // const imgWidth = 210; // A4 width in mm
    // const pageHeight = 297; // A4 height in mm
    // let pagesAdded = 0; // Track pages added

    // // Function to check if canvas is empty (all white pixels)
    // const isCanvasEmpty = (canvas: HTMLCanvasElement) => {
    //     const ctx = canvas.getContext('2d');
    //     if (!ctx) return true;
    //     const imgData = ctx.getImageData(0, 0, canvas.width, canvas.height).data;
    //     return !imgData.some((channel, index) => index % 4 !== 3 && channel !== 255); // Ignore alpha channel
    // };

    // // Function to process each section sequentially
    // const processSection = (index: number) => {
    //     if (index >= sections.length) {
    //         // Only save if there's content
    //         if (pagesAdded > 0) {
    //             const pdfName = `${this.testReport.selectedMould.startTime}_${this.testReport.selectedMould.stopTime}_${this.currentMould}`;
    //             pdf.save(`${pdfName}.pdf`);
    //         }
    //         if (reportDiv) reportDiv.classList.add('d-none');
    //         this.report.printBtn = 'print';
    //         return;
    //     }

    //     const sectionId = sections[index];
    //     const data = document.getElementById(sectionId);

    //     if (data) {
    //         html2canvas(data, {
    //             useCORS: true,
    //             scale: 2
    //         }).then(canvas => {
    //             if (isCanvasEmpty(canvas)) {
    //                 console.log(`Skipping empty section: ${sectionId}`);
    //                 processSection(index + 1); // Move to next section
    //                 return;
    //             }

    //             const imgHeight = canvas.height * imgWidth / canvas.width;
    //             const contentDataURL = canvas.toDataURL('image/png');

    //             if (pagesAdded > 0) {
    //                 pdf.addPage();
    //             }

    //             pdf.addImage(contentDataURL, 'PNG', 0, 0, imgWidth, imgHeight, '', 'FAST');
    //             pagesAdded++; // Increase page count

    //             processSection(index + 1); // Move to next section
    //         });
    //     } else {
    //         processSection(index + 1); // Skip missing sections
    //     }
    // };

    // // Start processing from section 1
    // processSection(0);
  }


}
