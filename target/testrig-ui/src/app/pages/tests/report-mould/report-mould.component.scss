@import '../../../../styles/variables';

.logs{
    height: calc(100vh - 173px);
}

.print-label{
    // position: absolute;
    // right: 10px;
    // top: -12px;
    background: white;
    padding: 0px 10px;
}

.a4-content {
  display: flex;
  flex-direction: column;
  padding: 10mm;
  max-width: 100%;
  height: 100%;
  overflow: hidden;
}

.skeleton-table{
  width: -webkit-fill-available;
}

.btn-lang{
  border: none;
  border-radius: 0px;
  height: 38px;
}
.btn-active{
  border-bottom: 3px solid $button-blue;
}
.page-break {
  page-break-after: always;
  break-after: page;
}