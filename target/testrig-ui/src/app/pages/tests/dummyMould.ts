// Combined schema with <PERSON><PERSON><PERSON>'s Schema
export const moulds = [

    {
        mouldid: '121', // String
        time: '',       // Time 
        comments: 'test Comment to show data', // String
        pressure_set_point: 3.0,   
        temperature_set_point: 3.50,
        enable_all: false,
        channels:[
            { channel_id: 1, channel_name: "channel", channel_lower_limit: 30, channel_upper_limit: 90, channel_enabled: true, channel_diameter: 'circle' },
            { channel_id: 2, channel_name: "channel", channel_lower_limit: 30, channel_upper_limit: 90, channel_enabled: true, channel_diameter: 'square' },
            { channel_id: 3, channel_name: "channel", channel_lower_limit: 30, channel_upper_limit: 90, channel_enabled: true, channel_diameter: 'circle' },
            { channel_id: 4, channel_name: "channel", channel_lower_limit: 30, channel_upper_limit: 90, channel_enabled: true, channel_diameter: 'square' },
            { channel_id: 5, channel_name: "channel", channel_lower_limit: 30, channel_upper_limit: 90, channel_enabled: true, channel_diameter: 'circle' },
            { channel_id: 6, channel_name: "channel", channel_lower_limit: 30, channel_upper_limit: 90, channel_enabled: true, channel_diameter: 'circle' },
            { channel_id: 7, channel_name: "channel", channel_lower_limit: 30, channel_upper_limit: 90, channel_enabled: true, channel_diameter: 'square' },
            { channel_id: 8, channel_name: "channel", channel_lower_limit: 30, channel_upper_limit: 90, channel_enabled: true, channel_diameter: 'circle' },
            { channel_id: 9, channel_name: "channel", channel_lower_limit: 30, channel_upper_limit: 90, channel_enabled: true, channel_diameter: 'circle' },
            { channel_id: 10, channel_name: "channel", channel_lower_limit:30, channel_upper_limit: 80, channel_enabled: true, channel_diameter: 'circle' },
            { channel_id: 11, channel_name: "channel", channel_lower_limit:30, channel_upper_limit: 80, channel_enabled: true, channel_diameter: 'square' },
            { channel_id: 12, channel_name: "channel", channel_lower_limit:30, channel_upper_limit: 80, channel_enabled: true, channel_diameter: 'circle' },
        ]
    },
    {
        mouldid: '122', // String
        time: '',       // Time 
        comments: 'test Comment to show data', // String
        pressure_set_point: 3.0,   
        temperature_set_point: 3.5,
        enable_all: false,
        channels:[
            { channel_id: 1, channel_name: "channel", channel_lower_limit: 30, channel_upper_limit: 90, channel_enabled: true, channel_diameter: 'circle' },
            { channel_id: 2, channel_name: "channel", channel_lower_limit: 30, channel_upper_limit: 90, channel_enabled: true, channel_diameter: 'square' },
            { channel_id: 3, channel_name: "channel", channel_lower_limit: 30, channel_upper_limit: 90, channel_enabled: true, channel_diameter: 'circle' },
            { channel_id: 4, channel_name: "channel", channel_lower_limit: 30, channel_upper_limit: 90, channel_enabled: true, channel_diameter: 'square' },
            { channel_id: 5, channel_name: "channel", channel_lower_limit: 30, channel_upper_limit: 90, channel_enabled: true, channel_diameter: 'circle' },
            { channel_id: 6, channel_name: "channel", channel_lower_limit: 30, channel_upper_limit: 90, channel_enabled: true, channel_diameter: 'circle' },
            { channel_id: 7, channel_name: "channel", channel_lower_limit: 30, channel_upper_limit: 90, channel_enabled: true, channel_diameter: 'square' },
            { channel_id: 8, channel_name: "channel", channel_lower_limit: 30, channel_upper_limit: 90, channel_enabled: true, channel_diameter: 'circle' },
            { channel_id: 9, channel_name: "channel", channel_lower_limit: 30, channel_upper_limit: 90, channel_enabled: true, channel_diameter: 'circle' },
            { channel_id: 10, channel_name: "channel", channel_lower_limit:30, channel_upper_limit: 80, channel_enabled: true, channel_diameter: 'circle' },
            { channel_id: 11, channel_name: "channel", channel_lower_limit:30, channel_upper_limit: 80, channel_enabled: true, channel_diameter: 'square' },
            { channel_id: 12, channel_name: "channel", channel_lower_limit:30, channel_upper_limit: 80, channel_enabled: true, channel_diameter: 'circle' },
        ]
    },
    {
        mouldid: '123', // String
        time: '',       // Time 
        comments: 'test Comment to show data', // String
        pressure_set_point: 3.0,   
        temperature_set_point: 3.5,
        enable_all: false,
        channels:[
            { channel_id: 1, channel_name: "channel", channel_lower_limit: 30, channel_upper_limit: 90, channel_enabled: true, channel_diameter: 'circle' },
            { channel_id: 2, channel_name: "channel", channel_lower_limit: 30, channel_upper_limit: 90, channel_enabled: true, channel_diameter: 'square' },
            { channel_id: 3, channel_name: "channel", channel_lower_limit: 30, channel_upper_limit: 90, channel_enabled: true, channel_diameter: 'circle' },
            { channel_id: 4, channel_name: "channel", channel_lower_limit: 30, channel_upper_limit: 90, channel_enabled: true, channel_diameter: 'square' },
            { channel_id: 5, channel_name: "channel", channel_lower_limit: 30, channel_upper_limit: 90, channel_enabled: true, channel_diameter: 'circle' },
            { channel_id: 6, channel_name: "channel", channel_lower_limit: 30, channel_upper_limit: 90, channel_enabled: true, channel_diameter: 'circle' },
            { channel_id: 7, channel_name: "channel", channel_lower_limit: 30, channel_upper_limit: 90, channel_enabled: true, channel_diameter: 'square' },
            { channel_id: 8, channel_name: "channel", channel_lower_limit: 30, channel_upper_limit: 90, channel_enabled: true, channel_diameter: 'circle' },
            { channel_id: 9, channel_name: "channel", channel_lower_limit: 30, channel_upper_limit: 90, channel_enabled: true, channel_diameter: 'circle' },
            { channel_id: 10, channel_name: "channel", channel_lower_limit:30, channel_upper_limit: 80, channel_enabled: true, channel_diameter: 'circle' },
            { channel_id: 11, channel_name: "channel", channel_lower_limit:30, channel_upper_limit: 80, channel_enabled: true, channel_diameter: 'square' },
            { channel_id: 12, channel_name: "channel", channel_lower_limit:30, channel_upper_limit: 80, channel_enabled: true, channel_diameter: 'circle' },
        ]
    }
]

