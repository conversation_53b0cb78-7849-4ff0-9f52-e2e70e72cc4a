<section class=" test-tabs">
    <ul class="nav nav-tabs">
        <li class="nav-item col-sm-3" 
            (click)="userInput = true;activeTab = 'flow_test'"
            [ngClass]="activeTab == 'flow_test' ? ' clicked' : ' '"
            [hidden]="tests['flow_test'] !== true">
            <a class="nav-link d-flex justify-content-between cursor-pointer position-relative " [ngClass]="{'active': availableTests[0].value == true, 'border-0' : activeTab != 'flow_test'}" >
                {{"Flow Test"|translate}}
                <span class="" class="loader-test-status" *ngIf="availableTests[0].value != true && !testStopped['flow_test'] "></span>
                <span class="checkMark" *ngIf="availableTests[0].value == true && !testError">
                    <img src="assets/images/check1.svg" alt="check"/>
                </span>
                <span class="text-danger" *ngIf="testStopped['flow_test']">❗{{ "Finished with error" | translate }}</span>
            </a>
        </li>
        <li class="nav-item col-sm-3 " 
            (click)="userInput = true;activeTab = 'air_leak_test'"
            [ngClass]="activeTab == 'air_leak_test' ? ' clicked ' : ' '"
            [hidden]="tests['air_leak_test'] !== true">
            <a class="nav-link d-flex justify-content-between cursor-pointer position-relative" [ngClass]="{'active' :  availableTests[1].value == true, 'border-0' : activeTab != 'air_leak_test' } " >
                {{"Air Leak Test"|translate}}
                <span class="" class="loader-test-status" *ngIf="availableTests[1].value != true && !testStopped['air_leak_test']"></span>
                <span class="checkMark" *ngIf="availableTests[1].value == true && !testError">
                    <img src="assets/images/check1.svg" alt="check"/>
                </span>
                <span class="text-danger" *ngIf="testStopped['air_leak_test']">❗{{ "Finished with error" | translate }}</span>
            </a>
        </li>
        <li class="nav-item col-sm-3" 
            (click)="userInput = true;activeTab = 'water_leak_test'"
            [ngClass]="activeTab == 'water_leak_test' ? ' clicked' : ''"
            [hidden]="tests['water_leak_test'] !== true">
            <a class="nav-link d-flex justify-content-between  cursor-pointer position-relative" [ngClass]="{'active' :  availableTests[2].value == true, 'border-0' : activeTab != 'water_leak_test'}" >
                {{"Water Leak Test"|translate}}
                <span class="" class="loader-test-status" *ngIf="availableTests[2].value != true && !testStopped['water_leak_test']"></span>
                <span class="checkMark" *ngIf="availableTests[2].value == true  && !testError">
                    <img src="assets/images/check1.svg" alt="check"/>
                </span>
                <span class="text-danger" *ngIf="testStopped['water_leak_test']">❗{{ "Finished with error" | translate }}</span>
            </a>
        </li>
        <li class="nav-item col-sm-3" 
            (click)="activeTab = 'heat_test'"
            [ngClass]="activeTab == 'heat_test' ? ' clicked' : ''"
            [hidden]="tests['heat_test'] !== true">
            <a class="nav-link d-flex justify-content-between  cursor-pointer position-relative" [ngClass]="{'active' :  availableTests[3].value == true, 'border-0' : activeTab != 'heat_test'}" >
                {{"Heat Test"|translate}}
                <span class="" class="loader-test-status" *ngIf="availableTests[3].value != true && !testStopped['heat_test']"></span>
                <span class="checkMark" *ngIf="availableTests[3].value == true && !testError">
                    <img src="assets/images/check1.svg" alt="check"/>
                </span>
                <span class="text-danger" *ngIf="testStopped['heat_test']">❗{{ "Finished with error" | translate }}</span>
            </a>
        </li>
    </ul>
    <div class="border  overflow-auto border-top-0" style="height: calc(100vh - 314px);">
        <div class="p-4 w-100" >
            <div class="channel-flow-val my-3 label-color" >
                <div class="d-flex justify-content-between">
                    <div>
                        <label class="justify-content-between">
                            {{"Pressure Set Points"|translate}} : <b> {{ this.testConfigReport.pressure.pressureVal }}</b>
                        </label>
                    </div>
                    <div *ngIf="activeTab == 'air_leak_test' || activeTab == 'water_leak_test'">
                        <label >
                            {{"Maximum Pressure Loss"|translate}} : <b> {{ this.testConfigReport.pressure.pressureLoss }}</b>
                        </label>
                    </div>
                    <div *ngIf="activeTab == 'heat_test'">
                        <label >
                            {{"Outlet Temp"|translate}} : <b> {{ this.testConfigReport.outletTemp }} °C</b>
                        </label>
                    </div>
                </div>
                <!-- <div class="card-steps px-2"  *ngIf="activeTab === 'flow_test'">
                    <div class="card-steps-inner">
                        <span class=" d-flex align-items-center" *ngFor="let run of getRange(stepperData.pressure_set_point_min, stepperData.pressure_set_point_max)"
                        [ngClass]="run < stepperData.pressure_set_point_max? ' flex-fill' : '' ">
                            <button class="card-step-btn"
                                (click)="activePressure = run"
                                [disabled]="availableTests[0].value !== true"
                                [ngClass]="{
                                    'visited': '',
                                }">
                                {{ run }}
                            </button>
                            <div class="line-grow light-line"></div>
                        </span>
                    </div>
                </div> -->
            </div>

            <div class="channel-flow-val my-2" *ngIf="activeTab === 'flow_test'">
                <table class="table table-borderless">
                    <thead class="table-light">
                        <tr>
                            <th scope="col"  class="label-color">{{"Channels"|translate}}</th>
                            <th scope="col" *ngFor="let channel of stepperData.channels" [hidden]="channel.channel_enabled != true"> {{ channel.channel_id }} </th>
                          </tr>
                    </thead>

                    <tbody *ngIf="activeTab === 'flow_test'">
                        <tr>
                            <th scope="row" class="label-color">{{"Flow Values"|translate}} (l/min)</th>

                            <td *ngFor="let channel of getKeys(testConfigReport.pressureTabs[activePressure])">
                                {{ testConfigReport.pressureTabs[activePressure][channel] < 1 ? '&lt; 1' : testConfigReport.pressureTabs[activePressure][channel].toFixed(1) }}
                            </td>
                               
                          </tr>
                    </tbody>
                    <!-- <tbody *ngIf="activeTab === 'heat_test'">
                        <tr>
                            <th scope="row" class="label-color">{{"Flow Values"|translate}}</th>
                            <td *ngFor="let channel of getKeys(testConfigReport.heatTabFlow)">
                                {{ testConfigReport.heatTabFlow[channel] < 1 ? '&lt; 1' : testConfigReport.heatTabFlow[channel].toFixed(1) }}
                            </td>
                               
                          </tr>
                    </tbody> -->
                </table>
            </div>

        </div>
        <div class=" position-relative logs" style="max-height: 275px;min-height: 275px;">
            <label class="logs-label">
                {{"Logs"|translate}}
            </label>
            <div #scrollContainer class="scroll-logs" >
                <div *ngFor="let log of testConfigReport.logs">
                    {{ log }}
                    <br>
                </div>
                <span class="blink">....</span>
            </div>
        </div>
    </div>
</section>