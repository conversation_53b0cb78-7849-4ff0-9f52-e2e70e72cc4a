@import '../../../../styles/variables';

.selected-tab-style{
    // background-color: #f4f7f8;
    background-color: lightgray;
    font-size: larger;
    padding: 10px 20px;
    margin: 10px 5px;
}

.selected-tab-style.active{
    border: none;
}

.test-tabs{
    li{
        outline: 0 !important;
    }
    padding: 0px 20px 20px;
    .nav-link{
        border-color: transparent !important;
    }
    .clicked{
        // border: 1px solid lightgray;
        border-bottom: none;
        .nav-link{
            color: black;
            border-color: #dee2e6 !important;
            border-bottom-color: white !important;
            
            opacity: 1;
            
        }
    }
    .checkMark
    {
        // background-color: #EAEAEA;
        background-color: $green;
        display: flex;
        align-items: center;
        justify-content: center;
        width: 24px;
        height: 24px;
        min-width: 24px;
        border-radius: 100%;

    }
}
.channel-flow-val{
    padding: 5px;
    background: #f8f8f8;
}
table > :not(caption) > * > *{
    background-color: #f8f8f8;
}

.label-color{
    label{
        padding: 8px;
    }
    color:$header-blue;
}
.nav-link{
    opacity: 0.6;
    color: #000000;
}

.card-steps 
{
    display: flex;
    justify-content: center;
    .card-steps-inner
    { 
        margin-bottom: 25px;
        display: inline-flex;  
        width: 100%; 
        justify-content: space-between;
        align-items: center;
        .line-grow
        {
            flex: 1;
            background-color: $button-blue;
            height: 1px;
            &.light-line
            {
                background-color: $button-blue;
                opacity: 0.07;
            }
        }
        .btn-primary{
            background-color: rgba(9, 144, 210, 0.05);
            color: $button-blue;
            display: flex;
            align-items: center;
            font-weight: 500;
            font-size: 16px;
            justify-content: center;
            padding: 0;
            position: relative;
            z-index: 2;
        }
        .card-step-btn
        {
            // background-color: rgba(9, 144, 210, 0.05);
            background-color: #E2E2E2;
            border: none;
            border-radius: 100%; 
            color: rgba(0, 0, 0, 0.40);
            width: 42px;
            height: 42px;
            display: flex;
            align-items: center;
            font-weight: 500;
            font-size: 16px;
            justify-content: center;
            padding: 0;
            position: relative; 
            z-index: 2;
            &.visited{
                background-color: #CFF1FF;
                color: $button-blue;
            }
            &.active
            {   
                background-color: $button-blue;
                color: #fff; 
            }
            .closeBtn {
                position: absolute;
                width: 15px;
                height: 16px;
                background: #fff;
                align-items: center;
                display: none;
                justify-content: center;
                background: red;
                border-radius: 100%;
                right: -7px;
                top: -4px;
                img 
                {
                    filter: brightness(0) invert(1);
                    height: 8px;
                }
            }

            &.lastBTN:hover 
            {
                .closeBtn
                {
                    display: flex;
                }
            }

           
        }
    }
}

.system-normal {
    margin-top: 20px;
    background: #f7f7f7;
    padding: 10px;
    span 
    {
        color: #000000;
        font-size: 18px;
        font-weight: 500;
        width: 115px;
        display: inline-block;
        text-align: center;
    }
    strong 
    {
        color: #000000;
        font-size: 28px;
        font-weight: 500;
        display: inline-block;
        width: 115px;
        text-align: center;
    }
}
.borderRight1
{
    position: relative;
    &::after
    {
        height: 100%;
        right: -7px;
        content: "";
        position: absolute;
        width: 1px;
        background-color: $border-color;
        top: 0;
        
    }
}

