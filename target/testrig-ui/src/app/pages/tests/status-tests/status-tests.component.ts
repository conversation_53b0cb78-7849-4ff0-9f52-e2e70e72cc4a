import { CommonModule } from '@angular/common';
import { Component, ElementRef, Input, OnDestroy, OnInit, ViewChild } from '@angular/core';
import { MouldService } from '../../../services/mould.service';
import { MachineService } from '../../../services/machine.service';

import { Channel, Mould, Test } from '../../../models/mould.model';
import { Subscribable, Subscription, forkJoin } from 'rxjs';
import { ActivatedRoute, Router } from '@angular/router';
import { TranslatePipe, TranslateService } from '../../../translate';
@Component({
  selector: 'app-status-tests',
  standalone: true,
  imports: [CommonModule, TranslatePipe],
  templateUrl: './status-tests.component.html',
  styleUrl: './status-tests.component.scss'
})

export class StatusTestsComponent implements OnInit, OnDestroy{
  @ViewChild('scrollContainer') private scrollContainer!: ElementRef;
  @Input() tests!: any;
  @Input() stepperData!: Mould;

  private pressureInterval: any;

  testConfigReport:any ={
    pressureTabs: {},  // To manage the pressure step count of limit (2-9)
    pressure: {
      pressureMin: 0,
      pressureMax: 9,
      pressureVal: 0,
      pressureLoss: 0
    },
    heatTabFlow: {}, // to manage the flow on heat tab only
    outletTemp: 0,
    logs: []
  }

  availableTests: Test[]
  activeTab: string
  activePressure: number = 0
   
  constructor(private mouldService: MouldService,private router:Router, private translate:TranslateService,
     private machineService: MachineService, private route:ActivatedRoute) {
    this.availableTests = [
      { key : 'flow_test', 'value' : false },
      { key : 'air_leak_test', 'value' : false },
      { key : 'water_leak_test', 'value' : false },
      { key : 'heat_test', 'value' : false }
    ]
    this.scrollToBottom()
    this.mouldService.getConfiguration().subscribe({
      next: (config)=>{
        if(config.max_pressure_loss){
          this.testConfigReport.pressure.pressureLoss = config.max_pressure_loss
        }
      },
      error: (err)=>{
        console.log('✌️error --->', err);
      }
      
    })
    const mouldId = this.route.snapshot.paramMap.get('mould-id');
    if(mouldId){
      this.getLogsFromMouldEvents(mouldId)
    }
  }

  ngOnInit(): void { 
    let count = 0
    for (const key in this.tests) {
      if (this.tests.hasOwnProperty(key)) {
        if(this.tests[key] == true){
          this.activeTab = key
          break
        }
        count++
      }
    }

    // Set pressure limit based on selectd mould configuration
    this.testConfigReport.pressure.pressureMin = this.stepperData.pressure_set_point_min
    this.testConfigReport.pressure.pressureMax = this.stepperData.pressure_set_point_max

    this.testConfigReport.pressureTabs = this.testConfigReport.pressureTabs || [];
    // const storedPressureTabs = localStorage.getItem('pressureTabs');
    // if (storedPressureTabs) {
    //   this.testConfigReport.pressureTabs = JSON.parse(storedPressureTabs);
    // }
    
    this.startPressureInterval()
  }
  private transformedDataSubscription: Subscription
  getInletTempFromGlobalBehaviour(){
    this.transformedDataSubscription = this.machineService.transformedData.subscribe((mouldConfig) => {
      if (!mouldConfig) return;
      this.testConfigReport.inletTemp = this.mouldService.tempRoundOf(mouldConfig.inlet_temps)
    });
  }

  /**
   * Settthe logs scroll bar to bottom 
   */
  scrollToBottom(): void {
    try {
      const marginFromBottom = this.scrollContainer.nativeElement.scrollHeight - this.scrollContainer.nativeElement.scrollTop - this.scrollContainer.nativeElement.clientHeight;
      // if(marginFromBottom < 60){
      this.scrollContainer.nativeElement.scrollTop = this.scrollContainer.nativeElement.scrollHeight;
      // }
      setTimeout(()=>{
        this.scrollToBottom()
      }, 400);
    } catch (err) {
      setTimeout(()=>{
        this.scrollToBottom()
      }, 1000);
    }
  }

  /**
   * 
   * @param min To return the loop in dynamic limit
   * @param max 
   * @returns 
   */
  // getRange(min: number, max: number): number[] {
  //   return Array.from({ length: max - min + 1 }, (_, i) => min + i);
  // }
  flowRange:any = []
  getRange(min: number, max: number): number[] {
    // Calculate pressure range
    const pressureRange = max - min;
    
    // Calculate number of steps (clamped between 2 and 8)
    const steps = Math.min(Math.max(Math.floor(pressureRange), 3), 8);
    
    // Calculate step size
    const pressureStep = pressureRange / (steps - 1);
    this.flowRange = Array.from({ length: steps }, (_, i) => parseFloat((min + i * pressureStep).toFixed(2)))
    // Generate the array of pressure values
    return Array.from({ length: steps }, (_, i) => parseFloat((min + i * pressureStep).toFixed(2)));
  }
  getKeys(object:any){
    if(object == undefined){
      return []
    }
    return Object.keys(object)
  }

  /**
   * Verify that its last key of the defined json
   * @param key 
   * @returns 
   */
  // isWithInPressureTab(key: number):number{
  //   if(this.testConfigReport.pressureTab){
  //     const keys = Object.keys(this.testConfigReport.pressureTab);
  //     const lastKey = keys[keys.length - 1];
  //     return Number(lastKey)
  //   }
  //   return 0
  // }

  /**
   * get the current pressure for pressure set point 
   * Along with "Get current values from data API for all channels"
   */
  startPressureInterval(){
    // setTimeout(() => {
      if((window.location.href.includes('tests/'))){
        const mouldId = this.route.snapshot.paramMap.get('mould-id');
        if(!mouldId){
          return this.router.navigate(['/tests'])
        }
        setTimeout(()=>{
        // return true
          forkJoin({
            machineData: this.machineService.getFlowData(),
            pressureData: this.mouldService.readPressure(),
          }).subscribe({
            next: ({
              machineData, pressureData
            }) =>{
              this.machineService.globalShare.next({'test':'running'})
              this.setCompletedTestBasedOnStatus()
              if(pressureData){
                const pressureVal = pressureData.pressure
                this.testConfigReport.pressure.pressureVal = this.mouldService.presusureRoundOf(pressureVal)
              }
              if(machineData){
                const currentTest = this.mouldService.mouldTestStatus?.state.split(':')[1]
                if(currentTest == 'flow_test'){
                  // console.log('flowRange',this.flowRange)
                  let presureSetPoint = this.testConfigReport.pressure.pressureVal.toString()
                  if(this.availableTests[0]){
                    this.activePressure = presureSetPoint
                  }
                  let enabledMachineData:any = {}
                  this.stepperData.channels.forEach((value)=>{
                    if(value.channel_enabled == true){
                      enabledMachineData[value.channel_id] = machineData[value.channel_id]
                    }
                  })
                    const matchedRangeValue = this.flowRange.find((rangeValue: number, index: number, array: number[]) => {
                    if (index < array.length - 1) {
                      const nextRangeValue = array[index + 1];
                      // console.log((parseFloat(presureSetPoint)+0.06),rangeValue,nextRangeValue)
                      if ((parseFloat(presureSetPoint)+0.06) > rangeValue && (parseFloat(presureSetPoint)+0.06) < nextRangeValue) {
                      return true;
                      }
                    }
                    return Math.abs(rangeValue - parseFloat(presureSetPoint)) <= 0.05;
                    });
                  // console.log('matchedRangeValue',this.flowRange,matchedRangeValue,presureSetPoint)
                  if (matchedRangeValue !== undefined) {
                    this.activePressure = matchedRangeValue;
                    
                    this.testConfigReport.pressureTabs[matchedRangeValue] = enabledMachineData;
                    localStorage.setItem('pressureTab', JSON.stringify(this.testConfigReport.pressureTabs));
                  }else{
                    this.activePressure = 0;
                    this.testConfigReport.pressureTabs[0] = enabledMachineData;
                  }
                }else{
                  const storedPressureTabs = localStorage.getItem('pressureTab');
                  if (storedPressureTabs) {
                    this.testConfigReport.pressureTabs = JSON.parse(storedPressureTabs);
                  }
                  if(currentTest == 'heat_test'){
                    this.getOutletTemperature()
                    let enabledMachineData:any = {}
                    this.stepperData.channels.forEach((value)=>{
                      if(value.channel_enabled == true){
                        enabledMachineData[value.channel_id] = machineData[value.channel_id]
                      }
                    })
                    this.testConfigReport.heatTabFlow = enabledMachineData
                    localStorage.setItem('heatTabFlow', JSON.stringify(this.testConfigReport.heatTabFlow));
                  }else{
                    const storedHeatTabFlow = localStorage.getItem('heatTabFlow');
                    if (storedHeatTabFlow) {
                      this.testConfigReport.heatTabFlow = JSON.parse(storedHeatTabFlow);
                    } 
                  }
                }
              }
              if(machineData || pressureData){
                this.startPressureInterval()
              }
            },
            error: (err) => {
            }
          })
        },1000);
      }
      return
    // },1000);
    
  }

  /**
   * verify that channels are enabled are all present in the data 
   */
  verifyEnabledChannels(){
    const enabledChannels: Channel[] = this.stepperData.channels.filter((channel:Channel) => channel.channel_enabled);
    const pressureChannels = this.testConfigReport.pressureTabs[this.testConfigReport.pressureTabs.length - 1].channels;
    enabledChannels.forEach(channel => {
      const exists = pressureChannels.some((pChannel:any)=> pChannel.id === channel.channel_id);
      if (!exists) {
        pressureChannels.push({
            id: channel.channel_id,
            channe_name: channel.channel_name,
            flow_val: 0
        });
      }
    });

    // 2️⃣ Remove disabled channels
    this.testConfigReport.pressureTabs[this.testConfigReport.pressureTabs.length - 1].channels = pressureChannels.filter((pChannel: any) =>
    this.stepperData.channels.some((channel: Channel) => channel.channel_id === pChannel.id && channel.channel_enabled)
    );
  }
  userInput:boolean = false
  public setCompletedTestBasedOnStatus(){
    if(!this.mouldService.mouldTestStatus?.state.includes('idle')){
      const currentTest = this.mouldService.mouldTestStatus?.state.split(':')[1]
      
      if(!this.userInput){
        this.activeTab = currentTest
      }
      if(currentTest === 'flow_test'){
        this.availableTests[0].value = false
        this.availableTests[1].value = false
        this.availableTests[2].value = false
        this.availableTests[3].value = false
      }else if(currentTest === 'air_leak_test'){
        this.availableTests[0].value = true
        this.availableTests[1].value = false
        this.availableTests[2].value = false
        this.availableTests[3].value = false
      }else if(currentTest === 'water_leak_test'){
        this.availableTests[0].value = true
        this.availableTests[1].value = true
        this.availableTests[2].value = false
        this.availableTests[3].value = false
      }else if(currentTest === 'heat_test'){
        this.availableTests[0].value = true
        this.availableTests[1].value = true
        this.availableTests[2].value = true
        this.availableTests[3].value = false
      }
      // console.log('✌️this.availableTests --->', this.availableTests);
    }else{
      if(this.testConfigReport.logs.length > 0){

        this.availableTests[0].value = true
        this.availableTests[1].value = true
        this.availableTests[2].value = true
        this.availableTests[3].value = true
      }
    }
  } 

  public logsEventTimeoutId: any
  
  /**
   * Get logs from mould events for a specific mould ID
   * @param mouldId The ID of the mould
   */
  onceStarted = false
  getLogsFromMouldEvents(mouldId: string,lastLog:boolean = false): void {
    // console.log(this.mouldService.mouldTestStatus?.state.includes('idle'),this.onceStarted,this.testConfigReport.logs.length)
    if(!this.mouldService.mouldTestStatus?.state.includes('idle') || (this.mouldService.mouldTestStatus?.state.includes('idle') && this.onceStarted && this.testConfigReport.logs.length > 0 && !lastLog)){
      this.onceStarted = true
      this.mouldService.getMouldCompleteEventsLogs(mouldId).subscribe({
        next: (mouldEvents: any) => {
        if (mouldEvents && mouldEvents?.mould_events) {
          this.testConfigReport.logs = []
          const newLog = mouldEvents?.mould_events.map((log:string) => log.replace(/_/g, ' '))
          newLog.forEach((currectLog:string) => {
            this.testStopOrError(currectLog)
            
              currectLog = currectLog.toLowerCase()
              let anyNumber:any = currectLog.match(/[\d.]+/); // matches integers or decimals like 1, 1.0, 0.25
              if(anyNumber){
                currectLog = currectLog.replace(anyNumber,'{{value}}')
                this.testConfigReport.logs.push(this.translate.instant(currectLog,anyNumber))
              }else{
                this.testConfigReport.logs.push(this.translate.instant(currectLog.toLowerCase()));
              }
            
          })
          }
          this.logsEventTimeoutId = setTimeout(() => {
            if(this.mouldService.mouldTestStatus?.state.includes('idle') && this.testConfigReport.logs.length > 0 && !lastLog){
              lastLog = true
            }
            this.getLogsFromMouldEvents(mouldId,lastLog);
          }, 1000);
        },
        error: (error) => {
          console.log('✌️error --->', error);
        }
      });
    }else{
      if(!this.onceStarted){
        this.logsEventTimeoutId = setTimeout(() => {
          this.getLogsFromMouldEvents(mouldId);
        }, 1000);
      }
    }
  }

  testStopped:any = {}
  testError = false
  /**
   * To verify that test is stopped or getting eny error in between
   */
  testStopOrError(log: string){
    const currentTest = this.mouldService.mouldTestStatus?.state.split(':')[1]
    if(log.includes('error') || log.includes(" stop")){
      this.testStopped[currentTest] = true
      this.testError = true
      return true
    }
    this.testStopped[currentTest] = false
    return false
  }
  /**
   * Get latest value for the outlet temp from read outlet temprature API
   */
  private outletTempSub!: Subscription;
  getOutletTemperature(){
    this.outletTempSub = this.mouldService.getoutletTemperature().subscribe({
      next: (response) => {
        if(response){
          this.testConfigReport.outletTemp = this.mouldService.tempRoundOf(response.outlet)
        }
        setTimeout(() => {
          this.getOutletTemperature()
        }, 1000)
      },
      error: (err) => {}
    })
  }

  ngOnDestroy(): void {
    clearInterval(this.pressureInterval);
    if (this.logsEventTimeoutId) {
      clearTimeout(this.logsEventTimeoutId);
    }

    // this.transformedDataSubscription.unsubscribe()
  }

}
