import { Routes } from '@angular/router';
import { authGuard } from './guards/auth.guard';
import { LoginComponent } from './account/login/login.component';

export const routes: Routes = [
    {
        path: 'controls',
        canActivate: [authGuard],
        loadComponent: () =>
        import("./pages/controls/controls.component").then((component)=>component.ControlsComponent)
    },
    {
        path: 'mould',
        canActivate: [authGuard],
        loadComponent: () =>
        import("./pages/mould-settings/mould-settings.component").then((component)=>component.MouldSettingsComponent)
    },
    {
        path: 'mould/new',
        canActivate: [authGuard],
        loadComponent: () =>
        import("./pages/mould-settings/new-edit-mould/new-edit-mould.component").then((component)=>component.NewEditMouldComponent)
    },
    {
        path: 'mould/edit/:mould-id',
        canActivate: [authGuard],
        loadComponent: () =>
        import("./pages/mould-settings/new-edit-mould/new-edit-mould.component").then((component)=>component.NewEditMouldComponent)
    },
    {
        path: 'tests',
        canActivate: [authGuard],
        loadComponent: () =>
        import("./pages/tests/tests.component").then((component)=>component.TestsComponent)
    },
    {
        path: 'settings',
        canActivate: [authGuard],
        loadComponent: () =>
        import("./pages/config/config-settings.component").then((component)=>component.ConfigSettingsComponent)
    },
    {
        path: 'settings/pump-config',
        canActivate: [authGuard],
        loadComponent: () =>
        import("./pages/config/pump-valve/pump-valve.component").then((component)=>component.PumpValveComponent)
    },
    {
        path: 'settings/setting-config',
        canActivate: [authGuard],
        loadComponent: () =>
        import("./pages/config/mould-configuration/mould-configuration.component").then((component)=>component.MouldConfigurationComponent)
    },
    {
        path: 'tests/:mould-id', // Add the dynamic parameter ":id"
        canActivate: [authGuard],
        loadComponent: () =>
        import("./pages/tests/mould-tests/mould-tests.component").then((component)=>component.MouldTestsComponent)
    },
    {
        path: 'tests/report/:mould-id', // Add the dynamic parameter ":id"
        canActivate: [authGuard],
        loadComponent: () =>
        import("./pages/tests/report-mould/report-mould.component").then((component)=>component.ReportMouldComponent)
    },
    {
        path: 'login',
        component: LoginComponent
    },
    { path: '**', redirectTo: '/login' }
];
