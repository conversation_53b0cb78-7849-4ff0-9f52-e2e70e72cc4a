// import { ChartDataSets } from 'chart.js';
// import { Label } from 'ng2-charts';

import { float } from '@zxing/library/esm/customTypings';
import { Status } from '../utils/enums';

export interface Machine {
  _id: ID;
  mac: string;
  name: string;
  status?: string;
  manifolds: Manifold[];
  ui_expand?: boolean;
  ui_status?: Status;
}

export interface ID {
  $oid: string;
}

export interface Limits {
  temp: LimitTemp;
  pressure: LimiPressure;
}

export interface LimiPressure {
  inlet: LimitItem;
  delta: LimitItem;
}

export interface LimitItem {
  max: number;
  min: number;
  enabled: boolean;
}

export interface LimitTemp {
  max: number;
  min: number;
  enabled: boolean;
}

export interface Manifold {
  mac: string;
  pressureChannels: number;
  flowChannels: number;
  channels: ManifoldChannel[];
  name?: string;
  ui_status?: Status;
  channel_no?: number;
  sensor_types?: number;
  pressure_enabled?: boolean;
  limits?: Limits;
}

export interface ManifoldChannel {
  channel_id: number;
  channel_name: string;
  channel_lower_limit: number,
  channel_upper_limit: number,
  channel_enabled: number,
  channel_diameter: any;
  ui_status?: Status;
}

export interface Sensor {
  min: number;
  max: number;
  limits_enabled: boolean;
  type: Type;
  ui_status?: Status;
}

export enum Type {
  Flow = "flow",
  Pressure = "pressure",
  Temperature = "temperature"
}

export interface MacStatus {
  mac: string;
  status: string;
}

export interface ChannelData {
  timestamp: string;
  flow: number[];
  temperature: number[];
}

export interface ManifoldData {
  mac: string;
  channels: number;
  channeldata: ChannelData[];
}

export interface Installation {
  machinemac: string;
  manifoldmac: string;
}

export interface Production {
  mouldname: string;
  machinemac: string;
}

export interface Language {
  flag: string;
  language: string
}


export interface ChannelEnergy {
  id: number;
  flowValue: float;
  deltaT: float;
  totalEnergy: float;
}

export interface ChannelsEnergyData {
  channels: ChannelEnergy[];
}