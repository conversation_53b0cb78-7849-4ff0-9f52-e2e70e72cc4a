// export interface Mould {
//     moudl_id: string;
//     mould_name: string;
//     time: String
// }


export interface Channel {
    channel_id: number;
    channel_name: string;
    channel_lower_limit: number;
    channel_upper_limit: number;
    channel_enabled: boolean;
    channel_diameter: string;
}

export interface Mould {
    mouldid: string;
    time: string;
    comments: string;
    pressure_set_point_min: number;
    pressure_set_point_max: number;
    enable_all?: boolean;
    channels: Channel[];
    max_water_pressure: number;
    tests : {
        flow_test: boolean,
        air_leak_test: boolean,
        water_leak_test: boolean,
        heat_test: boolean
    }
}

export interface Test { 
    key :string, 
    value : boolean 
}


export interface Configuration {
    max_water_pressure: number  
    flow_stabilization_timeout: number
    flow_stabilization_limit: number
    max_pressure_loss: number
    pressure_stabilization_timeout: number
    pressure_measure_time: number
    tcu_temperature: number
    // cooling_cutoff_temperature: number
    location: string
    user_language: string
    language: String[]
}