import { Status } from "../utils/enums";

export interface Channel {
  id: number;
  values: {
    temp: number;
    sensor_val: number;
  };
  ui_status: Status;
}

export interface CurrentData {
  time?: string;
  inlet_temps: number[];
  channels: Channel[];
  pressure_in: number;
  pressure_out: number;
  ui_machine_mac?: string;
  ui_manifold_mac?: string;
  mac?:string;
}

export interface CurrentMachine {
  mac: string,
  manifolds: Array<any>
}
