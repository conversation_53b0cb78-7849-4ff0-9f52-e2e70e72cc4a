<header *ngIf="!tokenFromLocalStorage">
  <!-- <header [hidden]="false"> -->
  <div class="d-flex justify-content-between align-items-center">

    <div>
      <a class="logo mouldlive-logo" style="cursor: pointer;" [attr.aria-label]="'Logo'" [routerLink]="['/controls']">
          <img src="assets/images/logo.svg" alt="Logo"/>
          
      </a><br>
      <div class="float-end">v1.0.0</div>

    </div>
    <div *ngIf="badgesFOL" class=" position-absolute" style="left: 250px">
      <span (click)="badgesFOL = false" class="emergance-pressed" > <span class="blink">{{"Flow Out Of Limits"|translate}}</span> &nbsp;&nbsp;&nbsp;<span class="cursor-pointer">x</span></span>
    </div>
    <div class="tabs" >
      <a *ngFor="let item of manifoldTabs"  [ngClass]="{ 'active': isActive(item.path) }" [href]="[item.path]"
        [hidden]="checkTemperatureFlow(item.label)">
        {{ item.label | translate}} 
        <div class="line"></div>
      </a>
    </div>
    <div class="d-flex align-items-center gap-3 position-relative">
        <!-- <div *ngFor="let badge of badges" [hidden]="!badge.visible">
          <span class="emergance-pressed" > {{ badge.banner }}</span>
        </div> -->
     
      <span class="position-absolute d-flex gap-2" style="left: -1155px">
        <span (click)="removeBadge(machineService.badges[badge].alias)"  *ngFor="let badge of getKeys(machineService.badges)">
          <span  class="emergance-pressed" *ngIf="machineService.badges[badge].visible == true">
            {{ machineService.badges[badge].message | translate}}
          </span>
        </span>
      </span>
      <span class="position-absolute d-flex">
        <span class="blink emergance-pressed" style="left: -220px" *ngIf="verifyEmergencyAlert" >
          {{ "Emergence Button Pressed" | translate }}
        </span>
        <span class="blink-test cursor-pointer" (click)="testScreen()" *ngIf="testStatus" 
          [style]="verifyEmergencyAlert? 'left: -570px': ' left: -140px'" >
          <span class="blink">
            {{ "Test Running" | translate }}
          </span>
        </span>
        <!-- <span class="emergance-pressed" style="right: 100px" *ngIf="!verifyEmergencyAlert">
          Filter Pressure Overflow
        </span> -->
      </span>
      <div class="pump-ready" [ngClass]="waterPumpStatus? ' active' : ' '" >
        {{ pumpStatus | translate }}
        <!-- {{ "Pump Not Ready" | translate}} -->
      </div>

      <div>
        <!-- <div class="loader-bar opacity-75" *ngIf="button.support == 'Processing...'"></div> -->
        <button id="support-btn" class="btn support-btn support-icon" [ngClass]="button.support == 'Online'?' support-start': ' '"
        [disabled]="button.support == 'Processing...'"
        (click)="supportStartStop(button.support)">
        <div>
          <!-- <span class="img-offline" *ngIf="button.support != 'Online'"></span> -->
          <img src="assets/images/sprt.png" width="20px" alt="pump"/>
        </div> 
        <div class="support-label" style="min-width: 102px;"> 
          <span *ngIf="button.support != 'Processing...'">
            <span *ngIf="button.support == 'Online'">{{ 'Support Online' |translate }}</span>
            <span *ngIf="button.support != 'Online'">{{ 'Need Support ?' |translate }}</span>
          </span>
          <span class="loader-bar opacity-75" *ngIf="button.support == 'Processing...'" ></span> 
      </div>
        </button>
      </div>
      <button class="btn btn-success" (click)="purgeWater()" 
        [ngClass]="mouldService.purgeButton == 'Stop Purge'? ' stop-purge' : ''"
        [disabled]="mouldService.purgeButton == 'Processing...'"> {{ mouldService.purgeButton | translate }} </button>
      <!-- <button class="btn btn-style btn-outline-ssuccess" >Pump Ready </button> -->
      <div class="dropdown flag-dropdown">
        <button class="dropdown-toggle notranslate rig-button" 
            type="button" id="dropdownProfile" data-bs-toggle="dropdown" aria-expanded="false">
            <img src="assets/images/user.svg" alt="user"/>      {{ userName }}
        </button>
        <ul class="dropdown-menu" aria-labelledby="dropdownProfile">
          <!-- <li> 
            <img src="assets/images/key.svg" width="20px" />
            Change Password 
          </li> -->
          <li *ngIf="superUser == true">
            <a href="javascript:void(0);"  class="a-tag w-100" [href]="['/settings']">
              <img src="assets/images/settingicon.svg" width="20px" />
              {{"Settings" | translate}}   
            </a>
          </li>
          <li>  
            <a href="javascript:void(0);" (click)="logout()" class="a-tag w-100">
              <img src="assets/images/logout.svg" width="20px" />
              {{"Logout" | translate}}
            </a>
          </li>
        </ul>
      </div>
    </div>
  
  </div>
</header>
