@import '../../../styles/variables';

.app-header {
  background-color: $white;
  position: relative;
  z-index: 99;
  padding: 15px 30px 10px 15px;
  margin-bottom: 1px;
  @media screen and (max-width:767px)
  {
    padding-top: 10px;
    padding-right: 15px;
  }
}

.title {
  font-size: 24px;
  white-space: nowrap;
  font-weight: bold;
  @media screen and (max-width:767px)
  {
    font-size: 15px;
  }
}

.toggle-icon {
  width: 40px;
  height: 40px;
  display: flex;
  cursor: pointer;
  align-items: center;
  justify-content: center;
  border-radius: 100%;
  color: #000;
  font-size: 22px;
  @media screen and (max-width:767px)
  {
    width: auto;
  }
  &:hover 
  {
    color: $button-blue;
  }
  .fa-bars 
  {
    display: flex;
  }
  .fa-times
  {
    -webkit-text-stroke: 1px #f4f7f8;
  }
  &.active
  {
     .fa-bars
      {
        display: flex;
        -webkit-text-stroke: 0.5px #f4f7f8;
      }

      .fa-times
      {
        display: none;
      }
  }
}



.app-breadcrumb {
  .dot {
    display: flex;
    align-self: center;
    justify-content: center;
    width: 24px;

    &:before {
      display: block;
      width: 2px;
      height: 2px;
      border-radius: 50%;
      content: ' ';
      background: $green;
    }
  }

  a {
    &:last-child {
      color: $grey;
    }
  }
}


.head{
  button.btn {
    width: 42px;
    height: 42px;
    border-radius: 50px;
    background: #009371;
    i {
      font-size: 14px;
      color: #fff;
    }
    @media screen and (max-width:767px)
    {
      width: 30px;
      height: 30px;
      padding: 0;
    }
  }
  div {
    margin-left: 15px;
    @media screen and (max-width:767px)
    {
      margin-left: 7px;
    }
    span{
      font-size: 13px;
      color: #001D3E;
      text-transform: capitalize;
      font-weight: 600;
    }
    h5 {
      margin: 0;
      font-size: 13px;
      color: #001D3E;
      max-width: 150px;
      overflow: hidden;
      text-overflow: ellipsis;
    }
  }
}



.mould-modal .modal-header h4 {
  font-size: 18px;
  font-weight: 600;
}

.mould-modal .modal-header button {
  opacity: 1;
  width: 17px;
  height: 17px;
  padding: 0;
  margin: 5px 0;
}

.modal-dialog .modal-content {
  border-radius: 10px !important;
  border: none !important;
}

.mould-modal .modal-header {
  border-width: 2px;
  border-color: #ededed;
  padding: 15px 20px;
}

.mould-modal .modal-body {
  padding: 15px 20px;
}

.mould-modal .modal-body .form-group label {
  font-size: 14px;
  font-weight: 500;
}

.mould-modal .modal-body .form-group .form-control {
  border-radius: 50px;
}

.mould-modal .modal-body .form-group button.btn {
  display: flex;
  align-items: center;
  column-gap: 6px;
  text-transform: capitalize;
  border-radius: 50px;
  min-width: 90px;
  min-height: 38px;
  justify-content: center;
  background: $green;
  font-size: 14px;
  color: #fff;
}

.mould-modal .modal-body .form-group div {
  column-gap: 10px;
}

.mould-modal .modal-body .form-group button.btn.remove {
  background: #db504a;
}

.btn.cancel{
  background: #db504a !important;
  color:#fff;
}

.overLaping{
  z-index: 999 !important;
}

select{
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.multilevel-dropdown .multilevel-select {
  position: relative;
  display: block;
  width: 100%;
  height: calc(1.5em + 0.75rem + 2px);
  padding: 0.375rem 0.75rem;
  font-size: 1rem;
  font-weight: 400;
  line-height: 1.5;
  color: #495057;
  background-color: #fff;
  background-clip: padding-box;
  border: 1px solid #ced4da;
  border-radius: 50px;
  transition: border-color .15s ease-in-out,box-shadow .15s ease-in-out;
  z-index: 9;
}
.multilevel-dropdown 
{
  position: relative;
  flex: 1;
}
.multilevel-dropdown .nestedDropdown
{
  position: absolute;
  top: 100%;
  max-height: 300px;
  width: 100%;
  z-index: 9;
  overflow: auto;
}


.create-user-modal
{
    .modal-header
    {
        border-width: 2px;
        border-color: #ededed;
        padding: 15px 20px;
        h4 
        {
            font-size: 18px;
            font-weight: 600;
        }
        button 
        {
            opacity: 1;
            width: 17px;
            height: 17px;
            padding: 0;
            margin: 5px 0;
        }
        
    }
    label 
    {
        font-size: 14px;
    font-weight: 500;
    padding-top: 0;
    }
    input 
    {
        border-radius: 50px;
    }
    .password-icon 
    {
        position: relative;
        .input-group-append {
            position: absolute;
            right: 15px;
            top: 0;
            height: 38px;
        }
        span.input-group-text {
            border-radius: 0 50px 50px 0;
            padding-right: 15px;
            padding-left: 15px;
        }
        input 
        {
            padding-right: 60px;
        }
    }
   
    .btn 
    {
        display: flex;
        align-items: center;
        -moz-column-gap: 6px;
        column-gap: 6px;
        text-transform: capitalize;
        border-radius: 50px;
        min-width: 90px;
        min-height: 38px;
        justify-content: center;
        background: $green;
        font-size: 14px;
        color: #fff;
    }
}
.select-mould 
{
  @media screen and (max-width:1100px)
  {
    padding-bottom: 40px;
    .tabs 
    {
      position: absolute;
      bottom: 0;
    }
  }
}
.header-btn 
{
  button 
  {
    @media screen and (max-width:767px)
    {
      padding-left: 7px;
      padding-right: 7px;
      font-size: 12px;
    }
  }
}



button#dropdownLanguageChange {
  width: 100% !important;
  border: 1px solid #ced4da;
  border-radius: 50px;
  text-align: left;
  display: flex;
  align-items: center;
}

button#dropdownLanguageChange::after{
  margin-left: auto;
}

.flag-dropdown {
  width: 100%;
  margin:0;
}

.flag-dropdown .dropdown-menu{
  width: 100%;
}

.flag-dropdown #dropdownProfile ~ .dropdown-menu{
  width: auto;
}
.btn.add-machine {
  background: #0090ce;
  border-radius: 100%;
  color: #fff;
  padding: 0;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-top: 6px;
  margin-right: 5px;
  cursor: pointer;
}

.btn.add-machine:focus {
  outline: none;
}

.btn.add-machine i {
  color: #fff !important;
}

.header-style{
  padding-top: 10px;
  padding-bottom: 20px;
}
header 
{
  background-color: #fff;
  padding:0 30px 0;
  .btn-success 
  {
    height: 38px;
  }
}
.pump-ready {
  white-space: nowrap;
  border: 1px solid red;
  background: transparent;
  position: relative;
  border-radius: 50px;
  padding: 0 15px 0 25px;
  height: 30px;
  font-size: 16px;
  color: red;
  // min-width: 138px;
  display: flex;
  align-items: center;
  &::before{
      width: 8px;
      height: 8px;
      border-radius: 100%;
      content: '';
      background-color: red;
      position: absolute;
      top: 10px;
      left: 12px;
  }
  &.active
  {
    border-color: $green;
    color: $green;
    &::before
    {
      background-color: $green;
    }
  }
}


.emergance-pressed {
  white-space: nowrap;
  border: 1px solid red;
  background: transparent;
  position: relative;
  border-radius: 50px;
  padding: 1px 15px 1px 15px;
  height: 30px;
  font-size: 16px;
  color: red;
  display: flex;
  align-items: center;
}
.blink-test{
  white-space: nowrap;
  border: 1px solid $button-blue;
  background: rgba(111, 207, 255, 0.1);
  position: relative;
  border-radius: 50px;
  padding: 1px 15px 1px 15px;
  height: 30px;
  font-size: 16px;
  color: $button-blue;
  display: flex;
  align-items: center;
}

.tabs a
{
  font-size: 18px;
  text-decoration: none;
  color: #4E4E4E;
  padding: 23px 20px 23px;
  border-bottom: 4px solid transparent;
  display: inline-block;
  &.active
  {
      border-color: $button-blue;
      color: #000000;
      font-weight: 500;
  }
} 
/* HTML: <div class="loader"></div> */
.stop-purge{
  background-color: $button-blue;
  border-bottom: $button-blue;
}
.loader-bar {
  width: 8px;
  aspect-ratio: 1;
  border-radius: 50%;
  position: fixed;
  right: 64px;
  top: 104px;
  z-index: 99;
  animation: l5 1s infinite linear alternate;
}
@keyframes l5 {
  0%  {box-shadow: 15px 0 gray, -15px 0 #80808096; background: gray;}
  33% {box-shadow: 15px 0 gray, -15px 0 #80808096; background: #80808096;}
  66% {box-shadow: 15px 0 #80808096, -15px 0 gray; background: #80808096;}
  100%{box-shadow: 15px 0 #80808096, -15px 0 gray; background: gray;}
}

.support-btn {
  // background-color: #80808096 !important;
  background-color: #d8d8d8 !important;
  // min-width: 0px;
  border-radius: 40%;
  border-color: #d8d8d8 !important;
  border-radius:  10px 0 0 10px !important; /* Pill shape */
  padding: 0.75rem 1.5rem !important;
  position: fixed;
  right: 0px;
  top: 83px;
  z-index: 99;
  overflow: hidden;
  &.support-icon
  {
    padding: 0 10px!important;
    min-width: 150px;
    height: 50px;
    margin: 0;
  }
  &.support-start{
      color: white;
      background-color: $green !important;
      border-color: $green;
  }
  img{
    filter: brightness(0) invert(1);
  }

}

.support-label{
  color: white;
}

.img-offline {
  position: absolute;
  height: 1.5px;
  width: 30px;
  background: white;
  transform: rotate(45deg);
}