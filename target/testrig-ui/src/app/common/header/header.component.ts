import Notiflix from 'notiflix';
import { CommonModule } from '@angular/common';
import { ROUTING_NAME } from '../../utils/routing';
import { Router, RouterModule } from '@angular/router';
import { AuthService } from '../../services/auth.service';
import { MouldService } from '../../services/mould.service';
import { AfterViewChecked, Component } from '@angular/core';
import { MachineService } from '../../services/machine.service';
import { machine as dummyMachine} from '../../pages/controls/dummydata';
import { ApiService } from '../../services/api.service';
import { TranslatePipe, TranslateService } from '../../translate';

@Component({
  selector: 'app-header',
  standalone: true,
  imports: [CommonModule, RouterModule, TranslatePipe],
  templateUrl: './header.component.html',
  styleUrl: './header.component.scss'
})
export class HeaderComponent implements AfterViewChecked{
  button:any = {
    'support': 'Offline',
  }
  superUser: boolean // to verify that loged in user is super or not 
  public userName: string = ''  // User name
  // tabs label and path
  public manifoldTabs = [
    { label: 'Overview', selected: false, path: ROUTING_NAME.CONTROLS },
    { label: 'Tests & Reports', selected: false, path: ROUTING_NAME.TESTS },
    { label: 'Mould Settings', selected: false, path: ROUTING_NAME.SETTINGS },
    // { label: 'Heat Test', selected: false, path: ROUTING_NAME.HEAT }
  ];
  
  // To get user name and user role from localStorage
  ngAfterViewChecked() {
    if(this.authService.isLoggedIn()){
      setTimeout(() => {
        this.getUserNameFromLocal();
        this.superUser = this.machineService.getUserRole();
      },500);
    }
  }
  badgesFOL:any = false
  constructor( private authService: AuthService, private router: Router, public mouldService: MouldService,
    public machineService: MachineService, private translate: TranslateService){ 
      this.checkSupportBtnStatus()
      this.machineService.globalShare.subscribe((data)=>{
        this.machineService.badges.FOL.visible = data['alert-FOL'] ? true : false
        if(data['alert-FOL'] == true){
          this.badgesFOL = this.machineService.badges.FOL.visible 
        }
        // if(this.badgesFOL){
        //  setTimeout(() => {
        //   this.badgesFOL = false
        //  },5000)
        // }
      })
  }
  
  removeBadge(alias:string){
    if(alias == 'FOL'){
      this.machineService.badges[alias].visible = false
    }
  }
  /**
   * Function to get the user name from local storage 
   */
  getUserNameFromLocal(){
    const storedUser = localStorage.getItem('username');
    if (storedUser) {
      try {
        this.userName = JSON.parse(storedUser);
      } catch (error) {
        this.userName = '';
      }
    } else 
      this.userName = ''; 
  }

  /**
   * Function to handle the logout by clearing storage and redirecting to login page
   */
  logout(): void{
    localStorage.removeItem('Bearer Token');
    localStorage.removeItem('username');
    this.router.navigate(['/login']);
    Notiflix.Notify.success(this.translate.instant('Log out successfully!'));
  }

  get waterPumpStatus(){
    return this.machineService.waterTankStatus.pumpStatus
  }

  get pumpStatus(){
    return this.waterPumpStatus? 'Pump Ready': 'Pump Not Ready'
  }
  
  /**
   * Function to check if the user is logged in
   */
  get tokenFromLocalStorage(){
    return this.authService.isLoggedIn()?  false: true 
  }

  /**
   * Get function get data from service and return the machine mac address
   */
  public get machineMac(): string {
    return dummyMachine?.mac;
  }

  /**
   * Get function to get data from service and return the manifold mac address 
   */
  // public get manifoldMac(): string {
  //   return dummyManifold?.mac;
  // }

  /**
   * this function checks that pressure and temperature data available or not 
   * @param tab tab name
   */
  checkTemperatureFlow(tab: string){
    // if(tab == 'Charts' ){
      // return this.machineService.manifold?.channels.length > 1? false: true;
      // return dummyManifold?.channels.length > 1? false: true;
    // }
    return false
  }

  /**
   * Navigate to test screen with selected mould
   */
  testScreen(){
    this.router.navigate([ROUTING_NAME.TESTS]);
  }

  /**
   * Redirects the user to the mould settings this is also the landing page as well.
   */
  redirectToMouldSettings(): void {
    this.router.navigate(['/mould']);
  }

  /**
   * To verify that emergency button is pressed or not
   */
  get verifyEmergencyAlert(){
    return this.machineService.emergencyButtonPressed
  }

  /**
   * To check that test is in running state or not
   */
  public get testStatus(): boolean {
    return (this.mouldService.mouldTestStatus?.state.includes('idle') || this.mouldService.mouldTestStatus?.state.includes('verifying')) ? false : true
  }

  getKeys(object:any){
    try{
      return Object.keys(object)
    }catch{
      return null
    }
  }


  
  /**
     * to start and stop support button 
     * @param buttonType 
     */
  supportStartStop(buttonType:string){
    this.button.support = 'Processing...'
    const action = buttonType == 'Online'? 'stop' : 'start';
    this.mouldService.supportStartStop(action).subscribe({
      next: (support) =>{
        if(support){
          this.button.support = buttonType != 'Online'? 'Online' : 'Offline';
          // Notiflix.Notify.success(support.message);
        }else{
          this.button.support = buttonType == 'Online'? 'Online' : 'Offline';
        }
      },
      error: (error) => {
        Notiflix.Notify.failure(`Unable to ${action} the support`);
        this.button.support = buttonType == 'Online'? 'Online' : 'Offline';
      }
    })
  }

  /**
   * To verify Purge water and also to stop purgeing water
   */
  purgeWater(){
    const currentPurgeState = this.mouldService.purgeButton
    this.mouldService.purgeButton = 'Processing...'
    const action = currentPurgeState == 'Stop Purge'? 'stop': 'start';
    this.mouldService.purgeWater(action).subscribe({
      next: (purge) => {
        this.mouldService.purgeButton = currentPurgeState == 'Stop Purge'? 'Purge Water': 'Stop Purge';
        this.mouldService.verifyPurgeStatus()
      },
      error: (error) => {
        // Notiflix.Notify.failure(this.translate.instant('Unable to purge water'));
        this.mouldService.purgeButton = currentPurgeState != 'Stop Purge'? 'Purge Water': 'Stop Purge';
      }
    })
  }

  /**
   * To verify the status of support button
   */
  checkSupportBtnStatus(){
    if(this.authService.isLoggedIn()){
      this.mouldService.supportBtnStatus().subscribe({
        next: (status)=>{
          if(status.status == 'success')
            this.button.support = status.service_status == 'active'? 'Online' : 'Offline';
          else
            Notiflix.Notify.failure(this.translate.instant('Unable to get the support status'));
        }, error: (error)=>{
          // Notiflix.Notify.failure(this.translate.instant('Something went wrong with support status API!'));
        }
      });
    }else{
      setTimeout(() => { this.checkSupportBtnStatus() }, 1000)
    }
  }

  isActive(path: string): boolean {
    if(path == ROUTING_NAME.CONTROLS && this.router.url.includes(path))
      return true;
    else if(path == ROUTING_NAME.TESTS && this.router.url.includes(path))
      return true;
    else if(path == ROUTING_NAME.SETTINGS && this.router.url.includes(path))
      return true
    return false
  }

}