import { HttpInterceptorFn, HttpResponse } from '@angular/common/http';
import { inject } from '@angular/core';
import { Router } from '@angular/router';
import Notiflix from 'notiflix';
import { of, tap } from 'rxjs';
import { TranslateService } from '../translate';

export const authInterceptor: HttpInterceptorFn = (req, next) => {
  const router = inject(Router)
  let token 
  const translate = inject(TranslateService);
  if(localStorage.getItem('Bearer Token')){
    token = JSON.parse(localStorage.getItem('Bearer Token') || '')
  }
  
  // If request URL contains '127.0.0.1', return the response from a local JSON file
  if(req.url.includes('127.0.0.1')){
    const folderPath = '/var/www/projects/Test-rig/big-testriglive-1/target/testrig-ui/src/app/dumy_api_responses/';
    const endpointFilePath = `${folderPath}token.js`;
    
  }
  console.log('req.url :', req.url);
  if (req.url.includes('http://192.168.65.81:8000/users')) {
    const dummyData = {
      "users": [
        {
          "username": "John",
          "password": "Mouldflo100%",
          "role": "admin"
        },
        {
          "username": "Danny",
          "password": "Mouldflo200%",
          "role": "user"
        }
      ]
    }
    console.log('dummyData @@@@@@@@@@@@@@@@@@@@@@@:', dummyData);

    return of(new HttpResponse({
      status: 200,
      body: dummyData
    }));
  }
  
  

  const clonedReq = req.clone({
    setHeaders: {
      Authorization: `Bearer ${token ? token : ''}`
    }
  });

  return next(clonedReq).pipe( tap(() => {}, (err:any) => {
    if(err.status === 401){
      localStorage.clear();
      router.navigate(['/login']);
      Notiflix.Notify.info(translate.instant('Unauthorized User!'));
    }
  }));
};
