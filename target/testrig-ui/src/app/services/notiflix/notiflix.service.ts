import * as Notiflix from "notiflix"

export const cssNotiflix={
    notify(){
        Notiflix.Notify.init({
            width:'auto',
            showOnlyTheLastOne:true,
            borderRadius: '5px',
            fontSize: '17px',
            timeout: 3000,
            clickToClose: true,
            failure: {
                background:'#db504a',
                textColor: '#fff',
                notiflixIconColor: '#fff',
                fontAwesomeClassName: 'fas fa-times-circle',
                fontAwesomeIconColor: '#fff',
            },
            warning : { 
                background:'#eed202',
                textColor: '#171717',
                notiflixIconColor: '#171717',
            },
            success:{
                background:'#1a936f',
                textColor: '#fff',
                notiflixIconColor: '#fff',
            }
        }) 
    }
}
