import { Injectable } from '@angular/core';
import { Mould } from '../models/mould.model';
import { BehaviorSubject, Observable } from 'rxjs';
import { ApiService } from './api.service';
import { AuthService } from './auth.service';
import { HttpClient, HttpHeaders } from '@angular/common/http';
import Notiflix from 'notiflix';
import { TranslateService } from '../translate';


@Injectable({
  providedIn: 'root'
})

export class MouldService {
  public pumpStatusVal: string = 'start'
  public mouldTestStatus: any = {state: '::verifying'}
  public pressure_in: number
  public purgeButton: string = 'Purge Water' 
  public default_mould: Mould = {
    mouldid: '',
    time: '',
    comments: '',
    pressure_set_point_min: 0.0,
    pressure_set_point_max: 9.0,
    enable_all: false,
    max_water_pressure: 0.0,
    channels: [],
    tests: {
      'flow_test': false,
      'air_leak_test': false,
      'water_leak_test': false,
      'heat_test': false
    }
  };
  constructor(
    private readonly api: ApiService, 
    private authService: AuthService, 
    private http: HttpClient,
    private readonly translate: TranslateService
    ) { 
    this.verifyPumpStatus()
    this.verifyMouldTestStatus()
    this.verifyPurgeStatus()
    
  }

  /**
   *  - - - - - - Common function to round off the values at multiple tabs - - - - - -
  */
  public presusureRoundOf(pressure:number):number{
    let num = Number(pressure)
    return isNaN(num) ? 0 : parseFloat(num.toFixed(1));
  }

  public tempRoundOf(temp:number):number{
    let num = Number(temp)
    return isNaN(num) ? 0 : parseFloat(num.toFixed(1));
  }


  // /**
  //  * Conert timestamp to required timestamp  
  //  * @param timestamp 
  //  * @returns 
  //  */
  // formatTimestamp(timestamp: string): string {
  //   return moment(timestamp).format("YYYY-MM-DDTHH:mm:ss");
  // }

  public initializeParameters(): Observable<any> {   // POST
  // Initialization parameters to the pump
    return this.api.post(`initialize`, {});
  }

  /**
   *  - - - - - - NEW MOULD - - - - - -
  */
  public addNewMould(mouldData: any): Observable<any> { // POST
  // Store new mould in sheet 
    return this.api.post(`add-mould-row`, mouldData);
  }

  public deleteCurrentMould(orderNumber: number): Observable<any> { // DELETE
  // Store new mould in sheet 
    return this.api.delete(`delete-mould-row/${orderNumber}`);
  }

  public editCurrentMould(orderNumber:number, mouldData: any): Observable<any> { // PUT
  // Update the selected mould in sheet 
    return this.api.put(`edit-mould-row/${orderNumber}`, mouldData);
  }

  /**
   *  - - - - - - MOULD SETTINGS (LANDING PAGE) - - - - - -
  */
  
  public supportStartStop(action:string): Observable<any> { // POST
  // Store mould settings in DB 
    return this.api.post(`ssh-tunnel/${action}`, {});
  }

  public supportBtnStatus(): Observable<any> { // GET
  // Store mould settings in DB 
    return this.api.get(`ssh-tunnel/status`);
  }

  public insertMouldSettings(body: Mould): Observable<any> { // POST
  // Store mould settings in DB 
    return this.api.post(`insert`, body);
  }
  
  public updateMouldSettings(mould_id:string, body: Mould): Observable<any> {  // PUT
  // Updated mould settings in based on recordID 
    return this.api.put(`update/${mould_id}`, body);
  }

  public fetchMouldSettings(record_id:String): Observable<any> {  // GET
  // Updated mould settings in based on recordID 
    return this.api.get(`fetch/${record_id}`);
  }

  public deleteMouldFromDB(record_id:String): Observable<any> {  // DELETE
  // Updated mould settings in based on recordID 
    return this.api.delete(`delete/${record_id}`);
  }

  public listMouldids(): Observable<any>{ // GET
  // Get API to check how many moulds available
    return this.api.get('list-mouldids')
  }

  public listMouldOpitions(): Observable<any>{ // GET
  // Get API to check how many moulds available
    return this.api.get('read-moulds-file')
  }

  public listMouldWithData(): Observable<any>{ // GET
  // Get API to check how many moulds available with complete data
    return this.api.get('read-moulds-file-whole')
  }

  public listChannelOpitions(): Observable<any>{ // GET
  // Get API to check how many moulds available
    return this.api.get('read-channels-file')
  }

  public listDiameterOpitions(): Observable<any>{ // GET
  // Get API to check how many moulds available
    return this.api.get('read-diameters-file')
  }

  /**
   *  - - - - - - MOULD TEST  - - - - - -
  */
  
  public verifyTestStatus(): Observable<any> { // GET
    return this.api.get(`get_test_status`);
  }

  public getMouldEventsLogs(mould_id: string): Observable<any> { // GET
    return this.api.get(`latest-mould-event/${mould_id}`);
  }

  public getMouldCompleteEventsLogs(mould_id: string): Observable<any> { // GET
    return this.api.get(`latest-mould-test-event/${mould_id}`);
  }

  public getMouldTestHistory(mould_id: string): Observable<any> { // GET
    return this.api.get(`mould-test-history/${mould_id}`);
  }

  public getMouldEventsSelectedTimeStamp(mouldId: string, startTime: string, stopTime: string): Observable<any> { // GET
    return this.api.get(`mould-event-timestamped/${mouldId}/${startTime}/${stopTime}`);
  }

  public purgeWater(purgeAction:string): Observable<any> { // POST
    return this.api.post(`publish/purge_events?message=${purgeAction}`, {});
  }

  public getPurgeWaterStatus(): Observable<any> { // GET
    return this.api.get(`get_purge_status`);
  }

  public startTest(mould_name:string): Observable<any> { // POST
    let data = { message: mould_name+ ':start' }
    return this.api.post(`publish/mould_events?message=${encodeURIComponent(mould_name+ ':start')}`, data);
  }

  public stopTest(mould_name:string): Observable<any> { // POST
    let data = { message: mould_name+ ':stop' }
    return this.api.post(`publish/mould_events?message=${encodeURIComponent(mould_name+ ':stop')}`, data);
  }

  public getoutletTemperature(): Observable<any>{ // GET
  // Get API to check how many moulds available
    return this.api.get('read-outlet-temperature')
  }

  /**
   *  - - - - - - PUMP AND VALVE - - - - - -
  */

  public relayAddonStatus(): Observable<any> {  // GET
  // To check the status of all relay addons
    return this.api.get(`relay/addon/status`);
  }
  
  public getCleaningStatus(): Observable<any> {  // GET
  // To check the status of all Hot/Cold cleaning
    return this.api.get(`get_clean_status`);
  }

  public getAutoFilterStatus(): Observable<any> {  // GET
  // To check the status of all filter cleaning
    return this.api.get(`get_filter_status`);
  }
  
  
  public cleaningStartStop(cleaningAction:string): Observable<any> { // POST
  // To start and stop hot or cold cleaning
    return this.api.post(`publish/clean_events?message=${cleaningAction}`, {});
  }

  public autoFilterCleaningStartStop(filterAction:string): Observable<any> { // POST
  // To start and stop auto filter cleaning
    return this.api.post(`publish/filter_events?message=${filterAction}`, {});
    // return this.api.post(`publish/xyz?message=${filterAction}`, {});
  }

  public waterTankFillEmpty(action:string): Observable<any> { // POST
  // To start and stop auto filter cleaning
    return this.api.post(`publish/recycle_events?message=${action}`, {});
  }
  
  public readPressure(): Observable<any> {   // GET
  // Get Pressure settings from DB
    return this.api.get(`read-pressure`);
  }

  public outletPressure(): Observable<any> {   // GET
    // Get Pressure settings from DB
      return this.api.get(`outlet-pressure`);
    }
  public pumpStatus(): Observable<any> {   // GET
  // Get status for pump 0 - stop | 1 - start | 2 - stop
    return this.api.get(`pump-status`);
  }

  public pumpPressure(): Observable<any> {   // GET
  // Get pressure settings for pump after start
    return this.api.get(`pump-pressure`);
  }

  public pumpPressureInTimeStmp(startTime: string, stopTime: string): Observable<any> {   // GET
  // Get pressure settings for pump with in selecetd time stamp
    return this.api.get(`pump-pressure/${startTime}/${stopTime}`);
  }

  public relaysStatus(relay_number: string, state: string,type:'main' | 'addon'): Observable<any> {   // POST
  // Relay Main and addon APIs based on enum "type"
    return this.api.post(`relay/${type}/${relay_number}/${state}`, {});
  }
  
  public startPump(pressure_val: number): Observable<any> { // POST
  // start the pump 
    return this.api.post(`set-pressure-start-pump/${pressure_val}`, {});
  }

  public incrementDecrementPump(pressure_val: number): Observable<any> { // POST
  // start the pump 
    return this.api.post(`set-pressure/${pressure_val}`, {});
  }
    
  public stopPump(): Observable<any> {  // POST
  // stop the pump 
    return this.api.post(`stop`, {});
  }

  /**
   *  - - - - - - GRAPH (REPORT) - - - - - -
  */

  reportInFormOfGraph(startTime: string, stopTime: string, enabledChannels: string, lang: string): Observable<any> {
    const token =  JSON.parse(localStorage.getItem('Bearer Token') || '')
    const apiURL = this.api.baseUrl + `/generate_flow_chart/flow/${startTime}/${stopTime}?enabled_channels=${enabledChannels}&lang=${lang}`
    const headers = new HttpHeaders({
      'accept': 'application/json',
      'Authorization': `Bearer ${token ? token : ''}`,
    });
    return this.http.get(apiURL, { headers, responseType: 'blob' });
  }

  heatReportInFormOfGraph(startTime: string, stopTime: string, enabledChannels: string, lang: string): Observable<any> {
    const token =  JSON.parse(localStorage.getItem('Bearer Token') || '')
    const apiURL = this.api.baseUrl + `/generate_temp_chart/temp/${startTime}/${stopTime}?enabled_channels=${enabledChannels}&lang=${lang}`
    const headers = new HttpHeaders({
      'accept': 'application/json',
      'Authorization': `Bearer ${token ? token : ''}`,
    });
    return this.http.get(apiURL, { headers, responseType: 'blob' });
  }

  /**
   *  - - - - - - WATER FLOW STATUS (CONFIG SETTINGS) - - - - - -
  */

  public waterFlowStatus(): Observable<any> { // GET
  // Get the water flow status
    return this.api.get(`inputs`);
  } 

  public getRecycleStatus(): Observable<any> { // GET
  // Get the recycle status of water tank
    return this.api.get(`get_recycle_status`);
  }
  
  /**
   *  - - - - - - SETTINGS CONFIG (CONFIG) - - - - - -
  */
  
  public getConfiguration(): Observable<any> { // GET
  // Get the settings config
    return this.api.get(`config`);
  }

  public updateConfigurations(data: any): Observable<any> { // PUT
  // Update the settings config
    return this.api.put(`config`, data);
  }

  public downloadUplodedLogo(filename: string): Observable<Blob> {
  // Get the settings config
    const apiURL = this.api.baseUrl + `/download/${filename}`
    return this.http.get(apiURL, {
      responseType: 'blob'  // Required casting due to Angular typing
    });
  }


  public uploadLogo(logo: File): Observable<any> { // POST
  // Update the settings config
    const token =  JSON.parse(localStorage.getItem('Bearer Token') || '')
    const apiURL = this.api.baseUrl + `/upload`

    const formData = new FormData();
    formData.append('file', logo); 
    const headers = new HttpHeaders({
      'accept': 'application/json',
      'Authorization': `Bearer ${token ? token : ''}`,
    });
    return this.http.post(apiURL, formData, { headers});
  }

  public uploadPDF(file: Blob,fileName:string): Observable<any> { // POST
    const token =  JSON.parse(localStorage.getItem('Bearer Token') || '')
    const apiURL = this.api.baseUrl + `/upload-pdf`

    const formData = new FormData();
    // formData.append('file', file); 
    formData.append('file', file, fileName); // Ensure the file has a name
    const headers = new HttpHeaders({
      'accept': 'application/json',
      'Authorization': `Bearer ${token ? token : ''}`,
    });
    return this.http.post(apiURL,formData, { headers});
  }

  /**
   * Verify pump status 'start' | 'stop'
   */
  verifyPumpStatus(){
    if(this.authService.isLoggedIn()){
      this.pumpStatus().subscribe( (status)=>{
        this.pumpStatusVal = status.status == 0? 'start': 'stop'
      })
    }
  }

  /**
   * Verify status of mould tests 
   */
  verifyMouldTestStatus(){
    if(this.authService.isLoggedIn()){
      this.verifyTestStatus().subscribe({
        next: (status) => {
          this.mouldTestStatus = status
          setTimeout(() => {
            this.verifyMouldTestStatus()
          }, 2000);
        },
        error: (err) => {
          setTimeout(() => {
            this.verifyMouldTestStatus()
          }, 2000);
          console.log('✌️err --->', err);
        }
      });
    }
  }

  /**
   * Verify Purge status 'start' | 'stop'
   */
  verifyPurgeStatus(){
    if(this.authService.isLoggedIn()){
      this.getPurgeWaterStatus().subscribe({
        next: (status) => {
          if(status.state == 'idle' || status.state == 'purge:stopped' || status.state == 'purge:completed'){
            this.purgeButton = 'Purge Water';
          }else{
            this.purgeButton = 'Stop Purge';
            setTimeout(()=>{
              this.verifyPurgeStatus()
            }, 2000)
          }
        },
        error: (err) => {
          console.log('✌️err --->', err);
        }
      })
    }
  }

  /**
   * Function is used to auto clean the filter when water tank is empty
   */
  autoFilterCleaning(){
    let lowerSensor = false
    
    // To verify that lowerSensor is true or false (having water in container or not)
    this.waterFlowStatus().subscribe({
      next: (data) => {
        if (data) {
          let inputsArray = data.inputs.split(" "); // Convert string to an array
          let lastThree = inputsArray.slice(-3).join(""); // Get last 3 values as a string
          switch (lastThree.slice(0, -1)) {
          case "01":
            lowerSensor = true;
            break;
          case "11":
            lowerSensor = true;
            break;
          default:
            lowerSensor = false;
            break;
          }
          console.log('✌️lowerSensor --->', lowerSensor);
          if(lowerSensor){
            this.filterStart()
          }else{
            localStorage.setItem('filterRunningStatus', 'false')
          }
        }  
      },
      error: (error) => {}
    })
  }
  
  /**
   * To start the auto filter cleaning process
   */
  filterStart(){
    // verify the status of auto filter cleaning
    this.getAutoFilterStatus().subscribe({
      next: (data) => {
        if(data && data.state){         
          if(data.state.includes('idle')){
            // Run auto filter cleaning
            this.autoFilterCleaningStartStop('start').subscribe({
              next: (response) => {
                Notiflix.Notify.success(this.translate.instant('Auto Filter Cleaning Started'));
              },
              error: (error) => {
                Notiflix.Notify.failure(this.translate.instant('Something went wrong while starting auto filter cleaning'));
            }
            })
          }
        }
      },
      error: (error) => {}
    })
    
  }

}
