import { round } from '../utils/calculate';
import { ApiService } from './api.service';
import { Injectable } from '@angular/core';
import { AuthService } from './auth.service';
import { MouldService } from './mould.service';
import { BehaviorSubject, Observable } from 'rxjs';
import { Machine, Manifold } from '../models/machine.model';
import { Channel, CurrentData } from '../models/channel.model';
import { ActivatedRoute } from '@angular/router';

@Injectable({
  providedIn: 'root'
})

export class MachineService {
  apiRecallTime: number = 600
  selectedAction:string = ''
  badges:any = {
    "FOL":{
      "alias": 'FOL',
      "message":'Flow out of limit',
      "visible":false
    },
    "FO":{
      "alias": 'FO',
      "message":'Filter Overflow',
      "visible":false
    }
  }
  emergencyButtonPressed: boolean = false // to check that emergance button is pressed or not
  waterTankStatus: any = {
    tanklabel : 'No Water',
    tanklabelClass : 'no-water',
    pumpStatus:  null
  }
  public relayMainStatus: any = {
    water_fill: false,
    water_purge: false,
    compressed_air_fill_inlet: false,
    compressed_air_fill_outlet: false
  }
  public pressure = {
    inletPressure : 0,  // pessure In
    outletPressure : 0, // pressure out
  }
  globalShare = new BehaviorSubject<any>({});
  // badgesContent = new BehaviorSubject<any>("");
  dataCalled = 0
  count = 0
  // public transformedData:any = {}
  public transformedData = new BehaviorSubject<any>(null);


  constructor(private readonly api: ApiService, private mouldService: MouldService, private authService: AuthService,private route:ActivatedRoute) { 
    this.loadAllData()
    this.verifyEmergencyAlert()
    this.verifyPumpReady()
    this.verifyFilterPressure()
  }

  
  verifyPumpReady(){
    if(window.location.href.includes('controls')){
      // if(this.mouldService?.pressure_in){
      //   this.pressure.inletPressure = this.mouldService?.pressure_in
      // }else{
        if(this.authService.isLoggedIn()){
          this.mouldService.readPressure().subscribe({
            next: (response)=>{
              if(response.pressure || response.pressure == 0){
                this.pressure.inletPressure = response.pressure
                this.mouldService.pressure_in = response.pressure
              }
              setTimeout(()=>{
                this.verifyPumpReady()
              }, this.apiRecallTime);
            }, error: (err)=>{
              console.error(err);
              setTimeout(()=>{
                this.verifyPumpReady()
              }, 2000);
            }
          })
        }
      // }
    }
    else{
      setTimeout(()=>{
        this.verifyPumpReady()
      }, this.apiRecallTime);
    }
  }
  
  public getFlowData():Observable<any>{         // GET
    return this.api.get(`flow`);
  }
  
  public getMachineData(): Observable<any> {    // GET
    return this.api.get(`data`);
  }

  public getMachineDataAvergage(): Observable<any> {    // GET
    return this.api.get(`data-average`);
  }

  public getFilterPressure(): Observable<any> { // GET
    return this.api.get(`read-filter-pressure`);
  }
  

  public loadAllData(){
    const isLoggedIn = this.authService.isLoggedIn();
    if(!isLoggedIn) return 
    if(window.location.href.includes('controls')){
      // this.getMachineData().subscribe({
      this.getMachineDataAvergage().subscribe({
        next: (data) => {
          if(data){
            this.globalShare.next({ 'dataApi': true })
            let updatedVal = this.dataSchemaUpdateAcc(data)
            this.transformedData.next(updatedVal);
          }else{
            this.globalShare.next({ 'dataApi': false })
          }
          setTimeout(() => {
            if(this.authService.isLoggedIn()){
              this.loadAllData();
            }
          },this.apiRecallTime);
        },
        error: (error) => {
          setTimeout(() => {
            if(this.authService.isLoggedIn()){
              this.globalShare.next({ 'dataApi': false })
              this.loadAllData();
            }
          }, 2000);
        }
      });
    }else{
      setTimeout(() => {
        if(this.authService.isLoggedIn()){
          this.loadAllData();
        }
      },this.apiRecallTime);
    }
  }


  private readonly machineSub = new BehaviorSubject<any>(null);
  public readonly machine$ = this.machineSub.asObservable();
  public get machine(): Machine {
    return this.machineSub.getValue();
  }
  public set machine(val: Machine) {
    if (val) {
      this.machineSub.next(val);
    }
  }

// Current manifold
  private readonly manifoldSub = new BehaviorSubject<Manifold | null>(null);
  public readonly manifold$ = this.manifoldSub.asObservable();
  public get manifold(): Manifold | null {
    return this.manifoldSub.getValue();
  }
  public set manifold(val: Manifold) {
    if (val) {
      this.manifoldSub.next(val);
      this.channel = this.channels.find( item => item.ui_machine_mac === this.machine.mac && item.ui_manifold_mac === val.mac );
    }
  }

  // Current channel
  // private readonly channelSub = new BehaviorSubject<CurrentData | null>(null);
  // public readonly channel$ = this.channelSub.asObservable();
  // public get channel(): CurrentData | null{
  //   return this.channelSub.getValue();
  // }
  // public set channel(val: CurrentData) {
  //   this.channelSub.next(val);
  // }

  // Current channel
  private readonly channelSub = new BehaviorSubject<any>(null);
  public readonly channel$ = this.channelSub.asObservable();
  public get channel(): any {
    return this.channelSub.getValue();
  }
  public set channel(val: any) {
    this.channelSub.next(val);
  }

  // CurrentData
  private readonly channelsSub = new BehaviorSubject<CurrentData[]>([]);
  public readonly channels$ = this.channelsSub.asObservable();
  public get channels(): CurrentData[] {
    return this.channelsSub.getValue();
  }
  public set channels(val: CurrentData[]) {
    this.channelsSub.next(val);
  }


  /**
   * Result from inputs API 
   * get the values and manege for emergency button pressed (first bit value)
   * Manage pump status  based on 2nd last and 3rd last bit from inputs  
   */
  verifyEmergencyAlert(flag=false){
    let currentUrl = window.location.href;
    // const isMouldRoute = this.route.snapshot.url.some(segment => segment.path === 'mould');
    // console.log('isMouldRoute', isMouldRoute);
    if(this.authService.isLoggedIn() ){
      // if(!currentUrl.includes('/mould')){
        this.mouldService.waterFlowStatus().subscribe({
          next: (inputs) => {
            if(flag == true)
              window.location.reload()
            if (inputs) {
              //check for lower sensor
              let inputsArray = inputs.inputs.split(" ");

              let inputs_ = (inputs.inputs).split(" ").join("")
              // To verify that emergency button is pressed or not
              this.emergencyButtonPressed = inputs_[0] == '1'? true: false; 
              
              this.relayMainStatus.water_fill = inputs_[7] == '1'? true: false 
              this.relayMainStatus.water_purge = inputs_[8]== '1'? true: false 
              // inputs = inputs.inputs;
              this.relayMainStatus.compressed_air_fill_inlet = inputs_[10]== '1'? true: false 
              this.relayMainStatus.compressed_air_fill_outlet = inputs_[9]== '1'? true: false 
              
              // let inputsArray = inputs.inputs.split(" "); // Convert string to an array
              let lastThree = inputsArray.slice(-3).join(""); // Get last 3 values as a string
              switch (lastThree.slice(0, -1)) {
                case "10":
                  this.waterTankStatus.pumpStatus = false
                  this.globalShare.next({'lowerSensor': false})

                  break;
                case "01":
                  this.waterTankStatus.pumpStatus = true
                  this.globalShare.next({'lowerSensor': true})
                  //lower sensor is active
                  break;
                case "11":
                  this.waterTankStatus.pumpStatus = true
                  //lower sensor is active
                  this.globalShare.next({'lowerSensor': true})

                  break;
                default:
                  this.waterTankStatus.pumpStatus = false
                  this.globalShare.next({'lowerSensor': false})

                  break;
              }
            }
            setTimeout(() => {
              this.verifyEmergencyAlert()
            }, this.apiRecallTime);
          },
          error: (error) => {
            setTimeout(()=>{
              this.verifyEmergencyAlert(true)
            },10000)
            console.log(error)
            if(this.api.baseUrl == 'http://127.0.0.1:8000'){
              let inputs ={ inputs : "1 0 0 1 0 1 0 0 0 0 0 1 1 0 1 1 1 0" }
              let inputsArray = inputs.inputs.split(" ");
              let inputs_ = (inputs.inputs).split(" ").join("")
              // To verify that emergency button is pressed or not
              this.emergencyButtonPressed = inputs_[0] == '1'? true: false; 
              
              this.relayMainStatus.water_fill = inputs_[7] == '1'? true: false 
              this.relayMainStatus.water_purge = inputs_[8]== '1'? true: false 
              // inputs = inputs.inputs;
              this.relayMainStatus.compressed_air_fill_inlet = inputs_[10]== '1'? true: false 
              this.relayMainStatus.compressed_air_fill_outlet = inputs_[9]== '1'? true: false 
              
              // let inputsArray = inputs.inputs.split(" "); // Convert string to an array
              let lastThree = inputsArray.slice(-3).join(""); // Get last 3 values as a string
              switch (lastThree.slice(0, -1)) {
                case "10":
                  this.waterTankStatus.pumpStatus = false
                  break;
                case "01":
                  this.waterTankStatus.pumpStatus = true
                  break;
                case "11":
                  this.waterTankStatus.pumpStatus = true
                  break;
                default:
                  this.waterTankStatus.pumpStatus = false
                  break;
              }
            }
            setTimeout(() => {
              this.verifyEmergencyAlert()
            }, 2000);
          }
        });
      // }
    }
  }

  dataSchemaUpdateAcc(data: any){
    this.pressure.outletPressure = data.data_14_0;
    // console.log('this.pressure.inletPressure',this.pressure.inletPressure)
    let transformedData:any = {
      // time: data.time,  
      inlet_temps: [data.data_15_0],       
      channels: [],
      mac: data.manifold, 
      pressure_in: this.pressure.inletPressure,      
      pressure_out: data.data_14_0, 
      // ui_machine_mac: data.machine, 
      // ui_manifold_mac: data.manifold ,
      delta_pressure: round(this.pressure.inletPressure - (data.data_14_0 > 0?data.data_14_0:0))
    };
  
    for (let key in data) {
      let match = key.match(/data_(\d+)_(\d+)/);
      if (match) {
        let channel = parseInt(match[1]); // Extract channel number
        let type = parseInt(match[2]);    // 0 for flow, 1 for temp
        let channelCount = 12
        if(channel <= channelCount){
          let channelObj:Channel = {
            id: channel,
            values: { temp: 0, sensor_val: 0 }, 
            ui_status: 0
          };
          if(type === 1){
            channelObj.values.temp = round((data as Record<string, any>)[key]); // Assign flow
            channelObj.values.temp = channelObj.values.temp + this.count
            transformedData.channels.push(channelObj);
            let tempKey = 'data_' + channel + '_0';
            channelObj.values.sensor_val = round((data as Record<string, any>)[tempKey]);  // Assign temperature
          }
        }
      }
    }
    return transformedData
  }

  /**
   * To get the user role from localStorge from return role
   * @returns 
   */
  getUserRole(): boolean {
    const role = localStorage.getItem('userRole');
    return role ? (JSON.parse(role) == 'admin'? true: false) : false;
  }

  /**
   * get the filter pressure 
   */
  verifyFilterPressure(){
    if(this.authService.isLoggedIn()){
      this.getFilterPressure().subscribe({
        next: (filter:any) => {
          if(filter){
            this.badges.FO.visible = filter.filter_pressure > 2 ? true : false

            this.globalShare.next({'filterPressure':this.mouldService.presusureRoundOf(filter.filter_pressure)})
            setTimeout(() => {
              this.verifyFilterPressure()
            }, 1000);
          }
        },
        error: (err:any) => {
          setTimeout(() => {
            this.verifyFilterPressure()
          }, 2000);
        }
      });
    }
}
}
