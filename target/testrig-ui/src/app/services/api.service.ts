import { HttpClient, HttpHeaders, HttpParams } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { isNotEmpty, isNotEmptyObject } from '../utils/validate';

enum Method {
  GET = 'GET',
  POST = 'POST',
  PUT = 'PUT',
  PATCH = 'PATCH',
  DELETE = 'DELETE'
}
@Injectable({
  providedIn: 'root'
})
export class ApiService {
  
  public baseUrl:string; // Set the API base URL  
  
  constructor(private http: HttpClient) {
    // Set the API base URL based on the environment "Because Base IP depenedr upon the environment"
    if (window.location.origin.includes('localhost') ||
        window.location.origin.includes('4200'))
    {
      // this.baseUrl = 'http://127.0.0.1:8000'
      this.baseUrl = 'http://*************:8000'
    }
    else if(window.location.origin.includes('4201')) {
      this.baseUrl = window.location.origin.replace('4201', '8000');
    }
    else{
      this.baseUrl = window.location.origin+':8000';
    }
  }

  // GET request
  get(endpoint: string, params?: any): Observable<any> {
    return this.http.get(`${this.baseUrl}/${endpoint}`, { params });
  }

  // POST request
  post(endpoint: string, body: any): Observable<any> {
    return this.request(Method.POST, endpoint, body);
    // return this.http.post(`${this.apiUrl}/${endpoint}`, data);
  }

  // PUT request
  put(endpoint: string, data: any): Observable<any> {
    return this.http.put(`${this.baseUrl}/${endpoint}`, data);
  }

  // PATCH request
  patch(endpoint: string, data: any): Observable<any> {
    return this.http.patch(`${this.baseUrl}/${endpoint}`, data);
  }

  // DELETE request
  delete(endpoint: string): Observable<any> {
    return this.http.delete(`${this.baseUrl}/${endpoint}`);
  }


  private request(method: Method, url: string, body?: any, params?: any): Observable<any> {
    const httpOptions = {
      body: isNotEmptyObject(body) ? JSON.stringify(body) : null,
      params: this.getParams(params),
      headers: new HttpHeaders({
        'Content-Type': 'application/json',
        Accept: 'application/json'
      })
    };
    return this.http.request(method, this.generateUrl(url), httpOptions);
  }
  
  private getParams(params: any): HttpParams {
    let uParams = new HttpParams();
    for (const key in params) {
      if (params.hasOwnProperty(key) && isNotEmpty(params[key])) {
        uParams = uParams.append(key, params[key]);
      }
    }
    return uParams;
  }

  private generateUrl(url: string): string {
    return url.indexOf('http') === 0 ? url : `${this.baseUrl}/${url}`;
  }
}
