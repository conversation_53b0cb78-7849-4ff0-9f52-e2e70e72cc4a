/* You can add global styles to this file, and also import other style files */
@import 'styles/index.scss';

@import url('https://fonts.googleapis.com/css2?family=Roboto:ital,wght@0,100..900;1,100..900&display=swap');


.a-tag{
    color: black !important;
    text-decoration: none;
}
.border-theme{
    border-color: #0990d2 !important;
}
.card-heading{
    color: $card-blue;
}

.hovered-element:hover{
    background-color: rgba(111, 207, 255, 0.1) !important;
    border: 0.5px solid rgb(0, 187, 255);
    border-radius: 3px;
}

.element-card{
    background-color: #f4f7f8 !important;
    border-radius: 3px !important;
}
 
.rounded-8
{
    border-radius: 8px;
}
.p20
{
    padding: 20px;
}
.test-container-card{ 
    border-radius:8px; 
    background-color: white;
    max-width: 900px;  
    margin: 0 auto;
}

.btn-style{
    border-radius: 25px !important;
}


.arrow {
    font-size: 50px;
    color: $card-blue; 
    height: 25px;
    line-height: 0;
}

.line {
    display: none;
    height: 4px;
    width: 100%;
    border-radius: 2px;
    background-color: $blue;
    position: absolute;
    bottom: -12px;
    margin-left: 0 !important;
    left: 0;
}
 
.login-module
{
    min-height: 100vh;
    padding: 20px 0;
    display: flex;
    align-items: center;
    justify-content: center;
    background: #F1FAFF;
    

    .card-box {
        box-shadow: 0px 12px 20px 0px #E0F3FD;
        background: #fff;
        border-radius: 16px;
        padding: 40px 34px;
        width: 100%;
        max-width: 450px;
        margin: auto;
        @media screen and (max-width: 767px)
        {
            padding: 20px;
        }
        h4 {
            color: #000000;
            font-weight: 600;
        }
        .form-control
        {
            height: 48px;
            border: 1px solid $border-color;
            border-radius: 4px;
            color: #000;
            padding-left: 20px;
            padding-right: 20px;
            min-height: inherit;
            padding-top: 10px;

            &:focus
            {
                border-color:$blue;
                box-shadow: none;
            }
        }
    }
}
.form-floating > label {
    padding-top: 11px;
    font-size: 16px;
    left: 10px;
}
.form-floating > .form-control:-webkit-autofill ~ label  , .form-floating > .form-control-plaintext ~ label, .form-floating > .form-control:focus ~ label, .form-floating > .form-control:not(:placeholder-shown) ~ label, .form-floating > .form-select ~ label{
    color: $blue;
    transform: scale(.85) translateY(-22px) translateX(.15rem);
}

.form-floating > .form-control-plaintext ~ label::after, .form-floating > .form-control:focus ~ label::after, .form-floating > .form-control:not(:placeholder-shown) ~ label::after, .form-floating > .form-select ~ label::after {
    position: absolute;
    inset: 1rem 0.375rem;
    z-index: -1;
    height: 1.5em;
    content: "";
    background-color: var(--bs-body-bg);
    border-radius: var(--bs-border-radius);
}
input:-webkit-autofill {
    background-color: #fff !important;
    border-color:$blue !important;
    -webkit-box-shadow: 0 0 0px 1000px #fff inset !important; 
    transition: background-color 5000s ease-in-out 0s;
}
.btn 
{
    font-size: 16px;
    height: 48px;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
    border-radius: 4px;
    padding: 2px 18px 0;
    min-width: 130px;

    &.btn-primary 
    {
        background-color: $blue;
        border-color: $blue;
        color: #fff;

        &:hover , &:active, &:focus
        {
            background-color: #0979B0;
            border-color: #0979B0;

           
        }
    }
    &.btn-success
    {
        background-color: $green;
        border-color: $green;
        color: #fff;
    }
    // &.btn-default
    // {
    //     background-color: #D2D2D2;
    //     border-color: #D2D2D2;
    //     color: #4E4E4E;

    //     &:hover , &:active, &:focus
    //     {
    //         background-color: #bfbebe;
    //         border-color: #bfbebe;
    //     }
    // }
    &.btn-icon
    {
        min-width: inherit !important;
        padding: 0 13px;
    }
    &.btn-bordered
    {
        border-color: $blue;
        color: $blue;
        &:hover , &:active, &:focus 
        {
            background-color: $blue;
            color: #fff;
        }
    }

    &.btn-green
    {
        border-color: $green;
        color: $green;
        &:hover , &:active, &:focus 
        {
            background-color: $green;
            color: #fff;

            img 
            {
                filter: brightness(0) invert(1);
            }
        }
    }
    &.btn-danger-outline
    {
        border-color: $btn-red;
        color: $btn-red;
        &:hover , &:active, &:focus 
        {
            background-color: $btn-red;
            color: #fff;
        }
    }

}
.card-data
{
    .borderRight
    { 
            border-right: 1px solid $border-color;      
    }
  
}


.card-data  .ngx-slider .ngx-slider-bar {
    left: 0;
    width: 100%;
    height: 4px;
    z-index: 1;
    background: rgba(9, 144, 210, 0.12);
    -webkit-border-radius: 2px;
    -moz-border-radius: 2px;
    border-radius: 20px;
}
.card-data  .ngx-slider .ngx-slider-bar-wrapper {
    left: 0;
    box-sizing: border-box;
    margin-top: -16px;
    padding-top: 9px;
    width: 100%;
    height: 31px;
    z-index: 1;
}
.card-data .ngx-slider .ngx-slider-pointer {
    cursor: pointer;
    width: 18px;
    border: 3px solid #E3F2ED;
    height: 18px;
    top: -14px;
    background-color: $blue;
    z-index: 3;
    -webkit-border-radius: 16px;
    -moz-border-radius: 16px;
    border-radius: 16px;
}
.card-data .ngx-slider .ngx-slider-pointer:after
{
    display: none;
}
.card-data .ngx-slider .ngx-slider-bubble {
    color: rgba(0, 0, 0, 1) !important;
    font-size: 13px !important;
}
.card-data .ngx-slider .ngx-slider-selection { 
    background: $blue !important; 
}


.comment-sidebar {
    box-shadow: -5px 0px 6px 0px #0000000D;
    background: #fff;
    position: fixed;
    width: 100%;
    max-width: 480px;
    top: 0;
    height: 100vh;
    right: -100%;
    z-index: 9;
    transition: all 0.3s ease-in-out 0s;
    padding: 20px;
    display: flex;
    flex-direction: column;

    .crossBtn {
        position: absolute;
        left: -58px;
        width: 58px;
        height: 58px;
        background: #EDF9FF;
        border: 1px solid #EDF9FF;
        border-radius: 8px 0 0 8px;
        top: 0;
        img 
        {
            height: 16px;
        }
    }
    h4 
    {
        font-size: 20px;
        color: $header-blue;
        margin-bottom: 20px;
    }

    .comment-box {
        padding: 20px 15px;
        border: 1px solid $border-color;
        border-radius: 8px;
        flex: 1;
        display: flex;
        flex-direction: column;

        .comment-form {
            position: relative;
            // margin-top: auto;
            flex: 1;

            textarea {
                background: #F1FAFF;
                height: 100%;
                width: 100%;
                border-color: #F1FAFF;
                padding: 10px;
                outline: none !important;
                font-size: 14px;
                padding-right: 90px;
            }
            .btn 
            {
                position: absolute;
                right: 10px;
                top: 10px;
                height: 35px;
                min-width: 70px;
            }
        }
    }
}
.comment-sidebar.hidden 
{
    right: 0;
}
.overlay-box {
    position: absolute;
    left: 0;
    top: 0;
    right: 0;
    bottom: 0;
    background: #0000008C;
    z-index: 4;
    transition: all 0.3s ease-in-out 0s;
    display: none;

    &.active 
    {
        display: block;
    }
}

.card-heading
{
    padding: 20px;
    border-bottom: 1px solid $border-color;

    h4 
    {
        color: $header-blue;
        font-size: 22px;
        font-weight: 500;
    }
}
.select-Module {
    padding: 20px;
    overflow: auto;
    height: calc(100vh - 290px);
    .list-group
    {
        list-style-type: none;
        display: flex;
        flex-direction: column;
        gap: 8px;

        li 
        {
            background-color: #F8F8F8;
            border:1px solid #F8F8F8;
            border-radius: 4px;
            color: #868585;
            font-size: 16px;
            display: inline-flex;
            justify-content: space-between;
            padding: 13px 20px;
            cursor: pointer;

            &.selected 
            {
                border-color: $blue;
                background-color: #F4FCFF;
                color: #000000;
            }
            &:hover 
            {
                background-color: #F4FCFF;
                color: #000000;

            }
        }
    }
}
.test-module
{
    padding: 5px 20px 20px;
    border-top: 1px solid $border-color;  
}


.logs{
    margin: 0px 24px;
    padding: 24px 12px;
    border: 1px solid #C8C8C8;
    border-radius: 5px;
    display: flex;
    flex-direction: column;
    .logs-label {
        position: absolute;
        top: -12px;
        background-color: #fff;
        padding: 1px 10px;
        font-size: 16px;
        color: $header-blue;
        left: 10px;
    }
    .scroll-logs{
        flex: 1;
        overflow: auto;
    }
}
.detail-tabs {
    flex: 1;
    display: flex;
    flex-direction: column;
    padding: 0px 20px 20px;
    .nav-link
    {
        display: flex;
        align-items: center;
        gap: 10px;
    }
    .delail-inner
    {
        .selected{
            background: rgba(9, 144, 210, 0.05);
            border: 0.5px solid rgb(0, 187, 255);
        }
        h3{
            color: $card-blue;
        }
        height: calc(100vh - 278px);
        .form-check
        {
            // padding-left: 40px;
            position: relative;
            display: inline-flex;
            gap: 10px;
            justify-content: space-between;
            input 
            {
                position: absolute;
                height: 24px;
                width: 24px;
                margin: 0;
                padding: 0;
                left: 0;
                top: 14px;
            }
        }
    }
}

.form-check-input:checked {
    background-color: $blue;
    border-color: $blue;
}
.presTab
{
    .MouldBOx
    {
        row-gap: 20px;
        margin-bottom: 20px;
        label 
        {
            color: #4E4E4E;
            font-size: 16px;
        }
        input {
            width: 100%;
            height: 38px;
            border-radius: 4px;
            border: 1px solid $border-color;
            padding: 0 15px;
        }
        .col-sm-3 {
            display: flex;
            align-items: center;
            justify-content: flex-end;
        }
    }

    .comment-text {
        margin-top: 20px;
        h4 
        {
            color: #4E4E4E;
            margin-bottom: 8px;
            font-size: 16px;
            font-weight: 400;
        }
        .note-comment {
            padding: 20px;
            border: 1px solid $border-color;
            border-radius: 4px;
            color: #000000;
            font-size: 16px;
        }
    }

    .table-responsive {
        background: #f7f7f7;
        padding: 20px;
        max-height: 305px;
        .table 
        {
            background-color: transparent;
            th , td 
            {
                color: #4E4E4E;
                font-size: 16px;
                background-color: transparent;
                border: none;
            }
            th 
            {
                font-weight: 500;
                color: #000000;
                font-size: 18px;
            }
        }
    }
    
}
.water_reserve 
{
    h4 {
        font-size: 20px;
        color: $header-blue;
        margin-bottom: 20px;
        font-weight: 400;
    }
    .inputbox
    {
        gap: 20px;

        label {
            font-size: 16px;
            color: #4E4E4E;
        }
        .form-control {
            height: 48px;
            border: 1px solid $border-color;
        }
    }
}
.p2p
{
    display: flex;
    align-items: center;
    margin: 20px 0;
    gap: 30px;
    cursor: pointer;
    .iconbox
    {
        background-color: #F4F7F8;
        display: flex;
        align-items: center;
        justify-content: center;
        min-width: 90px;
        width: 90px;
        height: 90px;
        border-radius: 8px;
    }
    .arrow-icon {
        margin-left: auto;
        width: 50px;
        height: 50px;
        background: $arrow-bg-light-blue;
        border: none;
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: 100%;
    }
    h3 
    {
        font-size: 28px;
        font-weight: 600;
        color: #000000;
        margin: 0;
    }
    &:hover 
    {
        background-color: $arrow-bg-light-blue !important;
    }
}
.backArrow
{
    width: 50px;
    height: 50px;
    background: $arrow-bg-light-blue;
    border: none;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 100%;
}
.gap20
{
    gap: 20px;
}
.outerBox
{
    padding: 20px 15px;
    height: calc(100vh - 220px);
    h4 {
        font-size: 18px;
        color: #000000;
        margin-bottom: 15px;
    }
    .pump-input {
        display: flex;
        gap: 50px;
        align-items: center;
        justify-content: space-between;
        margin-bottom: 10px;

        .me-3
        {
            min-width: 62px;
            text-align: right;
        }

        label 
        {
            color: #4E4E4E;
            font-size: 16px;
        }
        
        .form-control {
            height: 48px;
            max-width: 50%;
            border: 1px solid $border-color;

            &:disabled
            {
                background-color: #F2F2F2;
            }
            &.flex-fill 
            {
                max-width: 100%;
            }
        }
        .form-select {
            height: 48px;
            max-width: 50%;
            border: 1px solid $border-color;
            border-right: none;
            background-image: url(../src/assets/images/arrowBg.svg);
            background-size: 48px;
            background-position: right;
        }
    }
}
.borderRight
{ 
        border-right: 1px solid $border-color;      
}

.tags {
    background: transparent;
    padding: 0 12px;
    border: 1px solid;
    border-radius: 20px;
    height: 30px;
    display: flex;
    align-items: center;
    gap: 7px;
    font-size: 16px;
    min-width: 90px;
    &.yellow-tag 
    {
        border-color: #ED9F0F;
        color: #ED9F0F;
    }
    &.danger-tag
    {
        border-color: $btn-red;
        color: $btn-red;
        &:hover , &:active 
        {
            background-color: $btn-red;
            color: #fff;
            svg path
            {
                fill: #fff;
            }
        }
    }
    &.dark-green-tag
    {
        border-color:$green;
        color: $green;
    }
    &.gray-tag
    {
        border-color: $border-color;
        color: $border-color;
    }
    &.primary-tag
    {
        border-color: #29BCF1;
        color: #29BCF1;
    }
}

.mainHeading
{
    font-size: 28px;
    color:$header-blue ;
    margin: 0;
    font-weight: 500;
}

.water-status{
    background-color: red;
    border-radius:100%;
    height: 10px;
    width: 10px;
    &.active{
        background-color: green;
    }
}

.processing{
    min-height: calc(100vh - 117px);
}

.readonly-div{
    opacity: 0.7;
    pointer-events: none;
}


/* HTML: <div class="loader"></div> */
.loader-test-status {
    width: 24px;
    height: 24px;
    border: 3px solid $blue;
    border-bottom-color: transparent;
    border-radius: 50%;
    display: inline-block;
    box-sizing: border-box;
    animation: rotation 1s linear infinite;
    }

    @keyframes rotation {
    0% {
        transform: rotate(0deg);
    }
    100% {
        transform: rotate(360deg);
    }
} 

@keyframes blink {
    0% { opacity: 1; }
    50% { opacity: 0; }
    100% { opacity: 1; }
}
.blink {
    animation: blink 1s infinite;
}


.tests {
    padding: 0px;
    margin: 0px;
    span{
        user-select: none;
    }
    .test-inner
    {
        cursor: pointer;
        align-items: center;
        position: relative;
        display: flex;
        flex-direction: column;
        padding: 10px;
        /* background: #eee; */
        gap: 5px;
        border-radius: 5px;
        border: 1px solid #eee;
        min-width: 115px;
        justify-content: center;
    }
    img{
        max-height: 65px;
        max-width: 65px;
    }
    input{
        display: none;
        position: absolute;
        right: 5px;
        top: 5px;
    }
}
input:checked ~ .test-inner
{
    background: rgba(9, 144, 210, 0.05);
    border-color: #0087d2;
    span{
        color: #0087d2;
    }   
}

.print-btn {
    // display: flex;
    text-decoration: none;
    border: none;
    background: transparent;
    color: $green;
    gap: 10px;
    align-items: center;
}

.channel-card {
    // height: $card-height;
    border-radius: 16px;
    background-color: $white;
    text-decoration: none;
    border: none;
    cursor: pointer;
    transition: transform 0.5s ease;
    overflow: hidden;
    // background-color: $app-background;
    // box-shadow: 0 7px 20px 0px rgb(0 0 0 / 9%);
    box-shadow: 0 3px 15px 0px rgba(0, 0, 0, 0.09);
    background: #fff;
  
    &:hover {
      transform: scale(1.02);
      text-decoration: none;
    }
  
    &-header {
      position: relative;
  
      .header-border {
        width: 100%;
        border: none;
        &.badge{
          right: 10px !important;
          background: red($color: #000000);
        }
      }
  
      .number {
        font-size: 20px;
        color: $black;
        padding: 10px 15px;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        width: 100%;
        font-weight: 500;
      }
  
      .header-border {
        height: 5px;
        position: absolute;
      }
  
      &.size-2 {
        .header-border {
          height: 50px;
          position: relative;
        }
  
        .number {
          color: $white;
        }
      }
    }
  
    &-body {
      padding: 16px;
      display: flex;
      justify-content: space-between;
    }
  }
  

  .title {
    color: #000;
    margin-bottom: 16px;
    font-size: 14px;
    letter-spacing: 1px;
    // line-height: 8px; 
    text-align: center;
  }


  .chart {
    width: 90px;
    // height: 260px;
    height: 180px;
    border-radius: 4px;
    padding: 0 0 0 30px;
    background-color: #F1FAFF;
    padding-top: 35px;
    padding-bottom: 23px;
    display: flex;
    align-items: flex-end;
  
    &-value {
      width: 6px;
      border-radius: 4px;
      margin-left: 0;
      position: relative;
      animation: progressBar 1s ease-in-out;
      animation-fill-mode: both;
  
      &-content {
        font-size: 14px;
        font-weight: bold;
        // color: #DB504A;
        position: absolute;
        left: 0;
        top: -18px;
        &-unit {
          font-weight: normal;
          font-size: 14px;
          line-height: 8px;
        }
      }
    }
  
    &-label {
      display: flex;
      flex-direction: column-reverse;
      height: 100%;
      justify-content: space-between;
      padding-left: 0px;
      position: relative;
  
      &-item {
        color: $label-color;
        font-size: 14px;
        line-height: 12px;
        position: relative;
        &-text {
          position: relative;
          display: inline-block;
          width: 100%;
          text-align: right;
        }
        &-line {
          position: relative;
          &:after {
            display: block;
            content: '';
            width: 10px;
            height: 2px;
            background: $label-color;
            margin: auto;
          }
        }
      }
      .blue {
        color: $blue;
        font-size: 14px;
        .chart-label-item-line {
          &:after {
            background: $blue;
            display: none;
          }
        }
      }
      .primary {
        color: $primary;
        .chart-label-item-line {
          &:after {
            background: $primary;
          }
        }
      }
    }
  }


.chart-label-item.blue .chart-label-item-line {
    background: $blue;
    right: -10px;
  }
  
  .chart-label .primary .chart-label-item-line {
    background: #db504a;
  }
  
  
  .chart-label-item:first-child span.chart-label-item-text {
    top: 17px;
    left: 0;
    text-align: center;
  }
  
  .chart-label-item:nth-child(6) span.chart-label-item-text {
    top: -22px;
    left: 0;
    text-align: center;
    display: inherit;
    min-width: 26px;
  }
  
  .chart .chart-label:after {content: '';width: 2px;height: 100%;position: absolute;background: #9eb9c2;left: 0;right: 0;margin: auto;}
  
  .chart-label-item.primary .chart-label-item-line {
    // right: -30px;
    display: none;
  }
  
  .chart-label-item.blue .chart-label-item-line {
    width: 6px;
    height: 6px;
    border-radius: 50px;
    z-index: 10;
    top: -9px;
  }
  
  .chart-label .blue .chart-label-item-text {
    top: 0;
    text-align: right;
    left: -27px;
    min-width: 28px;
  }
  
  

  .width-value{
    width: 45px;
    }
    
    
    
    
    .down-5 {
      bottom: 7px;
    }
    
    .up-5 {
      bottom: 15px;
      // bottom: 6px;
    }



// Skeleton Wave -----------------------

.placeholder-wave .placeholder {
    background-color: $skeleton-wave-light-gray; /* Base background */
    background-image: linear-gradient(
      90deg,
      $skeleton-wave-light-gray 0%,
      $skeleton-wave-mid-gray 50%,
      $skeleton-wave-light-gray 100%
    );
    background-size: 200% 100%;
    animation: strongPlaceholderWave 1.2s linear infinite;
  }
  
  @keyframes strongPlaceholderWave {
    0% {
      background-position: 200% 0;
    }
    100% {
      background-position: -200% 0;
    }
  }
.skeleton-bg{
    border-radius: 8px;
    background-color: $skeleton-bg;
}

//   ----------------------