@import url('https://fonts.googleapis.com/css?family=Noto+Sans:400,700&display=swap');

@import 'variables.scss';
@import 'mixins';
@import 'utilities';
@import 'button.scss';
@import 'input.scss';
@import 'checkbox.scss';
@import 'select.scss';
@import 'radio.scss';
// @import '~ngx-toastr/toastr.css';

// Toast message customization start
.toast-title{
  // color: rgb(44, 59, 92) !important;
  color: white !important;
  font-size: 16px !important;
}

.toast-container .ngx-toastr {
  position: relative;
  overflow: hidden;
  margin: 0 0 6px;
  padding: 15px 15px 15px 50px;
  width: 300px;
  border-radius: 15px !important;
  background-position: 15px center;
  background-repeat: no-repeat;
  background-size: 24px;
  box-shadow: 0 0 12px #999;
  color: rgb(236, 234, 234);
  border: 4px solid rgb(107, 158, 190) !important;
  font-size: 13px !important;
}

// Toast message customization end

body {
  font-size: $app-font-size;
  background-color: $app-background; 
  font-family: "Roboto", serif;
  color: $dark-navy;
}

// .b-status-0 {
//   background: #db504a !important;
// }

.b-status-2 {
  background: #db504a !important;
}

.b-status-3 {
  background: #0990D2;
}

canvas{
  box-shadow: 0 7px 20px 0px rgb(0 0 0 / 9%);
}

.header-border.b-status-2 + .header-content app-loading .sk-circle:before {
  background: #fff !important;
}

.header-border.b-status-3 + .header-content app-loading .sk-circle:before {
  background: #fff !important;
}

#NXConfirmButtonOk{
  background-color: #0090ce !important;
  text-decoration:  none;
}
#NXConfirmButtonCancel{
  text-decoration: none;
}
.notiflix-confirm-head h5{
  color: rgb(0, 0, 0) !important;
}
// @-webkit-keyframes blinker {
//   from {opacity: 1.0;}
//   to {opacity: 0.0;}
// }
// .blink{
// 	text-decoration: blink;
// 	-webkit-animation-name: blinker;
// 	-webkit-animation-duration: 0.6s;
// 	-webkit-animation-iteration-count:infinite;
// 	-webkit-animation-timing-function:ease-in-out;
// 	-webkit-animation-direction: alternate;
// }

.gray-dark
{
  background: #898989 !important;
}
.gray-light
{
  background: #a7a7a7 !important;
}
.visibility-on-gray{
  visibility: hidden !important;
}
.full-width-blink
{
  width: 80px;
}

.disabled-click{
  pointer-events: none;
  opacity: 0.4;
}

button:focus{
  box-shadow: none !important;
}

@media screen and (min-width: 560px) {
  .modal-dialog {
      max-width: 650px;
  }
}
.create-user-modal .multiselect-dropdown
{
    .dropdown-btn 
    {
        border: 1px solid #ced4da !important;
        border-radius: 50px !important;
        .dropdown-multiselect__caret
        {
          &::before 
          {
            border-style: solid;
            border-width: 6px 6px 0 !important; 
          }
        }
        .selected-item-container 
        {
          .selected-item
          {
            border-radius: 30px !important;
            padding:0 10px!important;
            a 
            {
              top: -1px;
              position: relative
            }
          }
        }
    }
    .dropdown-list 
    {
      background: #eee !important;
      font-size: 16px;
      input[aria-label="multiselect-search"] {
          background: #eee;
          height: 30px;
      }
    }
   
}
.text-danger
{
  color: #db504a !important;
}
 

.cursor-pointer{
  cursor: pointer;
}

//  remove the bar of the google translator after selecting language
.goog-te-gadget img, .VIpgJd-ZVi9od-l4eHX-hSRGPd{
  display:none !important;
}
body > .skiptranslate {
  display: none;
}
body {
  top: 0px !important;
}
.google-drop-down{
  display: none;
}
// .goog-te-gadget .goog-te-combo {
//     margin: 4px 0;
//     background: transparent;
//     border: none !important;
//     outline: none !important;
//     padding: 5px 0px;
//     font-weight: bold;
//     color: #001D3E;
// }
// .skiptranslate.goog-te-gadget {
//   font-size: 0;
// }
.flag-dropdown .dropdown-menu li {
  white-space: nowrap;
  display: flex;
  gap: 5px;
  cursor: pointer;
  align-items: center;
  a{
    padding: 5px 10px;
  }
}
.flag-dropdown .dropdown-menu li:hover {
  background: #eee;
}
.flag-dropdown  .dropdown-menu {
  left: auto !important;
  right: 0 !important;
}
.flag-dropdown button {
  background: transparent !important;
  color: #000 !important;
  border: none;
}
.flag-dropdown button span 
{
  margin-right: 5px;
}
.flag-dropdown
{
  display: inline-block;
}
.table-btn {
  min-width: 65px;
  padding: 0px 6px 3px;
  font-size: 14px;
}

.mouldlive-logo 
{
  position: relative;
  display: inline-block;
  text-align: center;
  .logo-text
  {
    position: relative;
    display: inline-block;
    padding-top: 15px;
  }

  h3 {
    font-weight: bold;
    color: #0990d2;
    font-size: 30px;
    margin-bottom: 0  ;
    @media screen and (max-width:1023px)
    {
      font-size: 22px;
    }
  }
  .right-logo {
    position: absolute;
    display: flex;
    align-items: center;
    top: 0;
    right: 0;
    gap: 5px;
    font-size: 10px;
    color: #001d3e;
    img 
    {
      max-width: 52px;
      margin-top: -3px;

    }
  }
}
app-sidebar 
{
  transition: all 0.3s ease-in-out 0s;
}
.header-logo  
{
  gap: 15px;

  .mouldlive-logo 
  {
    display: none;

  }

  @media screen and (max-width:767px)
  {
    gap: 8px;
  }
}


.side-bar-active 
{
  .app-sidebar
  {
    margin-left: -100%;

    @media screen and (max-width: 1023px)
    {
      margin-left: 0 !important; 
    }
  }
  .mouldlive-logo
  {
    display: inline-block !important;
  }
  .header-logo 
  {
    .mouldlive-logo
    {

        @media screen and (max-width: 767px)
        {
          display: none !important;
        }
    }
  }
  .page-body
  {
    margin-left: 0 !important;
  }
  .header-logo h3 
  {
    font-size: 22px;
  }
}



.side-bar-remove 
{
  .header-logo 
  {
    .mouldlive-logo
    {
        @media screen and (max-width: 1023px)
        {
          display: inlineblock !important;

        }

        @media screen and (max-width: 767px)
        {
          display: block !important;

          h3 
          {
            font-size: 17px;
          }
        }
    }
  }
  
}


.temprature {
  .temprature-inner
  {
    @media screen and (max-width:500px)
    {
      app-controls-small-card 
      {
        width: 100%;
        .small-card
        {
          width: 100% !important;
        }
      }
    }
  }
}

.user-dropdown-icon{
  width: 24px;
}

.support-icon{
  
  margin-top: 6px;
  cursor: pointer;
}

.footer-inner {
    text-align: center;
    padding: 10px 0px;
    color: #222;
    font-size: 14px;
    border-top: 1px solid #ddd;
    position: fixed;
    width: calc(100% - 250px);
    bottom: 0;
    background: white;
}

.side-bar-active{
  .footer-inner{
    width: 100%;
  }
}
@media screen and (max-width:1023px)
{
  .footer-inner
  {
    width: 100%;
  }
}

.qr-scanner-modal .modal-dialog {
  max-width: 500px;
  height: 100%;
    display: flex;
    align-items: center;
    margin-top: 0;
}
.qr-scanner-png {
  width: 180px;
  opacity: 0.2;
  margin-bottom: 30px;
}
.modal-cross
{
  position: absolute;
  right: 15px;
  font-size: 12px;
  top: 15px;
}
.qr-scanner-title {
  display: flex;
  justify-content: center;
}
.btn-qr-scan-primary:hover {
  background: #0990d2;
  color: #fff;
}

.btn-qr-scan-primary {
  background: #0990d2;
  color: white;
  border-radius: 30px;
}

.header-border.b-status-1+.header-content .group-edit i {
  color: #fff;
}

.rig-button{
  width: max-content;
}


.mould-disabled{
  pointer-events:none; 
  opacity: 0.6;
}


.enable-disable {
  white-space: nowrap;
  background: transparent;
  position: relative;
  padding: 0 15px 0 30px;
  height: 30px;
  display: flex;
  align-items: center;
  &::before{
    width: 12px;
    height: 12px;
    border-radius: 100%;
    content: '';
    background-color: lightgray ;
    position: absolute;
    top: 10px;
    left: 12px;
  }
  &.active
  {
    // color: $green;
    &::before
    {
      background-color: $green;
    }
  }
}
