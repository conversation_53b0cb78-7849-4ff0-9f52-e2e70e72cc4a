.app-button-base {
  color: $white;
  border-radius: 2px;
  font-size: 12px;
  line-height: 16px;
  height: 32px;
  padding: 0 20px;
  text-align: center;
  outline: none;
  border: none;
  &:focus {
    outline: none;
  }
}

.app-button {
  &-primary {
    @extend .app-button-base;
    @include colors($white, $primary);
  }

  &-outline {
    @extend .app-button-base;
    @include colors($primary, $white);
    border: solid 1px $primary;
  }
}

.app-button-transparent {
  background: transparent;
  outline: none;
  border: none;
  &:focus {
    outline: none;
  }
}
