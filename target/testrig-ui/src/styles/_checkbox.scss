// @import '~pretty-checkbox/src/pretty-checkbox.scss';
.app-checkbox {
  input[type='checkbox'] {
    display: none;
  } /* to hide the checkbox itself */
  input[type='checkbox'] + label:before {
    font-family: 'Font Awesome 5 Pro';
    display: inline-block;
  }

  input[type='checkbox'] + label:before {
    content: '\f0c8';
    font-weight: 200;
  } /* unchecked icon */
  input[type='checkbox'] + label:before {
    letter-spacing: 10px;
  } /* space between checkbox and label */

  input[type='checkbox']:checked + label:before {
    content: '\f14a';
    font-weight: 900;
  } /* checked icon */
  input[type='checkbox']:checked + label:before {
    letter-spacing: 5px;
  } /* allow space for check mark */

  &.part {
    input[type='checkbox']:checked + label:before {
      content: '\f146';
    }
  }

  label {
    margin-bottom: 0;
  }
}
