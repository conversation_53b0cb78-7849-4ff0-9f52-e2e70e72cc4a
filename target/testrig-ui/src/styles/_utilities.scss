// Colors
.c-white {
  color: $white;
}
.c-black {
  color: $black;
}
.c-primary {
  color: $primary;
}
.c-blue {
  color: $blue;
}
.c-green {
  color: $green;
}
.c-red {
  color: $red;
}
.c-grey {
  color: $grey;
}

// Font
.f-14 {
  font-size: 14px;
}
.f-16 {
  font-size: 16px;
}

// Status
.b-status-0 {
  background: $green;
}
.b-status--1 {
  background: $medium-grey;
}
.b-status-1 {
  background: $yellow;
}
.b-status-2 {
  background: $red;
}
.b-dark-grey {
  background: $dark-grey;
}

.app-wrapper {
  background-color: $app-background;
  border-top: none; 
  padding: 24px 30px; 
  overflow-x: hidden;
  overflow: auto;
  @media screen and (max-width:767px)
  {
    padding: 24px 15px;
    height: auto;
  }
}
