.app-select {
  min-width: 235px;
  position: relative;
  overflow: hidden;
  display: inline-flex;
  font-size: 14px;

  select {
    line-height: 16px;
    height: 32px;
    padding: 8px 12px;
    border-radius: 30px;
    border: 1px solid #ccc;
    background-color: $white;
    width: 100%;
    appearance: none;

    &:focus {
      outline: 0;
    }
    &:disabled {
      border-color: $grey;
    }
  }
  &:after {
    content: '\f0d7';
    font-family: 'Font Awesome 5 Pro';
    color: $dark-grey;
    font-weight: bold;
    padding: 12px 8px;
    position: absolute;
    right: 12px;
    top: -6px;
    z-index: 1;
    width: 10%;
    height: 100%;
    pointer-events: none;
  }
  &.disabled {
    &:after {
      color: $grey;
    }
  }
}
