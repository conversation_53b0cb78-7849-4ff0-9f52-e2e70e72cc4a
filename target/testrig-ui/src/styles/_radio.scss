.app-radio {
  display: inline-flex;
  margin-right: 12px;

  label {
    margin: 0;
  }

  input[type='radio'] {
    border: 0;
    clip: rect(0 0 0 0);
    height: 1px;
    margin: -1px;
    overflow: hidden;
    padding: 0;
    position: absolute;
    width: 1px;
    color: $primary;
  }

  input[type='radio'] + label:before {
    font-family: 'Font Awesome 5 Pro';
    content: '\f111';
    font-size: 20px;
    letter-spacing: 8px;
    top: 2px;
    position: relative;
  }
  input[type='radio']:checked + label:before {
    content: '\f192';
    font-size: 20px;
    font-family: 'Font Awesome 5 Pro';
    font-weight: 400;
  }

  &.disabled {
    color: $grey;
    input[type='radio'] {
      pointer-events: none;
    }
  }
}
