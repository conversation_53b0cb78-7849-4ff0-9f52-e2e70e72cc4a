# -*- Makefile -*-
#
# Common docker app Makefile
#
# Make targets available:
#   all                  - build docker images
#   tags              - tag images
#   push-development          - push images to development repository
#   push-production          - push images to production repository
#   manifest-development      - make manifest for development
#   manifest-production          - make manifest for production
#   push-manifest-development - push manifest to development repository
#   push-manifest-production  - push manifest to production repository
#   development               - all of the above for development
#   production                - all of the above for production
#
# Flow is to build, tag and push images. Next build and push manifest.
#
# Manifests are still and experimental feature of docker and needs to be enabled:
# https://github.com/docker/docker-ce/blob/master/components/cli/experimental/README.md
#

.PHONY: all tags-arm tags-x86_64 manifest-ssrv tags push-x86_64-ssrv manifest

REGISTRY_PREFIX ?= eu.gcr.io/mouldlive
PROD_TAGS_X86 = $(addsuffix /${MODULE}:${VER}, $(REGISTRY_PREFIX))
PROD_TAGS_ARM = $(addsuffix /arm32v7/${MODULE}:${VER}, $(REGISTRY_PREFIX))
PROD_MANIFEST = ${REGISTRY_PREFIX}/${MODULE}:${VER}

VER ?= latest
REPOS_VER ?= latest
REPOS_X86_RUNTIME ?= $(REPOS_X86)
REPOS_ARM_RUNTIME ?= $(REPOS_ARM)
IMAGE_NAME ?= $(MODULE)

all: ${IMAGE_NAME} arm32v7/${IMAGE_NAME}

arm32v7/${IMAGE_NAME}: qemu-arm-static deps
ifdef REPOS_ARM
	docker build . \
		--build-arg MODULE=$(MODULE) \
		--build-arg REPOS_RUNTIME=$(REPOS_ARM_RUNTIME) \
		--build-arg REPOS_VER=${REPOS_VER} \
		--build-arg REPOS=${REPOS_ARM} \
		--build-arg ARCH=arm \
		-t $(REGISTRY_PREFIX)/prod/${IMAGE_NAME}:${VER} -f ${DOCKER_FILE} \
		-t $(REGISTRY_PREFIX)/dev/${IMAGE_NAME}:${VER} -f ${DOCKER_FILE}
endif

${IMAGE_NAME}: qemu-arm-static deps
ifdef REPOS_X86
	docker build . \
		--build-arg MODULE=$(MODULE) \
		--build-arg REPOS_RUNTIME=$(REPOS_X86_RUNTIME) \
		--build-arg REPOS_VER=${REPOS_VER} \
		--build-arg REPOS=${REPOS_X86} \
		--build-arg ARCH=x86_64 \
		-t $(REGISTRY_PREFIX)/${IMAGE_NAME}:${VER} -f ${DOCKER_FILE}
endif

qemu-arm-static:
ifdef REPOS_ARM
	[ -f /usr/bin/qemu-arm-static ] && cp /usr/bin/qemu-arm-static .
endif

manifest-production: push-production
	rm -rf ~/.docker/manifests/${REGISTRY_PREFIX}*
	docker manifest create --amend ${PROD_MANIFEST} $(PROD_TAGS_ARM) $(PROD_TAGS_X86)
	docker manifest annotate --os linux --arch amd64 ${PROD_MANIFEST} $(PROD_TAGS_X86)
	docker manifest annotate --os linux --arch arm --variant v7 ${PROD_MANIFEST} $(PROD_TAGS_ARM)

push-manifest-production: manifest-production
	docker manifest push --purge ${PROD_MANIFEST}

production: push-production manifest-production push-manifest-production verify-production

docker-clean:
	- [ ! -z "$$(docker ps -a -q)" ] && docker rm -f $$(docker ps -a -q)
	- [ ! -z "$$(docker images | grep none | awk '{print $$3}')" ] && \
		docker rmi -f $$(docker images | grep none | awk '{print $$3}')

clean:
	- docker rmi ${REGISTRY_PREFIX}/${MODULE}:${VER}

gcloud-login-registry:
	gcloud auth print-access-token | docker login -u oauth2accesstoken --password-stdin https://eu.gcr.io

push-arm-production: arm32v7/${IMAGE_NAME}
ifdef REPOS_ARM
	docker push ${REGISTRY_PREFIX}/prod/${MODULE}:${VER}
endif

push-x86_64-production: ${IMAGE_NAME}
ifdef REPOS_X86
	docker push ${REGISTRY_PREFIX}/prod/${MODULE}:${VER}
endif

push-arm-development: arm32v7/${IMAGE_NAME}
ifdef REPOS_ARM
	docker push ${REGISTRY_PREFIX}/dev/${MODULE}:${VER}
endif

push-x86_64-development: ${IMAGE_NAME}
ifdef REPOS_X86
	docker push ${REGISTRY_PREFIX}/dev/${MODULE}:${VER}
endif

push-production: push-x86_64-production push-arm-production

push-development: push-x86_64-development push-arm-development

verify-production:
	docker inspect ${REGISTRY_PREFIX}/${MODULE}:arm-${VER} | jq '.[0] | "\(.RepoDigests[0])"' -c -M | cut -d':' -f3 | sed 's/"//'
	docker inspect ${REGISTRY_PREFIX}/${MODULE}:x86_64-${VER} | jq '.[0] | "\(.RepoDigests[0])"' -c -M | cut -d':' -f3 | sed 's/"//'
	docker manifest inspect ${REGISTRY_PREFIX}/${MODULE}

test-x86:
	docker manifest inspect ${SSRV_MANIFEST}
	docker run --rm -it --entrypoint '/bin/sh' ${SSRV_ENV}/${MODULE}

test-arm:
	docker manifest inspect ${SSRV_MANIFEST}
	docker run --rm -it --entrypoint '/bin/sh' ${SSRV_ENV}/${MODULE}:arm-${VER}

register:
	docker run --rm --privileged multiarch/qemu-user-static:register

help:
	@echo ""
	@echo "  all                  - build docker images"
	@echo "  tags              - tag images"
	@echo "  push-development          - push images to development repository"
	@echo "  push-production          - push images to production repository"
	@echo "  manifest-development      - make manifest for development"
	@echo "  manifest-production          - make manifest for production"
	@echo "  push-manifest-development - push manifest to development repository"
	@echo "  push-manifest-production  - push manifest to production repository"
	@echo "  development               - all of the above for development"
	@echo "  production                - all of the above for production"
	@echo ""
	@echo "Build Flow is ..."
	@echo " 1. Build, tag and push images."
	@echo " 2  Build and push manifest."
	@echo ""
	@echo "Manifests are still and experimental feature of docker and needs to be enabled:"
	@echo "https://github.com/docker/docker-ce/blob/master/components/cli/experimental/README.md"
	@echo ""
