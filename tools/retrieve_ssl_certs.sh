#!/usr/bin/env bash
#
# Generate a certs-authorized_keys file for MQTT broker
#

KEYFILE=authorized_certs
TFTP_BASE="ftp://<EMAIL>/Development/mouldflo/production"

get_keys() {
    local URL=$1
    echo -e "\nKeys from ${URL})"
    for DIR in $(curl -s --list-only ${URL}); do
        FILE=$(curl -s --list-only ${URL}/${DIR}/ | grep cert)

        if [ -z "$FILE" ] ; then
            echo -e "\e[31mNo certificates in :  ${URL}${DIR}/\e[0m"
            continue
        fi

        curl -s ${URL}/${DIR}/$FILE >> tmp.crt
        res=$(openssl x509 -in tmp.crt -subject -noout)
        echo -e "\e[32m${res}\e[0m"
        if [ $? -ne 0 ] ; then
            echo "Error : $FILE"
            cp tmp.crt err.crt
        fi
        cat tmp.crt >> $KEYFILE
        rm tmp.crt
    done
}

rm -f ${KEYFILE}
get_keys "${TFTP_BASE}/devices/"
get_keys "${TFTP_BASE}/cloud/"

echo -e "\n INFO: Wrote certificates in : $KEYFILE"
