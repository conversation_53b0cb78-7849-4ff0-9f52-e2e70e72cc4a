#/usr/bin/env bash


# V1
# Ripped off from http://www.steves-internet-guide.com/mosquitto-tls

# v2
# https://medium.com/@sevcsik/authentication-using-https-client-certificates-3c9d270e8326
#

#
# https://www.digitalocean.com/community/tutorials/openssl-essentials-working-with-ssl-certificates-private-keys-and-csrs
#
REALPATH=$(realpath $0)
DIRPATH=$(dirname $REALPATH)
API_BACKEND=localhost

TFTP_BASE="ftp://<EMAIL>/Development/mouldflo/production"
BROKER_PATH=${DIRPATH}/../cloud/broker/certs
SERVER_CERT=${BROKER_PATH}/broker_cert.pem
SERVER_KEY=${BROKER_PATH}/broker_key.pem
BASE_SERVER='no'
TFTP_COPY='no'
MAN_DEVICE='none'
HW_VER='none'
DEVICE_ID='none'

usage() {
    echo "Usage: $0 [-m <manufacturer-device>] [-s <serial_number>] [-c <cert>] [-k <key>] [-t][-b]"
    echo "Example Mouldflo Mfio: $0 -m 401 -i 4 -v 1"
    exit 1;
}

while getopts ":n:c:k:b:t:m:v:i:" opt; do
    case $opt in
        n)
            SERNO=$OPTARG
            ;;
        c)
            SERVER_CERT=$OPTARG
            ;;
        k)
            SERVER_KEY=$OPTARG
            ;;
        b)
            BASE_SERVER=$OPTARG
            ;;
        t)
            TFTP_COPY='yes'
            ;;
        m)
            MAN_DEVICE=$OPTARG
            ;;
        v)
            HW_VER=$OPTARG
            ;;
        i)
            DEVICE_ID=$OPTARG
            ;;
        *)
            usage
            ;;
    esac
done

if [ "$BASE_SERVER" != 'no' ] ; then
    exit
    SERNO=$BASE_SERVER
    openssl req -x509 -newkey rsa:2048 \
        -keyout ${SERNO}_key.pem \
        -out ${SERNO}_cert.pem \
        -nodes -days 365 -subj "/CN=$SERNO"
    cp -v ${SERNO}_cert.pem ${BROKER_PATH}/broker_cert.pem
    cp -v ${SERNO}_key.pem ${BROKER_PATH}/broker_key.pem

    if [ TFTP_COPY = 'yes' ] ; then
        URL="${TFTP_BASE}/cloud/broker/"
        curl -sT ${BROKER_PATH}/broker_cert.pem ${URL}
        curl -sT ${BROKER_PATH}/broker_csr.pem ${URL}
        curl -sT ${BROKER_PATH}/broker_key.pem ${URL}
    fi
    exit 0
fi

if [ $MAN_DEVICE = 'none' ] || [ $HW_VER = 'none' ] || [ $DEVICE_ID = 'none' ]; then
    usage
    exit 0
fi

SERNO=$(printf "%03d_%02d_%06d" ${MAN_DEVICE} ${HW_VER} ${DEVICE_ID})
echo $SERNO

DAYS=$((365*30))

# Client CA certificates
openssl req \
        -newkey rsa:2048 -nodes -keyout ${SERNO}_key.pem \
        -days ${DAYS} -subj "/CN=$SERNO" \
        -out ${SERNO}_csr.pem

echo "Signing with $SERVER_CERT"
echo "Signing with $SERVER_KEY"

SSL_SERIAL=$(printf "0x%X\n" $((401<<24|4)))

# We sign CSR with our key and save it as a certificate.
# Here, we act as a Certificate Authority,
# so we supply our certificate and key via the -CA parameters:
openssl x509 -req -in ${SERNO}_csr.pem \
        -CA ${SERVER_CERT} \
        -CAkey ${SERVER_KEY} \
        -out ${SERNO}_cert.pem \
        -set_serial ${SSL_SERIAL} -days 365

if [ TFTP_COPY = 'yes' ] ; then
    URL="${TFTP_BASE}/devices/${SERNO}/"
    curl -s --ftp-create-dirs ${URL}
    curl -sT ${SERNO}_cert.pem ${URL}
    curl -sT ${SERNO}_csr.pem ${URL}
    curl -sT ${SERNO}_key.pem ${URL}

    echo "Copied files to $URL"
fi

TOKEN=$(curl -k -s -H "Content-Type: application/json" \
    --cacert api/certs/api_cert.pem \
    -d '{"username":"admin","password":"03izdeC0oLizT!"}' https://${API_BACKEND}/login | jq -r '.access_token')

DATA=$(python3 convert_key_to_json.py ${SERNO})
res=$(curl -s -k  --cacert ${DIRPATH}/../cloud/api/certs/api_cert.pem \
    -H "Authorization: Bearer $TOKEN" \
    -H "Content-Type: application/json; charset=UTF-8" \
    -d "${DATA}" \
    https://${API_BACKEND}/certificates)

echo https://${API_BACKEND}/certificates
echo $res
