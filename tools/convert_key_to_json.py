#!/usr/bin/python3
import sys
import time
import json

if len(sys.argv) == 1:
    print("usage:\npython3 {} <serial-number>".format(sys.argv[0]))
    exit(0)

name = sys.argv[1]
obj = { "serno": name, "key": [], "cert": [], "csr": [], "timestamp": int(time.time())}

for i in ("key", "cert", "csr"):
    file = open("{}_{}.pem".format(name, i),"r")
    for line in file:
        obj[i].append(line.replace("\n", ''))

print(json.dumps(obj))
