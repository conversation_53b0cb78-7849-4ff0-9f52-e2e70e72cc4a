#!/usr/bin/env bash

CERTS_FILE="none"
VM=movecms-vm

usage() {
    echo "Usage: $0 [-c <broker certificates file>] [-v <vm>]" 1>&2;
    exit 1;
}

while getopts ":c:v:" opt; do
    case $opt in
        c)
            CERTS_FILE=$OPTARG
            ;;
        v)
            VM=$OPTARG
            ;;
        h)
            usage
            ;;
        *)
            usage
            ;;
    esac
done

if [ ${CERTS_FILE} = 'none' ] ; then
    echo "Please enter file of certificates"
    exit 1
fi

if ! ssh ${VM} python --version > /dev/null 2>&1 ; then
    ssh ${VM} sudo apt update
    ssh ${VM} sudo apt install python python-pip
    ssh ${VM} sudo pip install docker-compose
fi

CURRENT_UID=$(ssh ${VM} id -u):$(ssh ${VM} id -g)
WORKING_DIR=$(ssh ${VM} pwd)

ssh ${VM} "echo CURRENT_UID=$CURRENT_UID > .env"
ssh ${VM} "echo WORKING_DIR=$WORKING_DIR >> .env"

if ! ssh ${VM} ls db > /dev/null 2>&1 ; then
    ssh ${VM} mkdir -p db/data
fi

if ! ssh ${VM} ls grafana > /dev/null 2>&1 ; then
    ssh ${VM} mkdir -p grafana
    scp -r grafana/ ${VM}:
    ssh ${VM} rm grafana/dashboards/*.json
fi


if ! ssh ${VM} ls broker/certs > /dev/null 2>&1 ; then
    ssh ${VM} mkdir -p broker/certs
    ssh ${VM} mkdir -p broker/data
fi

scp $CERTS_FILE ${VM}:broker/certs

export CURRENT_UID=$CURRENT_UID
export WORKING_DIR=$WORKING_DIR

setup/rdocker ${VM}

CURRENT_UID=$CURRENT_UID WORKING_DIR=$WORKING_DIR docker-compose build
CURRENT_UID=$CURRENT_UID WORKING_DIR=$WORKING_DIR docker-compose up
